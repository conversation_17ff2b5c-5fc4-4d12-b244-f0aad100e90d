package dataAll.body.define
{
   import com.sounto.oldUtils.Sounto64;
   import com.sounto.utils.ClassProperty;
   
   public class BodyMoreDefine
   {
      public static var pro_arr:Array = [];
      
      public var dpsMul:Number = 1;
      
      public var underHurtMul:Number = 1;
      
      public var arenaUnderHurtMul:Number = 1;
      
      public var studySkillLvAdd:Number = 0;
      
      private var _firstLv:String = "";
      
      public function BodyMoreDefine()
      {
         super();
         this.firstLv = 1;
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function set firstLv(v0:Number) : void
      {
         this._firstLv = Sounto64.encode(String(v0));
      }
      
      public function get firstLv() : Number
      {
         return Number(Sounto64.decode(this._firstLv));
      }
   }
}

