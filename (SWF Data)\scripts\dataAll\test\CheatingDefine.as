package dataAll.test
{
   import com.sounto.utils.ClassProperty;
   
   public class CheatingDefine
   {
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var father:String = "";
      
      public var input:String = "";
      
      public var inputTitle:String = "";
      
      public var string:String = "";
      
      public var tip:String = "";
      
      public var localB:Boolean = false;
      
      public function CheatingDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML, father0:String) : void
      {
         this.father = father0;
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function getInputTitle() : String
      {
         if(this.inputTitle != "")
         {
            return this.inputTitle;
         }
         if(this.input == "num")
         {
            return "请输入数值";
         }
         if(this.input == "str")
         {
            return "请输入文本";
         }
         return "";
      }
   }
}

