package dataAll._app.top.extra
{
   import com.sounto.utils.ClassProperty;
   
   public class ArenaTopExtra extends DpsTopExtra
   {
      public static var pro_arr:Array = [];
      
      public var skill:String = "";
      
      public var uname:String = "";
      
      public function ArenaTopExtra()
      {
         super();
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      override public function setToSimulated() : void
      {
         super.setToSimulated();
         this.skill = Gaming.defineGroup.skill.getRandomHeroSkillNameArr(int(6 + 3 * Math.random())).toString();
      }
   }
}

