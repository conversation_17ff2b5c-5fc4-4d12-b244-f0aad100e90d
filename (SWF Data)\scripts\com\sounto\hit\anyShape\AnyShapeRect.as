package com.sounto.hit.anyShape
{
   import com.sounto.motion.Motion_DOFData;
   import flash.geom.Rectangle;
   
   public class AnyShapeRect extends Rectangle
   {
      public var maxBack:int = 10;
      
      public var minBack:int = 0;
      
      public var inValue:int = 3;
      
      public var stepMinHigh:int = 25;
      
      public var n1:AnyShapeNode = new AnyShapeNode();
      
      public var n2:AnyShapeNode = new AnyShapeNode();
      
      public var n3:AnyShapeNode = new AnyShapeNode();
      
      public var n4:AnyShapeNode = new AnyShapeNode();
      
      public var arr:Array = [this.n1,this.n2,this.n3,this.n4];
      
      public var n5:AnyShapeNode = new AnyShapeNode();
      
      public function AnyShapeRect(x0:int = 0, y0:int = 0, w0:int = 0, h0:int = 0)
      {
         super(x0,y0,w0,h0);
      }
      
      public function flesh() : void
      {
         this.n1.x = x;
         this.n1.y = y;
         this.n2.x = x + width;
         this.n2.y = y;
         this.n3.x = x;
         this.n3.y = y + height;
         this.n4.x = x + width;
         this.n4.y = y + height;
         this.n5.x = x + width / 2;
         this.n5.y = y + height;
      }
      
      public function inData(rect0:Rectangle) : void
      {
         x = rect0.x;
         y = rect0.y;
         width = rect0.width;
         height = rect0.height;
      }
      
      public function haveAreaB() : Boolean
      {
         return width > 4 && height > 4;
      }
      
      private function getTan(x0:Number, y0:Number, x1:Number, y1:Number) : Number
      {
         var tan0:Number = 0;
         if(x0 == x1)
         {
            if(y0 < y1)
            {
               tan0 = 10000000000;
            }
            else if(y0 > y1)
            {
               tan0 = -10000000000;
            }
         }
         else
         {
            tan0 = (y1 - y0) / (x1 - x0);
         }
         return tan0;
      }
      
      public function setDof2(dof0:Motion_DOFData) : void
      {
         var tan_n3_ra:Number = NaN;
         var tan_n4_ra:Number = NaN;
         var big_n3_raB:Boolean = false;
         var big_n4_raB:Boolean = false;
         var c234:int = 0;
         var isSlopeB:Boolean = false;
         var slopeBackMul:Number = NaN;
         var mul001:Number = NaN;
         var PI:Number = Math.PI;
         var downRa0:Number = Math.atan2(this.n4.maxY - this.n3.maxY,this.n4.x - this.n3.x);
         var upRa0:Number = Math.atan2(this.n2.minY - this.n1.minY,this.n2.x - this.n1.x);
         var abs_downRa0:Number = Math.abs(downRa0);
         var abs_upRa0:Number = Math.abs(upRa0);
         var slopeGoB:Boolean = abs_downRa0 < PI / 3.3;
         var n1_hitB:Boolean = this.n1.hitB;
         var n2_hitB:Boolean = this.n2.hitB;
         var n3_hitB:Boolean = this.n3.hitB;
         var n4_hitB:Boolean = this.n4.hitB;
         var hitNum0:int = 0;
         if(n1_hitB)
         {
            hitNum0++;
         }
         if(n2_hitB)
         {
            hitNum0++;
         }
         if(n3_hitB)
         {
            hitNum0++;
         }
         if(n4_hitB)
         {
            hitNum0++;
         }
         var back00:int = 0;
         var back02:int = 0;
         var slopeY_choose:int = 5;
         var minSlopeY_choose:Boolean = true;
         if(hitNum0 == 0)
         {
            if(this.n2.down > this.n2.y + this.inValue && this.n2.down < this.n4.y - this.inValue || this.n4.up > this.n2.y + this.inValue && this.n4.up < this.n4.y - this.inValue)
            {
               dof0.right = 1;
            }
            if(this.n1.down > this.n1.y + this.inValue && this.n1.down < this.n3.y - this.inValue || this.n3.up > this.n1.y + this.inValue && this.n3.up < this.n3.y - this.inValue)
            {
               dof0.left = 1;
            }
            slopeY_choose = 0;
         }
         else if(hitNum0 == 2 || hitNum0 == 1)
         {
            back00 = 0;
            if(n1_hitB && n3_hitB)
            {
               dof0.left = 1;
               dof0.left_back = this.n3.right - this.n3.x;
               if(this.n1.right < this.n3.right)
               {
                  dof0.left_back = this.n1.right - this.n3.x;
               }
               dof0.left_back -= 2;
               if(abs_downRa0 < PI / 3)
               {
                  dof0.down = 1;
               }
               else
               {
                  slopeY_choose = 1;
               }
               minSlopeY_choose = false;
            }
            else if(n2_hitB && n4_hitB)
            {
               dof0.right = 1;
               dof0.right_back = this.n4.x - this.n4.left;
               if(this.n2.left > this.n4.left)
               {
                  dof0.right_back = this.n4.x - this.n2.left;
               }
               dof0.right_back -= 2;
               if(abs_downRa0 < PI / 3)
               {
                  dof0.down = 1;
               }
               else
               {
                  slopeY_choose = 1;
               }
               minSlopeY_choose = false;
            }
            else if(!n3_hitB && !n4_hitB)
            {
               dof0.up = 1;
               if(this.n1.minY > this.n2.minY)
               {
                  dof0.up_back = this.n1.minY - this.n1.y - 5;
               }
               else
               {
                  dof0.up_back = this.n2.minY - this.n2.y - 5;
               }
               if(upRa0 > PI / 3)
               {
                  dof0.right_back = (this.n2.x - this.n2.left) * (upRa0 - PI / 3) / upRa0 - 2;
                  dof0.right = 1;
               }
               else if(upRa0 < -PI / 3)
               {
                  dof0.left_back = (this.n1.right - this.n1.x) * (upRa0 + PI / 3) / upRa0 - 2;
                  dof0.left = 1;
               }
            }
            else
            {
               tan_n3_ra = Math.abs(this.getTan(this.n3.x,this.n3.maxY,this.n3.right,this.n3.y));
               tan_n4_ra = Math.abs(this.getTan(this.n4.x,this.n4.maxY,this.n4.left,this.n4.y));
               big_n3_raB = tan_n3_ra >= Math.tan(PI / 4);
               big_n4_raB = tan_n4_ra >= Math.tan(PI / 4);
               c234 = Math.abs(Math.abs(this.n5.maxY - this.n3.maxY) - Math.abs(this.n4.maxY - this.n5.maxY));
               isSlopeB = c234 < this.stepMinHigh;
               slopeBackMul = (this.stepMinHigh - c234) / this.stepMinHigh;
               if(slopeBackMul > 1)
               {
                  slopeBackMul = 1;
               }
               if(isSlopeB)
               {
                  if(slopeGoB)
                  {
                     dof0.down = 1;
                     dof0.down_back = this.n5.y - this.n5.maxY;
                  }
                  else if(downRa0 > 0)
                  {
                     dof0.left = 1;
                     dof0.left_back = (this.n5.minX - this.n5.x) * slopeBackMul;
                  }
                  else
                  {
                     dof0.right = 1;
                     dof0.right_back = (this.n5.x - this.n5.maxX) * slopeBackMul;
                  }
                  if(hitNum0 == 2)
                  {
                     dof0.down = 1;
                  }
               }
               else if(hitNum0 == 1)
               {
                  mul001 = 1;
                  mul001 = (this.n5.maxY - this.n5.y) / this.stepMinHigh;
                  if(n3_hitB)
                  {
                     if(mul001 > 1)
                     {
                        mul001 = 1;
                     }
                     if(!big_n3_raB)
                     {
                        dof0.down = 1;
                        dof0.down_back = this.n3.y - this.n3.maxY;
                        slopeY_choose = 0;
                     }
                     else
                     {
                        dof0.left = 1;
                        dof0.left_back = (this.n3.minX - this.n3.x) * mul001;
                        slopeY_choose = 1;
                     }
                  }
                  else if(!big_n4_raB)
                  {
                     dof0.down = 1;
                     dof0.down_back = this.n3.y - this.n3.maxY;
                     slopeY_choose = 0;
                  }
                  else
                  {
                     dof0.right = 1;
                     dof0.right_back = (this.n4.x - this.n4.maxX) * mul001;
                     slopeY_choose = 1;
                  }
               }
               else
               {
                  dof0.down = 1;
                  if(downRa0 > 0)
                  {
                     dof0.down_back = this.n4.y - this.n4.maxY;
                     dof0.left = 1;
                  }
                  else
                  {
                     dof0.down_back = this.n3.y - this.n3.maxY;
                     dof0.right = 1;
                  }
                  slopeY_choose = 1;
               }
               dof0.down_back -= 2;
               dof0.left_back -= 2;
               dof0.right_back -= 2;
            }
         }
         else if(hitNum0 == 3)
         {
            back00 = 0;
            if(!n1_hitB || !n2_hitB)
            {
               if(n1_hitB)
               {
                  dof0.left = 1;
                  back02 = this.n1.right - this.n1.x - width / 3;
                  if(back02 > 0)
                  {
                     dof0.left_back = back02;
                  }
               }
               else
               {
                  dof0.right = 1;
                  back02 = this.n2.x - this.n2.left - width / 3;
                  if(back02 > 0)
                  {
                     dof0.right_back = back02;
                  }
               }
               if(abs_upRa0 < PI / 3)
               {
                  dof0.up = 1;
               }
               else
               {
                  minSlopeY_choose = false;
               }
               dof0.down = 1;
               if(this.n4.maxY > this.n3.maxY)
               {
                  back00 = y + height - this.n4.maxY;
                  dof0.slopeY = this.n4.maxY;
               }
               else
               {
                  back00 = y + height - this.n3.maxY;
                  dof0.slopeY = this.n3.maxY;
               }
               dof0.down_back = back00 - 2;
               slopeY_choose = 1;
            }
            else
            {
               if(n3_hitB)
               {
                  dof0.left = 1;
               }
               else
               {
                  dof0.right = 1;
               }
               if(abs_downRa0 < PI / 3)
               {
                  dof0.down = 1;
               }
               dof0.up = 1;
               if(this.n1.minY < this.n2.minY)
               {
                  dof0.up_back = this.n1.minY - this.n1.y - 5;
               }
               else
               {
                  dof0.up_back = this.n2.minY - this.n2.y - 5;
               }
            }
         }
         else if(hitNum0 == 4)
         {
            dof0.up = 1;
            dof0.down = 1;
            dof0.right = 1;
            dof0.left = 1;
            if(x < 500)
            {
               dof0.left_back = 4;
            }
            else
            {
               dof0.right_back = 4;
            }
            if(y < 300)
            {
               dof0.up_back = 4;
            }
            else
            {
               dof0.down_back = 4;
            }
         }
         dof0.limitBack(this.maxBack);
         dof0.slopeAngle = downRa0;
         var cmaxmin:int = this.n3.maxY - this.n4.maxY;
         if(cmaxmin < 0)
         {
            if(slopeY_choose == 0)
            {
               dof0.slopeY = this.n3.maxY;
            }
            else if(slopeY_choose == 1)
            {
               dof0.slopeY = this.n4.maxY;
            }
            else if(slopeY_choose == 2)
            {
               dof0.slopeY = -cmaxmin / 3 + this.n3.maxY;
            }
         }
         else if(slopeY_choose == 0)
         {
            dof0.slopeY = this.n4.maxY;
         }
         else if(slopeY_choose == 1)
         {
            dof0.slopeY = this.n3.maxY;
         }
         else if(slopeY_choose == 2)
         {
            dof0.slopeY = cmaxmin / 3 + this.n4.maxY;
         }
         if(slopeY_choose == 5)
         {
            dof0.slopeY = this.n5.maxY;
         }
         if(this.n1.minY > this.n2.minY && minSlopeY_choose || this.n1.minY <= this.n2.minY && !minSlopeY_choose)
         {
            dof0.minSlopeY = this.n1.minY;
         }
         else
         {
            dof0.minSlopeY = this.n2.minY;
         }
      }
   }
}

