package dataAll._app.book
{
   import dataAll.ui.label.LabelAddData;
   
   public class BookFather
   {
      public static const arms:String = "arms";
      
      public static const arr:Array = [arms];
      
      public static const cnArr:Array = ["武器"];
      
      public function BookFather()
      {
         super();
      }
      
      public static function getLabelAddData() : LabelAddData
      {
         var n:* = undefined;
         var name1:String = null;
         var cn1:String = null;
         var fArr0:Array = null;
         var fCnArr0:Array = null;
         var da1:LabelAddData = null;
         var i:* = undefined;
         var name2:String = null;
         var cn2:String = null;
         var da2:LabelAddData = null;
         var gatherNameArr0:Array = arr;
         var gatherCnArr0:Array = cnArr;
         var fatherNameArr0:Array = [];
         var fatherCnArr0:Array = [];
         fatherNameArr0.push(["ultiArms","consArms","yagoldArms","darkgoldArms","yearArms","blackEvoArms","blackArms","redArms"]);
         fatherCnArr0.push(["究极武器","星座武器","太空武器","暗金武器","生肖武器","黑色进阶武器","黑色武器","稀有武器"]);
         var da0:LabelAddData = new LabelAddData();
         da0.inDataOne("helper","助手","HelperUI/bigLabel",0);
         for(n in gatherNameArr0)
         {
            name1 = gatherNameArr0[n];
            cn1 = gatherCnArr0[n];
            fArr0 = fatherNameArr0[n];
            fCnArr0 = fatherCnArr0[n];
            da1 = new LabelAddData();
            da1.inDataOne(name1,cn1,"HelperUI/midLabel",0);
            for(i in fArr0)
            {
               name2 = fArr0[i];
               cn2 = fCnArr0[i];
               da2 = new LabelAddData();
               da2.inDataOne(name2,cn2,"HelperUI/midLabel",i);
               da1.addChildData(da2);
            }
            da0.addChildData(da1);
         }
         return da0;
      }
   }
}

