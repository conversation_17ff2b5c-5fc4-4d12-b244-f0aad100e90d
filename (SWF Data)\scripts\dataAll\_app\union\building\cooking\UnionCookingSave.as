package dataAll._app.union.building.cooking
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.union.building.define.UnionCookingDefine;
   
   public class UnionCookingSave
   {
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var name:String = "";
      
      public var state:String = "no";
      
      public function UnionCookingSave()
      {
         super();
         this.surplusTime = 0;
      }
      
      public function get surplusTime() : Number
      {
         return this.CF.getAttribute("surplusTime");
      }
      
      public function set surplusTime(v0:Number) : void
      {
         this.CF.setAttribute("surplusTime",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function inDataByDefine(d0:UnionCookingDefine) : void
      {
         this.name = d0.name;
      }
   }
}

