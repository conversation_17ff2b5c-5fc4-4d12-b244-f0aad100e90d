package dataAll._data
{
   import com.sounto.cf.AscCF;
   import com.sounto.cf.Sounto64;
   import com.sounto.cf.SountoLocal64;
   import com.sounto.net.SWFLoaderUrl;
   import com.sounto.utils.StringMethod;
   import dataAll._app.login.LoginData4399;
   import dataAll._app.parts.define.PartsType;
   import dataAll._app.union.battle.UBattleAgent;
   import dataAll.equip.define.EquipPro;
   import dataAll.gift.define.GiftBase;
   import dataAll.ui.text.MixedTextLabel;
   
   public class StaticCtrl
   {
      private var musicObj:Object = {};
      
      public var musicNameArr:Array = [];
      
      private var urlObj:Object = {};
      
      public function StaticCtrl()
      {
         super();
         AscCF.staticInit();
         Sounto64.staticInit();
         SountoLocal64.staticInit();
         MixedTextLabel.staticInit();
         StringMethod.staticInit();
         EquipPro.staticInit();
         GiftBase.staticInit();
         PartsType.staticInit();
         UBattleAgent.staticInit();
         LoginData4399.staticInit();
         this.initUrl();
         this.initMusic();
      }
      
      private function initUrl() : void
      {
         this.addUrl("LargeSpaceshipMc","swf/scene/LargeSpaceshipMc.swf");
      }
      
      private function addUrl(label0:String, url0:String) : void
      {
         this.urlObj[label0] = url0;
      }
      
      public function getUrl(label0:String) : String
      {
         return this.urlObj[label0];
      }
      
      private function initMusic() : void
      {
         var s0:SWFLoaderUrl = null;
         s0 = new SWFLoaderUrl();
         s0.label = "mission";
         s0.cn = "使命必达";
         s0.url = "music_face/mission";
         this.addMusic(s0);
         s0 = new SWFLoaderUrl();
         s0.label = "main23";
         s0.cn = "新主题曲";
         s0.url = "music_face/main23";
         this.addMusic(s0);
         s0 = new SWFLoaderUrl();
         s0.label = "wind";
         s0.cn = "海风";
         s0.url = "music_face/wind";
         this.addMusic(s0);
         s0 = new SWFLoaderUrl();
         s0.label = "love";
         s0.cn = "回忆";
         s0.url = "music_face/love";
         this.addMusic(s0);
         s0 = new SWFLoaderUrl();
         s0.label = "sun";
         s0.cn = "骄阳";
         s0.url = "music_face/sun";
         this.addMusic(s0);
         s0 = new SWFLoaderUrl();
         s0.label = "first";
         s0.cn = "前路漫漫";
         s0.url = "music_face/first";
         this.addMusic(s0);
         s0 = new SWFLoaderUrl();
         s0.label = "descent";
         s0.cn = "暗袭";
         s0.url = "music_face/descent";
         this.addMusic(s0);
         s0 = new SWFLoaderUrl();
         s0.label = "Madboss";
         s0.cn = "战神降临";
         s0.url = "music_face/Madboss";
         this.addMusic(s0);
         s0 = new SWFLoaderUrl();
         s0.label = "piano";
         s0.cn = "轮回";
         s0.url = "music_face/piano";
         this.addMusic(s0);
         s0 = new SWFLoaderUrl();
         s0.label = "leave";
         s0.cn = "离别";
         s0.url = "music_face/leave";
         this.addMusic(s0);
         s0 = new SWFLoaderUrl();
         s0.label = "funny";
         s0.cn = "趣谈";
         s0.url = "music_face/funny";
         this.addMusic(s0);
         s0 = new SWFLoaderUrl();
         s0.label = "suspense";
         s0.cn = "谜团";
         s0.url = "music_face/suspense";
         this.addMusic(s0);
         s0 = new SWFLoaderUrl();
         s0.label = "lost";
         s0.cn = "失落边疆";
         s0.url = "music_main/s0";
         this.addMusic(s0);
      }
      
      private function addMusic(s0:SWFLoaderUrl) : void
      {
         this.musicObj[s0.label] = s0;
         this.musicNameArr.push(s0.label);
      }
      
      public function getMusic(name0:String) : SWFLoaderUrl
      {
         return this.musicObj[name0];
      }
   }
}

