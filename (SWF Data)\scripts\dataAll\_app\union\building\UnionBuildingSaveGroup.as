package dataAll._app.union.building
{
   import com.common.text.TextWay;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.union.building.cooking.UnionCookingSave;
   import dataAll._app.union.building.define.UnionBuildingDefine;
   import dataAll._app.union.building.define.UnionCookingDefine;
   import dataAll._app.union.building.define.UnionSendTaskDefine;
   import dataAll._app.union.building.federal.UnionSendTaskSave;
   import dataAll._app.union.building.geology.UnionGeologySave;
   
   public class UnionBuildingSaveGroup
   {
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var obj:Object = {};
      
      public var cookingObj:Object = {};
      
      public var sendTaskObj:Object = {};
      
      public var federalGiftB:Boolean = false;
      
      public var geology:UnionGeologySave = new UnionGeologySave();
      
      public function UnionBuildingSaveGroup()
      {
         super();
         this.eatNum = 0;
         this.federalState = 0;
         this.haveSendTaskGiftTime = 0;
         this.exchangeSuppliesNum = 0;
         this.buySuppliesNum = 0;
         this.federalTaskNum = 0;
         this.sendTaskNum = 0;
         this.daySuppliesNum = 0;
      }
      
      public function get haveSendTaskGiftTime() : Number
      {
         return this.CF.getAttribute("haveSendTaskGiftTime");
      }
      
      public function set haveSendTaskGiftTime(v0:Number) : void
      {
         this.CF.setAttribute("haveSendTaskGiftTime",v0);
      }
      
      public function get eatNum() : Number
      {
         return this.CF.getAttribute("eatNum");
      }
      
      public function set eatNum(v0:Number) : void
      {
         this.CF.setAttribute("eatNum",v0);
      }
      
      public function get federalState() : Number
      {
         return this.CF.getAttribute("federalState");
      }
      
      public function set federalState(v0:Number) : void
      {
         this.CF.setAttribute("federalState",v0);
      }
      
      public function get exchangeSuppliesNum() : Number
      {
         return this.CF.getAttribute("exchangeSuppliesNum");
      }
      
      public function set exchangeSuppliesNum(v0:Number) : void
      {
         this.CF.setAttribute("exchangeSuppliesNum",v0);
      }
      
      public function get buySuppliesNum() : Number
      {
         return this.CF.getAttribute("buySuppliesNum");
      }
      
      public function set buySuppliesNum(v0:Number) : void
      {
         this.CF.setAttribute("buySuppliesNum",v0);
      }
      
      public function get federalTaskNum() : Number
      {
         return this.CF.getAttribute("federalTaskNum");
      }
      
      public function set federalTaskNum(v0:Number) : void
      {
         this.CF.setAttribute("federalTaskNum",v0);
      }
      
      public function get sendTaskNum() : Number
      {
         return this.CF.getAttribute("sendTaskNum");
      }
      
      public function set sendTaskNum(v0:Number) : void
      {
         this.CF.setAttribute("sendTaskNum",v0);
      }
      
      public function get daySuppliesNum() : Number
      {
         return this.CF.getAttribute("daySuppliesNum");
      }
      
      public function set daySuppliesNum(v0:Number) : void
      {
         this.CF.setAttribute("daySuppliesNum",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.obj = ClassProperty.copySaveObj(obj0["obj"],UnionBuildingSave);
         this.sendTaskObj = ClassProperty.copySaveObj(obj0["sendTaskObj"],UnionSendTaskSave);
         this.cookingObj = ClassProperty.copySaveObj(obj0["cookingObj"],UnionCookingSave);
         if(!obj0.hasOwnProperty("geology"))
         {
            this.geology.fixedSave();
         }
      }
      
      public function initSave() : void
      {
         this.initObj();
         this.initCookingObj();
         this.initSendTaskObj();
         this.geology.fixedSave();
      }
      
      private function initObj() : void
      {
         var arr0:Array = null;
         var d0:UnionBuildingDefine = null;
         var s0:UnionBuildingSave = null;
         if(ComMethod.getObjElementNum(this.obj) == 0)
         {
            this.obj = {};
            arr0 = Gaming.defineGroup.union.building.arr;
            for each(d0 in arr0)
            {
               s0 = new UnionBuildingSave();
               s0.inDataByDefine(d0);
               this.obj[s0.name] = s0;
            }
         }
      }
      
      private function initCookingObj() : void
      {
         var arr0:Array = null;
         var d0:UnionCookingDefine = null;
         var s0:UnionCookingSave = null;
         if(ComMethod.getObjElementNum(this.cookingObj) == 0)
         {
            this.cookingObj = {};
            arr0 = Gaming.defineGroup.union.building.cookingArr;
            for each(d0 in arr0)
            {
               s0 = new UnionCookingSave();
               s0.inDataByDefine(d0);
               this.cookingObj[s0.name] = s0;
            }
         }
      }
      
      private function initSendTaskObj() : void
      {
         var arr0:Array = null;
         var d0:UnionSendTaskDefine = null;
         var s0:UnionSendTaskSave = null;
         if(ComMethod.getObjElementNum(this.sendTaskObj) == 0)
         {
            this.sendTaskObj = {};
            arr0 = Gaming.defineGroup.union.building.sendTaskArr;
            for each(d0 in arr0)
            {
               s0 = new UnionSendTaskSave();
               s0.inDataByDefine(d0);
               this.sendTaskObj[s0.name] = s0;
            }
         }
      }
      
      public function newDayCtrl() : void
      {
         this.eatNum = 0;
         this.federalState = 0;
         this.federalGiftB = false;
         this.haveSendTaskGiftTime = 0;
      }
      
      public function getNumStr() : String
      {
         var str0:String = "";
         str0 += "兑换个数：" + this.exchangeSuppliesNum;
         str0 += "\n购买个数：" + this.buySuppliesNum;
         str0 += "\n联邦任务累计次数：" + this.federalTaskNum;
         str0 += "\n派遣任务累计次数：" + this.sendTaskNum;
         str0 += "\n每日礼包领取次数：" + this.daySuppliesNum;
         var max0:int = this.federalTaskNum * 70 + this.sendTaskNum * 14 * 4 + this.daySuppliesNum * 42 + this.exchangeSuppliesNum + this.buySuppliesNum;
         return str0 + ("\n极限最大值：" + max0);
      }
      
      private function getForecastSuppliesNum() : int
      {
         return int(this.federalTaskNum * 70 + this.sendTaskNum * 14 * 4 + this.daySuppliesNum * 42 + this.exchangeSuppliesNum + this.buySuppliesNum);
      }
      
      public function getTestStr() : String
      {
         var s0:UnionBuildingSave = null;
         var lvStr0:String = null;
         var fore0:int = 0;
         var now0:int = 0;
         var zuobiB0:Boolean = false;
         var mustStr0:String = null;
         var d0:UnionBuildingDefine = null;
         var lvStrArr0:Array = [];
         var upMust0:int = 0;
         for each(s0 in this.obj)
         {
            d0 = Gaming.defineGroup.union.building.getDefine(s0.name);
            lvStrArr0.push(d0.cnName + "：" + s0.lv);
            upMust0 += s0.getAllSuppliesNum();
         }
         lvStr0 = TextWay.mixedStringArr(lvStrArr0,99,"；");
         fore0 = this.getForecastSuppliesNum();
         now0 = upMust0 + Gaming.PG.da.thingsBag.getThingsNum("militarySupplies");
         zuobiB0 = now0 > fore0;
         mustStr0 = "物资预估 " + fore0 + (zuobiB0 ? "《" : "》") + now0 + " 实际。" + (zuobiB0 ? "有作弊嫌疑" : "");
         return lvStr0 + "\n" + mustStr0;
      }
   }
}

