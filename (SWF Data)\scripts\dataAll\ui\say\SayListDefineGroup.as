package dataAll.ui.say
{
   public class SayListDefineGroup
   {
      private var obj:Object = {};
      
      public function SayListDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var i:* = undefined;
         var xml2:XML = null;
         var fatherName0:String = null;
         var xl2:XMLList = null;
         var m:* = undefined;
         var xml3:XML = null;
         var levelName0:String = null;
         var xl3:XMLList = null;
         var n:* = undefined;
         var xml4:XML = null;
         var d0:SayListDefine = null;
         var xl0:XMLList = xml0.father;
         for(i in xl0)
         {
            xml2 = xl0[i];
            fatherName0 = xml2.@name;
            xl2 = xml2.level;
            for(m in xl2)
            {
               xml3 = xl2[m];
               levelName0 = xml3.@name;
               xl3 = xml3.sayList;
               for(n in xl3)
               {
                  xml4 = xl3[n];
                  d0 = new SayListDefine();
                  d0.inData_byXML(xml4,levelName0,fatherName0);
                  if(!this.obj[levelName0])
                  {
                     this.obj[levelName0] = {};
                  }
                  this.obj[levelName0][d0.name] = d0;
               }
            }
         }
      }
      
      public function getDefineRole(levelName0:String, name0:String, p1Role0:String) : SayListDefine
      {
         var newLevel0:String = p1Role0 + "_" + levelName0;
         var d0:SayListDefine = this.getDefine(newLevel0,name0);
         if(d0 == null)
         {
            d0 = this.getDefine(levelName0,name0);
         }
         return d0;
      }
      
      public function getDefine(levelName0:String, name0:String) : SayListDefine
      {
         if(this.obj.hasOwnProperty(levelName0))
         {
            return this.obj[levelName0][name0];
         }
         return null;
      }
   }
}

