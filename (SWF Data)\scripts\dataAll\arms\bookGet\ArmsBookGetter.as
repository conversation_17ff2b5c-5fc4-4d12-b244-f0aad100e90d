package dataAll.arms.bookGet
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.StringMethod;
   import dataAll.ui.color.UIColor;
   import dataAll.ui.tip.CheckData;
   
   public class ArmsBookGetter
   {
      private static var obj:Object = {};
      
      public function ArmsBookGetter()
      {
         super();
      }
      
      public static function defineInit() : void
      {
         addArms("yearDragon","集齐以下武器","yearPig|yearChicken,yearDog,yearSnake,yearHourse,yearMouse,lightCone");
         addArms("yearTiger","集齐以下武器","yearMonkey,yearRabbit,yearCattle,penGun,rifleHornet*10,shotgunSkunk*10,sniperCicada*10");
         addArms("yearSheep","集齐以下武器","lightCone*6,meltFlamer*6,redFire*6,pistolFox*6,rocketCate*4,pianoGun,skyArch");
         addArms("consLeo","集齐以下武器","rifleHornet*16,shotgunSkunk*16,meltFlamer*12,lightCone*10,extremeGun*5,falconGun*5,rainGun");
      }
      
      private static function addArms(name0:String, title0:String, mustStr0:String) : void
      {
         var d0:ArmsBookGetDefine = new ArmsBookGetDefine();
         d0.name = name0;
         d0.inMustStr(mustStr0);
         d0.title = title0;
         obj[name0] = d0;
      }
      
      public static function getCheckById(id0:String) : CheckData
      {
         var d0:ArmsBookGetDefine = obj[id0];
         if(Boolean(d0))
         {
            return getCheck(d0);
         }
         return null;
      }
      
      public static function getDefine(name0:String) : ArmsBookGetDefine
      {
         return obj[name0];
      }
      
      private static function getCheck(d0:ArmsBookGetDefine) : CheckData
      {
         var marr1:Array = null;
         var check0:CheckData = null;
         var bb0:Boolean = false;
         var cnArr0:Array = null;
         var one0:ArmsBookGetOne = null;
         var cn1:String = null;
         var oneB0:Boolean = false;
         var marr2:Array = d0.mustArr2;
         var armsArr0:Array = Gaming.PG.da.getArmsDataArr(true,true,true,true);
         var allS0:String = "·" + d0.title;
         var allB0:Boolean = true;
         var cnArr2:Array = [];
         var allNum0:Number = marr2.length;
         var nowNum0:Number = 0;
         for each(marr1 in marr2)
         {
            bb0 = false;
            cnArr0 = [];
            for each(one0 in marr1)
            {
               if(!bb0)
               {
                  oneB0 = ArmsBookGetOne.panArr(one0,armsArr0);
                  if(oneB0)
                  {
                     bb0 = true;
                  }
               }
               cnArr0.push(one0.getCn());
            }
            cn1 = StringMethod.concatStringArr(cnArr0,999," 或 ");
            if(bb0)
            {
               nowNum0++;
               cn1 = UIColor.mixed(cn1 + " √",UIColor.green);
            }
            else
            {
               allB0 = false;
            }
            cn1 = "  ·" + cn1;
            cnArr2.push(cn1);
         }
         allS0 += ComMethod.mustColor(nowNum0,allNum0,true,"#00FF00","#FF6600");
         allS0 += "\n" + StringMethod.concatStringArr(cnArr2,1,"");
         check0 = new CheckData();
         check0.bb = allB0;
         check0.info = allS0;
         return check0;
      }
   }
}

