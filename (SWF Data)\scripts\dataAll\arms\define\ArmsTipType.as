package dataAll.arms.define
{
   public class ArmsTipType
   {
      public static const no:String = "no";
      
      public static const parts:String = "parts";
      
      public function ArmsTipType()
      {
         super();
      }
      
      public static function noUseHideB(type0:String) : Bo<PERSON>an
      {
         if(type0 == parts)
         {
            return false;
         }
         return true;
      }
      
      public static function specialB(type0:String) : <PERSON><PERSON>an
      {
         if(type0 == parts)
         {
            return false;
         }
         return true;
      }
      
      public static function skillB(type0:String) : Boolean
      {
         if(type0 == parts)
         {
            return false;
         }
         return true;
      }
   }
}

