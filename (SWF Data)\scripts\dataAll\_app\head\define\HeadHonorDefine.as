package dataAll._app.head.define
{
   import com.adobe.serialization.json.JSON2;
   import com.common.data.Base64;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll.pro.PropertyArrayDefine;
   
   public class HeadHonorDefine
   {
      public static var pro_arr:Array = [];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      private var _addObjJson:String = "";
      
      public function HeadHonorDefine()
      {
         super();
         this.min = 0;
         this.max = 0;
         this.addObjJson = "";
      }
      
      public function get min() : Number
      {
         return this.CF.getAttribute("min");
      }
      
      public function set min(v0:Number) : void
      {
         this.CF.setAttribute("min",v0);
      }
      
      public function get max() : Number
      {
         return this.CF.getAttribute("max");
      }
      
      public function set max(v0:Number) : void
      {
         this.CF.setAttribute("max",v0);
      }
      
      public function set addObjJson(v0:String) : void
      {
         this._addObjJson = Base64.encodeString(String(v0));
      }
      
      public function get addObjJson() : String
      {
         return Base64.decodeString(this._addObjJson);
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function getAddObj() : Object
      {
         var str0:String = null;
         var json0:String = this.addObjJson;
         if(json0 != "")
         {
            str0 = TextWay.replaceStr(json0,"\'","\"");
            return JSON2.decode(str0);
         }
         return {};
      }
      
      public function panValue(v0:Number) : Boolean
      {
         return v0 >= this.min && v0 <= this.max;
      }
      
      public function getProStr() : String
      {
         var name0:* = null;
         var p0:PropertyArrayDefine = null;
         var v0:Number = NaN;
         var obj0:Object = this.getAddObj();
         var nameArr0:Array = ComMethod.sortNameArrByObjAndStr(obj0,this.addObjJson);
         var str0:String = "荣誉值在" + this.getRangeStr() + "可获得以下属性提升：";
         var n:int = 0;
         for each(name0 in nameArr0)
         {
            p0 = Gaming.defineGroup.getPropertyArrayDefine(name0);
            v0 = Number(obj0[name0]);
            if(n > 0)
            {
               str0 += "、";
            }
            str0 += ComMethod.color(p0.cnName + p0.getValueString(v0),"#FFFF00");
            n++;
         }
         return str0;
      }
      
      private function getRangeStr() : String
      {
         var min0:Number = this.min;
         var max0:Number = this.max;
         if(max0 >= 99999)
         {
            return min0 + "之上";
         }
         return min0 + "~" + max0 + "之间";
      }
   }
}

