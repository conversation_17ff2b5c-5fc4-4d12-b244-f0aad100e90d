package dataAll._app.worldMap.define
{
   import com.sounto.utils.ClassProperty;
   
   public class WorldMapLevelDefine
   {
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var type:String = "";
      
      public function WorldMapLevelDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var arr0:Array = pro_arr;
         ClassProperty.inData_byXML(this,xml0,pro_arr);
      }
   }
}

