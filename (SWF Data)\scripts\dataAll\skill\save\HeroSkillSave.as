package dataAll.skill.save
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.save.ShopItemsSave;
   import dataAll.must.define.MustDefine;
   import dataAll.skill.HeroSkillData;
   import dataAll.skill.define.HeroSkillDefine;
   import dataAll.skill.define.SkillFather;
   
   public class HeroSkillSave extends ShopItemsSave
   {
      public static var pro_arr:Array = [];
      
      public static var mePro_arr:Array = null;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var baseLabel:String = "";
      
      private var dayProfiFullB:Boolean = false;
      
      private var profiFullB:Boolean = false;
      
      public var effB:Boolean = true;
      
      public function HeroSkillSave()
      {
         super();
         this.lv = 1;
         this.studyBodyLv = 1;
         itemsType = ItemsDataGroup.TYPE_SKILL;
      }
      
      public static function getUpradeMustBySave(studyBodyLv0:int, lv0:int, OriginalDefine0:HeroSkillDefine) : MustDefine
      {
         var num0:int = 0;
         var mul0:Number = NaN;
         var d0:MustDefine = new MustDefine();
         var mustLv0:int = HeroSkillDefine.getUpgradeMustLv(studyBodyLv0,lv0);
         var allLv0:int = OriginalDefine0.getAllLv();
         if(allLv0 > 0)
         {
            mustLv0 = allLv0;
         }
         if(mustLv0 > 99)
         {
            mustLv0 = 99;
         }
         d0.lv = mustLv0;
         d0.coin = Math.ceil(HeroSkillDefine.getUpgradeCoinNum(mustLv0) * OriginalDefine0.coinMustMul);
         var skillStoneNum0:int = Math.ceil(HeroSkillDefine.getUpgradeStoneNum(mustLv0) * OriginalDefine0.upgradeMustMul);
         var thingsArr0:Array = [];
         if(lv0 < 5)
         {
            thingsArr0 = ["skillStone;" + skillStoneNum0];
         }
         else if(lv0 < 7)
         {
            thingsArr0 = ["godStone;" + Math.ceil(skillStoneNum0 / 1.5)];
         }
         else
         {
            num0 = 20;
            if(lv0 == 8)
            {
               num0 = 20;
            }
            if(lv0 >= 9)
            {
               num0 = 30;
            }
            mul0 = 1;
            if(OriginalDefine0.father == "petBodySkill")
            {
               mul0 = 1.5;
            }
            num0 = Math.floor(num0 * mul0);
            thingsArr0 = ["lightStone;" + num0];
            if(lv0 >= 13)
            {
               if(OriginalDefine0.father == SkillFather.heroSkill && OriginalDefine0.isActiveB())
               {
                  thingsArr0 = ["yaStone;40"];
               }
               else
               {
                  thingsArr0.push("yearMonkey;40");
               }
            }
            if(OriginalDefine0.isFatherHeroB())
            {
               if(lv0 >= 10)
               {
                  d0.coin = 0;
               }
            }
         }
         if(OriginalDefine0.baseLabel == "possession_hero")
         {
            thingsArr0.push("possession_heroChip;10");
         }
         else if(OriginalDefine0.baseLabel == "FoggyHero")
         {
            thingsArr0.push("FoggyHeroChip;30");
         }
         else if(OriginalDefine0.baseLabel == "silverScreen_hero")
         {
            thingsArr0.push("silverScreen_heroChip;50");
         }
         d0.inThingsDataByArr(thingsArr0);
         return d0;
      }
      
      private static function getProfiMustBy(lv0:int, d0:HeroSkillDefine) : Number
      {
         var nextLv0:int = lv0 + 1;
         if(nextLv0 == 11)
         {
            return 9000;
         }
         if(nextLv0 == 12)
         {
            return 18000;
         }
         if(nextLv0 == 13)
         {
            return 27000;
         }
         if(nextLv0 >= 14)
         {
            if(d0.isActiveB())
            {
               return 27000;
            }
            return 36000;
         }
         return 0;
      }
      
      public function get lv() : Number
      {
         return this.CF.getAttribute("lv");
      }
      
      public function set lv(v0:Number) : void
      {
         this.CF.setAttribute("lv",v0);
      }
      
      public function get studyBodyLv() : Number
      {
         return this.CF.getAttribute("studyBodyLv");
      }
      
      public function set studyBodyLv(v0:Number) : void
      {
         this.CF.setAttribute("studyBodyLv",v0);
      }
      
      public function get dayProfi() : Number
      {
         return this.CF.getAttribute("dayProfi");
      }
      
      public function set dayProfi(v0:Number) : void
      {
         var max0:Number = HeroSkillData.maxDayProfi;
         this.dayProfiFullB = v0 >= max0;
         this.CF.setAttribute("dayProfi",v0);
      }
      
      public function get profi() : Number
      {
         return this.CF.getAttribute("profi");
      }
      
      public function set profi(v0:Number) : void
      {
         var max0:Number = HeroSkillData.maxProfi;
         this.profiFullB = v0 >= max0;
         this.CF.setAttribute("profi",v0);
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         super.inData_byObj(obj0);
         ClassProperty.inData_bySaveObj(this,obj0,mePro_arr);
         itemsType = ItemsDataGroup.TYPE_SKILL;
         lockB = false;
      }
      
      public function getTrueLabel() : String
      {
         return this.baseLabel + "_" + this.lv;
      }
      
      public function getDefine() : HeroSkillDefine
      {
         return Gaming.defineGroup.skill.getDefine(this.baseLabel + "_" + this.lv) as HeroSkillDefine;
      }
      
      public function getNextDefine() : HeroSkillDefine
      {
         return Gaming.defineGroup.skill.getDefine(this.baseLabel + "_" + (this.lv + 1)) as HeroSkillDefine;
      }
      
      public function getOriginalDefine() : HeroSkillDefine
      {
         return Gaming.defineGroup.skill.getDefine(this.baseLabel) as HeroSkillDefine;
      }
      
      public function getProfiFullB() : Boolean
      {
         return this.dayProfiFullB || this.profiFullB;
      }
      
      public function upgrade() : void
      {
         if(this.isMaxLevelB())
         {
            INIT.showErrorMust("已经是最高等级了，不能升级");
         }
         ++this.lv;
      }
      
      public function getUpradeMust() : MustDefine
      {
         return getUpradeMustBySave(this.studyBodyLv,this.lv,this.getOriginalDefine());
      }
      
      public function getProfiMust() : Number
      {
         return getProfiMustBy(this.lv,this.getDefine());
      }
      
      public function isMaxLevelB() : Boolean
      {
         return this.lv >= this.getOriginalDefine().getMaxLevel();
      }
      
      public function resetNewBaseLabel(label0:String, lv0:int) : void
      {
         this.baseLabel = label0;
         this.lv = lv0;
         this.studyBodyLv = 1;
      }
   }
}

