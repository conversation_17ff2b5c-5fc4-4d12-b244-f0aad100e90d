package dataAll._player.more
{
   import dataAll._app.city.dress.CityDressType;
   import dataAll._app.city.dress.IO_CityDressMouldMaker;
   import dataAll._app.love.LoveData;
   import dataAll._app.outfit.OutfitDataGroup;
   import dataAll._app.partner.PartnerData;
   import dataAll._app.task.define.TaskType;
   import dataAll._player.IO_PlayerLevelGetter;
   import dataAll._player.PlayerData;
   import dataAll._player.base.PlayerBaseData;
   import dataAll._player.define.MainPlayerType;
   import dataAll._player.define.PlayerDataName;
   import dataAll._player.io.IO_HavePlayerData;
   import dataAll._player.more.creator.MoreAddData;
   import dataAll.arms.ArmsChargerDataGroup;
   import dataAll.arms.ArmsData;
   import dataAll.arms.ArmsDataGroup;
   import dataAll.arms.save.ArmsSave;
   import dataAll.body.define.HeroDefine;
   import dataAll.equip.EquipData;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.EquipPropertyData;
   import dataAll.equip.add.EquipAddAgentGroup;
   import dataAll.equip.add.EquipAddGetter;
   import dataAll.equip.add.IO_EquipAddGetter;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipPro;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.save.EquipSaveGroup;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.skill.HeroSkillData;
   import dataAll.skill.HeroSkillDataGroup;
   import dataAll.skill.define.HeroSkillDefine;
   import gameAll.hero.HeroBody;
   import gameAll.more.DoubleCtrl;
   
   public class NormalPlayerData implements IO_CityDressMouldMaker
   {
      public static const swapProSaveArr:Array = ["base","arms","equip","skill","skillBag","partner","love"];
      
      protected static const swapProArr:Array = swapProSaveArr.concat(["outfit","charger","lotteryGetter"]);
      
      protected var bagNameArr:Array = ["arms","equip","skill","skillBag"];
      
      public var mainPlayerType:String = MainPlayerType.ME;
      
      protected var saveGroup:NormalPlayerSave;
      
      public var hero:HeroBody = null;
      
      protected var memoryEquip:EquipDataGroup = null;
      
      public var base:PlayerBaseData = new PlayerBaseData();
      
      public var arms:ArmsDataGroup = new ArmsDataGroup();
      
      public var equip:EquipDataGroup = new EquipDataGroup();
      
      public var skill:HeroSkillDataGroup = new HeroSkillDataGroup();
      
      public var skillBag:HeroSkillDataGroup = new HeroSkillDataGroup();
      
      public var outfit:OutfitDataGroup = new OutfitDataGroup();
      
      public var partner:PartnerData = new PartnerData();
      
      public var love:LoveData = new LoveData();
      
      public var charger:ArmsChargerDataGroup = new ArmsChargerDataGroup();
      
      public var lotteryGetter:EquipAddGetter = new EquipAddGetter(PlayerDataName.lotteryGetter);
      
      public var heroData:MoreData = null;
      
      public function NormalPlayerData()
      {
         super();
         this.skillBag.placeType = ItemsDataGroup.PLACE_BAG;
      }
      
      public function testPlayerData() : void
      {
         var g0:ItemsDataGroup = null;
         var arr0:Array = this.getIGArr();
         for each(g0 in arr0)
         {
            g0.testPlayerData(this);
         }
      }
      
      public function isMePlayerType() : Boolean
      {
         return this.mainPlayerType == MainPlayerType.ME;
      }
      
      public function fleshReference(moreData0:MoreData) : void
      {
         this.heroData = moreData0;
         this.arms.setPlayerData(this);
         this.equip.setPlayerData(this);
         this.skill.setPlayerData(this);
         this.skillBag.setPlayerData(this);
         this.partner.setPlayerData(this);
         this.love.setPlayerData(this);
         this.lotteryGetter.setPlayerData(this);
      }
      
      public function initSave(heroData0:MoreData, firstLv0:int = 0) : void
      {
         var s0:ArmsSave = Gaming.defineGroup.armsCreator.getSave_byArmsRangeName(heroData0.def.randomArmsRange[0],this.level,"white","diff_0");
         this.arms.addSave(s0);
      }
      
      public function initSaveByMoreAddData(da0:MoreAddData) : void
      {
         var s0:ArmsSave = null;
         var skillName0:* = undefined;
         this.base.save.level = da0.getLv();
         if(da0.bodyDef is HeroDefine)
         {
            s0 = Gaming.defineGroup.armsCreator.getSave_byArmsRangeName((da0.bodyDef as HeroDefine).randomArmsRange[0],this.level,"blue","diff_0");
            this.arms.addSave(s0);
         }
         if(da0.firstSkillArr.length > 0)
         {
            for each(skillName0 in da0.firstSkillArr)
            {
               this.skill.addSkillByLabel(skillName0,da0.getFirstSkillMustLv(),true);
            }
         }
      }
      
      public function inData_bySave(save0:NormalPlayerSave) : void
      {
         this.saveGroup = save0;
         this.charger.initData();
         this.base.inData_bySave(save0.base,this);
         this.arms.inData_bySaveGroup(save0.arms);
         this.equip.inData_bySaveGroup(save0.equip);
         this.skill.inData_bySaveGroup(save0.skill);
         this.skillBag.inData_bySaveGroup(save0.skillBag);
         this.partner.inData_bySave(save0.partner);
         this.love.inData_bySave(save0.love);
         this.outfit.initData();
      }
      
      public function setMainPlayerType(type0:String) : void
      {
         this.mainPlayerType = type0;
         if(this.hero is HeroBody)
         {
            this.hero.dat.mainPlayerType = type0;
         }
      }
      
      public function inSwapObj(obj0:Object, saveObj0:Object) : void
      {
         var name0:* = null;
         var da0:* = undefined;
         var hda0:IO_HavePlayerData = null;
         this.saveGroup.inSwapObj(saveObj0);
         for each(name0 in swapProArr)
         {
            da0 = obj0[name0];
            this[name0] = da0;
            hda0 = da0 as IO_HavePlayerData;
            if(Boolean(hda0))
            {
               hda0.setAllPlayerData(this);
            }
         }
      }
      
      protected function getSwapObj() : Object
      {
         var name0:* = null;
         var obj0:Object = {};
         for each(name0 in swapProArr)
         {
            obj0[name0] = this[name0];
         }
         return obj0;
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.skill.newDayCtrl(timeStr0);
         this.skillBag.newDayCtrl(timeStr0);
         this.partner.newDayCtrl(timeStr0);
         this.love.newDayCtrl(timeStr0,this is MorePlayerData);
      }
      
      public function getEquipAddDataNameArr() : Array
      {
         var arr0:Array = PlayerDataName.normal_proAddArr;
         if(this is PlayerData)
         {
            arr0 = arr0.concat(PlayerDataName.player_proAddArr);
            if(this.isMePlayerType())
            {
               arr0 = arr0.concat(PlayerDataName.me_proAddArr);
            }
         }
         return arr0.concat(PlayerDataName.last_proAddArr);
      }
      
      public function getEquipAddAgentGroup(proArr0:Array) : EquipAddAgentGroup
      {
         var g0:EquipAddAgentGroup = new EquipAddAgentGroup();
         g0.inData(this,proArr0);
         return g0;
      }
      
      public function getEquipAddGetter(name0:String) : IO_EquipAddGetter
      {
         if(this.hasOwnProperty(name0))
         {
            return this[name0];
         }
         return null;
      }
      
      public function getWearLevel() : int
      {
         var mlv0:int = 0;
         var lv0:int = this.level;
         if(Boolean(this.memoryEquip))
         {
            mlv0 = Gaming.LG.getMemoryLvLimit();
            if(mlv0 < lv0)
            {
               lv0 = mlv0;
            }
         }
         return lv0;
      }
      
      public function addExp(v0:Number) : Boolean
      {
         return this.base.addExp(v0);
      }
      
      public function get level() : int
      {
         return this.base.save.level;
      }
      
      public function getDps() : Number
      {
         var v0:Number = this.arms.getShowDps();
         var crit3:Number = this.getHeroMerge().critPro3;
         return Math.round(v0 * (1 + crit3 * 2));
      }
      
      public function getDpsWholeAdd() : Number
      {
         return 0;
      }
      
      public function zuobiPan(otherPlayerB0:Boolean = false) : String
      {
         var str0:String = this.equip.zuobiPan();
         var str1:String = this.arms.zuobiPan();
         if(str0 == "" && str1 == "")
         {
            return "";
         }
         return str0 + "," + str1;
      }
      
      public function isMainPlayerB() : Boolean
      {
         return this is PlayerData;
      }
      
      public function isP1() : Boolean
      {
         return this.isMainPlayerB();
      }
      
      public function isP2() : Boolean
      {
         return this.isPlayerCtrlB() && !this.isMainPlayerB();
      }
      
      public function isPlayerCtrlB() : Boolean
      {
         return this.base.save.playerCtrlB;
      }
      
      public function setPlayerCtrlB(bb0:Boolean) : void
      {
         this.base.save.playerCtrlB = bb0;
      }
      
      public function getPlayerCtrlString() : String
      {
         if(this.mainPlayerType == "me")
         {
            if(this.isMainPlayerB())
            {
               return "p1";
            }
            if(this.isPlayerCtrlB())
            {
               return "p2";
            }
         }
         return "";
      }
      
      public function getKeyPlayerType() : String
      {
         if(!DoubleCtrl.levelDoubleB())
         {
            return "single";
         }
         return this.getPlayerCtrlString();
      }
      
      public function getMainPlayerData() : PlayerData
      {
         return this.heroData.playerData;
      }
      
      public function getAllDataArrByItemsType(type0:String, houseB0:Boolean = false) : Array
      {
         var dg0:ItemsDataGroup = this[type0];
         if(dg0 is ItemsDataGroup)
         {
            return dg0.dataArr;
         }
         return null;
      }
      
      public function haveItemsDataGroup(dg0:ItemsDataGroup) : Boolean
      {
         var dg2:ItemsDataGroup = null;
         var arr_len0:int = int(this.bagNameArr.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            dg2 = this[this.bagNameArr[i]];
            if(dg0 == dg2)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getImageMcObj(setEquipDataGroup0:EquipDataGroup = null, haveFashionB0:Boolean = true) : Object
      {
         if(!(setEquipDataGroup0 is EquipDataGroup))
         {
            setEquipDataGroup0 = this.equip;
         }
         var fourNameArr0:Array = setEquipDataGroup0.getFourEquipImgNameArr(haveFashionB0);
         return Gaming.BG.getPartImageMcObjByEquipNameArr(fourNameArr0,this.getImgHeroDefine());
      }
      
      public function getImgHeroDefine() : HeroDefine
      {
         var fashionD0:EquipDefine = null;
         var imgName0:String = null;
         var baseDef0:HeroDefine = this.heroData.def;
         if(Gaming.LG.canEquipType(EquipType.FASHION))
         {
            fashionD0 = this.equip.getWearShowFashionDef();
            if(Boolean(fashionD0))
            {
               imgName0 = fashionD0.getImgBodyName();
               if(baseDef0.name != imgName0 && baseDef0.movieLink != imgName0)
               {
                  return Gaming.defineGroup.body.getHeroDefine(imgName0);
               }
            }
         }
         return baseDef0;
      }
      
      public function getGunImageNameArr() : Array
      {
         return this.arms.saveGroup.getArmsImageArr();
      }
      
      public function getArmsDataArr(bagB0:Boolean = false, moreB0:Boolean = false, moreBagB0:Boolean = false, houseB0:Boolean = false) : Array
      {
         return this.arms.dataArr;
      }
      
      public function getEquipDataArr(bagB0:Boolean = false, moreB0:Boolean = false, moreBagB0:Boolean = false, onlyNormalB0:Boolean = false, houseB0:Boolean = false) : Array
      {
         if(onlyNormalB0)
         {
            return this.equip.getNormalDataArr();
         }
         return this.equip.dataArr;
      }
      
      public function findItemsData(da0:IO_ItemsData) : ItemsDataGroup
      {
         var dg0:ItemsDataGroup = null;
         var dgarr0:Array = this.getItemsDataGroupArr();
         for each(dg0 in dgarr0)
         {
            if(dg0.dataArr.indexOf(da0) >= 0)
            {
               return dg0;
            }
         }
         return null;
      }
      
      public function getItemsDataGroupArr() : Array
      {
         var name0:* = null;
         var g0:ItemsDataGroup = null;
         var arr0:Array = [];
         for each(name0 in this.bagNameArr)
         {
            g0 = this[name0];
            arr0.push(g0);
         }
         return arr0;
      }
      
      protected function getIGArr() : Array
      {
         return [this.arms,this.equip,this.skill,this.skillBag];
      }
      
      public function findArmsData(da0:ArmsData, houseB0:Boolean = false) : ArmsDataGroup
      {
         return this.arms.haveData(da0) ? this.arms : null;
      }
      
      public function findEquipData(da0:EquipData, houseB0:Boolean = false) : EquipDataGroup
      {
         return this.equip.haveData(da0) ? this.equip : null;
      }
      
      public function getAllMustArenaStampNum() : Number
      {
         var dg0:ItemsDataGroup = null;
         var num0:Number = 0;
         var dgArr0:Array = [this.arms,this.equip];
         for each(dg0 in dgArr0)
         {
            num0 += dg0.getAllMustArenaStampNum();
         }
         return num0;
      }
      
      public function getAllItemsDataGroupArr() : Array
      {
         return [this.arms,this.equip];
      }
      
      public function findArenaGiftItemsData(str0:String) : IO_ItemsData
      {
         var dg0:ItemsDataGroup = null;
         var da0:IO_ItemsData = null;
         var dgArr0:Array = [this.arms,this.equip];
         for each(dg0 in dgArr0)
         {
            da0 = dg0.findArenaGiftItemsData(str0);
            if(da0 is IO_ItemsData)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function getAllSkillLabelArr(equipB0:Boolean = true, skillB0:Boolean = true, mapMode0:String = "regular", skillCnRange0:Array = null, skillNumLimit0:int = -1) : Array
      {
         var meCnRange0:Array = this.getHeroSkillCnArr();
         var arr0:Array = [];
         if(equipB0)
         {
            if(skillB0)
            {
               arr0 = arr0.concat(this.skill.getTrueLabelArr(meCnRange0,skillCnRange0,skillNumLimit0));
               if(skillNumLimit0 == -1)
               {
                  arr0 = arr0.concat(this.skillBag.getNoNeedTrueLabelArr(meCnRange0,skillCnRange0));
               }
            }
            arr0 = arr0.concat(this.equip.getSkillNameArr());
            arr0 = arr0.concat(this.outfit.getSkillNameArr());
         }
         else
         {
            arr0 = this.skill.getTrueLabelArr(meCnRange0,skillCnRange0,skillNumLimit0);
         }
         return arr0;
      }
      
      public function getHeroSkillCnArr() : Array
      {
         return this.heroData.def.getHeroSkillCnArr(this.isP1(),Gaming.PG.da.getRoleName());
      }
      
      public function getStudyMustLv() : int
      {
         return HeroSkillDefine.getStudyMustLv(this.skill.dataArr.length + this.skillBag.dataArr.length) + this.heroData.def.moreD.studySkillLvAdd;
      }
      
      public function getNoStudyOriginalSkillDefineArr() : Array
      {
         var n:* = undefined;
         var d0:HeroSkillDefine = null;
         var d2:HeroSkillDefine = null;
         var cnArr0:Array = this.getHeroSkillCnArr();
         var arr0:Array = Gaming.defineGroup.skill.getArrByCnNameArr(cnArr0);
         var arr1:Array = [];
         for(n in arr0)
         {
            d0 = arr0[n];
            if(!this.skill.getDataByBase(d0.name) && !this.skillBag.getDataByBase(d0.name))
            {
               d2 = Gaming.defineGroup.skill.getHeroDefine(d0.name,1);
               arr1.push(d2);
            }
         }
         return arr1;
      }
      
      public function haveSkillDataB(da0:HeroSkillData) : Boolean
      {
         if(this.skill.dataArr.indexOf(da0) == -1)
         {
            return this.skillBag.dataArr.indexOf(da0) >= 0;
         }
         return true;
      }
      
      public function getSkillChooseNum() : int
      {
         var lv0:int = this.level;
         if(lv0 < 15)
         {
            return 4;
         }
         if(lv0 < 25)
         {
            return 5;
         }
         if(lv0 < 35)
         {
            return 6;
         }
         if(lv0 < 45)
         {
            return 7;
         }
         return 8;
      }
      
      public function getVehicleExtraSkillArr() : Array
      {
         var addName0:String = null;
         var da0:HeroSkillData = null;
         var arr0:Array = [];
         if(this.isMainPlayerB())
         {
            addName0 = "vacuumJie";
            da0 = this.skill.getDataByBase(addName0);
            if(Boolean(da0))
            {
               arr0.push(da0.save.getDefine().name);
            }
         }
         return arr0;
      }
      
      public function getRoleDpsMul() : Number
      {
         return this.heroData.def.moreD.dpsMul;
      }
      
      public function getRoleUnderHurtMul() : Number
      {
         return this.heroData.def.moreD.underHurtMul;
      }
      
      public function getProfiNoFull() : HeroSkillData
      {
         var da0:HeroSkillData = this.skill.getProfiNoFull();
         if(da0 == null)
         {
            da0 = this.skillBag.getProfiNoFull();
         }
         return da0;
      }
      
      public function isMore50Pro(pro0:String) : Boolean
      {
         var main0:PlayerData = null;
         if(EquipPro.isMore50(pro0))
         {
            main0 = this.getMainPlayerData();
            if(!Boolean(main0))
            {
               return true;
            }
            if(main0 != this)
            {
               if(main0.isCpB(this) == false)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function fleshAllByEquip() : void
      {
         this.equip.fleshMergeData();
         if(Boolean(this.memoryEquip))
         {
            this.memoryEquip.fleshMergeDataOnlyMe();
         }
         var me0:EquipPropertyData = this.getHeroMerge();
         this.arms.fleshData_byEquip(me0);
         this.charger.flesh_byEquip(me0);
         this.outfit.fleshData(this);
         if(Boolean(this.hero))
         {
            this.hero.fleshEquipImage();
            this.hero.dat.changeEquipEvent();
         }
      }
      
      public function fleshAIHero() : void
      {
         if(Boolean(this.hero))
         {
            this.hero.ai.heroAttackAI.fleshAIData(this);
         }
      }
      
      public function fillAllData() : void
      {
         this.arms.fillAllData();
         this.charger.fillAllData();
         if(Boolean(this.hero))
         {
            this.hero.dat.fillAllData();
         }
      }
      
      public function fleshNewArmsData(da0:ArmsData) : void
      {
         da0.fleshData_byEquip(this.getHeroMerge(),false);
      }
      
      public function getHeroEquip() : EquipDataGroup
      {
         if(Boolean(this.memoryEquip))
         {
            return this.memoryEquip;
         }
         return this.equip;
      }
      
      public function getMerge() : EquipPropertyData
      {
         return this.equip.mergeData;
      }
      
      public function getHeroMerge() : EquipPropertyData
      {
         if(Boolean(this.memoryEquip))
         {
            return this.memoryEquip.mergeData;
         }
         return this.equip.mergeData;
      }
      
      public function getLifeMerge() : EquipPropertyData
      {
         if(Boolean(this.memoryEquip))
         {
            return this.memoryEquip.mergeData;
         }
         return this.equip.mergeData;
      }
      
      public function getDropMerge() : EquipPropertyData
      {
         if(Boolean(this.memoryEquip))
         {
            return this.memoryEquip.mergeData;
         }
         return this.equip.mergeData;
      }
      
      public function getMoveMerge() : EquipPropertyData
      {
         if(Boolean(this.memoryEquip))
         {
            return this.memoryEquip.mergeData;
         }
         return this.equip.mergeData;
      }
      
      public function setHero(hero0:HeroBody, lg0:IO_PlayerLevelGetter) : void
      {
         var fleshEquipB0:Boolean = true;
         this.hero = hero0;
         if(Boolean(this.hero))
         {
            if(lg0.isMemoryTaskB())
            {
               this.memoryEquip = this.getMemoryEquip(lg0);
               fleshEquipB0 = true;
            }
            else
            {
               this.memoryEquip = null;
            }
            this.hero.inPlayerData(this,this.getHeroEquip());
         }
         else
         {
            if(Boolean(this.memoryEquip))
            {
               fleshEquipB0 = true;
            }
            this.memoryEquip = null;
            this.arms.nowData = null;
            this.charger.nowData = null;
         }
         if(fleshEquipB0)
         {
            this.fleshAllByEquip();
         }
         this.fillAllData();
         this.changeArmsPan();
      }
      
      private function getMemoryEquip(lg0:IO_PlayerLevelGetter) : EquipDataGroup
      {
         var part0:* = null;
         var unlockLv0:int = 0;
         var da0:EquipData = null;
         var ca0:EquipData = null;
         var eg0:EquipDataGroup = new EquipDataGroup();
         var sg0:EquipSaveGroup = new EquipSaveGroup();
         sg0.gripMaxNum = EquipType.WEAR_ARR.length;
         sg0.unlockAll();
         eg0.inData_bySaveGroup(sg0);
         eg0.keepBodyEquipShowB = false;
         var memoryLv0:int = int(lg0.getMemoryLvLimit());
         var parr0:Array = EquipType.WEAR_ARR;
         for each(part0 in parr0)
         {
            unlockLv0 = TaskType.getEquipTypeUnlockLv(part0);
            if(memoryLv0 >= unlockLv0)
            {
               da0 = this.equip.getOneDataByType(part0);
               if(Boolean(da0))
               {
                  ca0 = da0.clone();
                  eg0.addData(ca0);
               }
            }
         }
         eg0.fleshMergeDataOnlyMe();
         return eg0;
      }
      
      public function panEquipCanUse(lg0:IO_PlayerLevelGetter, equipType0:String) : Boolean
      {
         if(Boolean(this.memoryEquip))
         {
            return lg0.getMemoryLvLimit() >= TaskType.getEquipTypeUnlockLv(equipType0);
         }
         return true;
      }
      
      public function changeArmsPan() : void
      {
         var bb0:Boolean = this.arms.nowArmsDataInArrB();
         if(Boolean(this.hero))
         {
            if(!bb0)
            {
               this.hero.arms.nextArms();
            }
            this.hero.dat.changeArmsEvent();
         }
         Gaming.pretreatGroup.preOnePlayer(this);
      }
      
      public function getNormalSave() : NormalPlayerSave
      {
         return this.saveGroup;
      }
      
      public function checkNormalPlayerData() : void
      {
         this.arms.checkNormalPlayerData(this);
         this.equip.checkNormalPlayerData(this);
         this.skill.checkNormalPlayerData(this);
         this.skillBag.checkNormalPlayerData(this);
      }
      
      public function getId() : String
      {
         return this.heroData.def.name;
      }
      
      public function getCnName() : String
      {
         return this.heroData.getCnName();
      }
      
      public function getRoleName() : String
      {
         return this.heroData.def.name;
      }
      
      public function getBodyCn() : String
      {
         return this.heroData.def.cnName;
      }
      
      public function getSayCn() : String
      {
         return this.heroData.def.getSayTitle();
      }
      
      public function getTempIconUrl() : String
      {
         var ed0:EquipDefine = this.equip.wearShowFashionDef;
         if(ed0 && ed0.hd && EquipDefine.FASHION_KEEP_ROLE_HEAD == false)
         {
            return ed0.getHeadIconUrl();
         }
         return this.heroData.def.headIconUrl;
      }
      
      public function getIconUrl() : String
      {
         var ed0:EquipDefine = null;
         if(EquipDefine.FASHION_KEEP_ROLE_HEAD == false)
         {
            ed0 = this.equip.getWearShowFashionDef();
            if(Boolean(ed0) && ed0.hd)
            {
               return ed0.getHeadIconUrl();
            }
         }
         return this.heroData.def.headIconUrl;
      }
      
      public function getCityDressType() : String
      {
         return CityDressType.player;
      }
      
      public function getCityDressImgUrl() : String
      {
         return this.heroData.def.name + "/equipStand";
      }
   }
}

