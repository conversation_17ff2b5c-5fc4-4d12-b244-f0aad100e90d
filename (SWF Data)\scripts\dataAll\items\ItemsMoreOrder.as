package dataAll.items
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   
   public class ItemsMoreOrder
   {
      public static const allChoose:String = "allChoose";
      
      public static const allChoose_cn:String = "全选";
      
      public static const invertChoose:String = "invertChoose";
      
      public static const invertChoose_cn:String = "反选";
      
      public static const allEquipChoose:String = "allEquipChoose";
      
      public static const allEquipChoose_cn:String = "全选";
      
      public static const invertEquipChoose:String = "invertEquipChoose";
      
      public static const invertEquipChoose_cn:String = "反选";
      
      public static const unlockChoose:String = "unlockChoose";
      
      public static const unlockChoose_cn:String = "选择未锁定";
      
      public static const lock:String = "lock";
      
      public static const lock_cn:String = "锁定";
      
      public static const unlock:String = "unlock";
      
      public static const unlock_cn:String = "解锁";
      
      public static const sell:String = "sell";
      
      public static const sell_cn:String = "卖出";
      
      public static const refining:String = "refining";
      
      public static const refining_cn:String = "分解";
      
      public static const destroy:String = "destroy";
      
      public static const destroy_cn:String = "摧毁";
      
      public static const decompose:String = "decompose";
      
      public static const decompose_cn:String = "拆成零件";
      
      public static const resolve:String = "resolve";
      
      public static const resolve_cn:String = "分解碎片";
      
      public static const tipArr:Array = [sell,refining,destroy,decompose,resolve];
      
      public function ItemsMoreOrder()
      {
         super();
      }
      
      public static function isChooseB(name0:String) : Boolean
      {
         return name0.indexOf("Choose") > 0;
      }
      
      public static function getCn(name0:String) : String
      {
         return ItemsMoreOrder[name0 + "_cn"];
      }
      
      public static function getTextByNameArr(nameArr0:Array, linkB0:Boolean = true) : String
      {
         var name0:* = null;
         var s0:String = null;
         var strArr0:Array = [];
         for each(name0 in nameArr0)
         {
            s0 = getTextByName(name0,linkB0);
            strArr0.push(s0);
         }
         return TextWay.mixedStringArr(strArr0,999,"   ");
      }
      
      private static function getTextByName(name0:String, linkB0:Boolean) : String
      {
         var cn0:String = getCn(name0);
         var s0:String = ComMethod.color(cn0,"#666666");
         if(linkB0)
         {
            s0 = ComMethod.link(cn0,name0);
         }
         return s0;
      }
   }
}

