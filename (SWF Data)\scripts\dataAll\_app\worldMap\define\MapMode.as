package dataAll._app.worldMap.define
{
   public class MapMode
   {
      public static const REGULAR:String = "regular";
      
      public static const CHALLENGE:String = "challenge";
      
      public static const ENDLESS:String = "endless";
      
      public static const DEMON:String = "demon";
      
      public static const MODE_ARR:Array = [REGULAR,CHALLENGE,ENDLESS,DEMON];
      
      public static const SPACE:String = "space";
      
      public function MapMode()
      {
         super();
      }
      
      public static function getCnName(name0:String) : String
      {
         if(name0 == REGULAR)
         {
            return "正常";
         }
         if(name0 == CHALLENGE)
         {
            return "挑战";
         }
         if(name0 == ENDLESS)
         {
            return "无尽";
         }
         if(name0 == DEMON)
         {
            return "修罗";
         }
         if(name0 == SPACE)
         {
            return "太空";
         }
         return "";
      }
      
      public static function getFullCnName(name0:String) : String
      {
         return getCnName(name0);
      }
      
      public static function getDemonLv() : int
      {
         return 99;
      }
      
      public static function isNormalLevelB(model0:String) : Boolean
      {
         return model0 == REGULAR || model0 == DEMON;
      }
   }
}

