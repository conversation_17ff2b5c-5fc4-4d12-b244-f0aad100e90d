package dataAll._player.base
{
   import dataAll._app.task.define.TaskType;
   import dataAll._player.PlayerData;
   import dataAll._player.io.IO_HavePlayerData;
   import dataAll._player.more.MorePlayerData;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.equip.EquipPropertyData;
   import dataAll.level.LevelCountSave;
   import dataAll.must.define.MustDefine;
   
   public class PlayerBaseData implements IO_NameChangeGetter, IO_HavePlayerData
   {
      private static const _MAX_LEVEL:Number = 19.8;
      
      private static const _ENEMY_LEVEL:Number = 9.9;
      
      public var save:PlayerBaseSave = null;
      
      private var normalPlayerData:NormalPlayerData = null;
      
      public var nowCountSave:LevelCountSave = new LevelCountSave();
      
      private var overExp:Number = 0;
      
      public function PlayerBaseData()
      {
         super();
      }
      
      public static function get MAX_LEVEL() : int
      {
         return Math.round(_MAX_LEVEL * 5);
      }
      
      public static function get ENEMY_LEVEL() : int
      {
         return Math.round(_ENEMY_LEVEL * 10);
      }
      
      public function inData_bySave(s0:PlayerBaseSave, normalPlayerData0:NormalPlayerData) : void
      {
         this.save = s0;
         this.normalPlayerData = normalPlayerData0;
      }
      
      public function setPlayerData(pd0:NormalPlayerData) : void
      {
         this.normalPlayerData = pd0;
      }
      
      public function setAllPlayerData(pd0:NormalPlayerData) : void
      {
         this.setPlayerData(pd0);
      }
      
      public function addExp(v0:Number) : Boolean
      {
         var loopNum0:int = 0;
         v0 = Math.round(v0);
         var level0:int = this.save.level;
         var level2:int = level0;
         var maxLevel0:int = MAX_LEVEL;
         var max0:Number = this.getNextMaxExpByLevel(level0);
         var now0:Number = this.save.exp;
         var over0:Number = 0;
         var upgradeB0:Boolean = false;
         now0 += v0;
         this.nowCountSave.exp += v0;
         if(level0 >= maxLevel0)
         {
            if(now0 > max0)
            {
               over0 = now0 - max0 + 1;
               now0 = max0 - 1;
            }
            this.save.exp = now0;
         }
         else if(this.save.lockB)
         {
            this.save.exp = now0;
         }
         else
         {
            loopNum0 = 0;
            while(now0 >= max0)
            {
               loopNum0++;
               now0 -= max0;
               level0++;
               if(level0 >= maxLevel0)
               {
                  break;
               }
               max0 = this.getNextMaxExpByLevel(level0);
            }
            if(level0 >= maxLevel0)
            {
               if(now0 > max0)
               {
                  over0 = now0 - max0 + 1;
                  now0 = max0 - 1;
               }
            }
            this.save.exp = now0;
            if(level0 > level2)
            {
               this.save.level = level0;
               upgradeB0 = true;
            }
         }
         this.overExp = over0;
         return upgradeB0;
      }
      
      public function typeUplevel() : Boolean
      {
         var level0:int = 0;
         var max0:Number = NaN;
         if(this.canUplevelB())
         {
            level0 = this.save.level;
            max0 = this.getNextMaxExpByLevel(level0);
            this.save.exp -= max0;
            level0++;
            this.save.level = level0;
            if(level0 >= MAX_LEVEL)
            {
               this.save.lockB = false;
            }
            return true;
         }
         return false;
      }
      
      public function canUplevelB() : Boolean
      {
         var max0:Number = NaN;
         var now0:Number = NaN;
         var level0:int = this.save.level;
         var maxLevel0:int = MAX_LEVEL;
         if(level0 >= maxLevel0)
         {
            return false;
         }
         max0 = this.getNextMaxExpByLevel(level0);
         now0 = this.save.exp;
         if(now0 < max0)
         {
            return false;
         }
         return true;
      }
      
      public function canLockB() : Boolean
      {
         var level0:int = this.save.level;
         var maxLevel0:int = MAX_LEVEL;
         if(level0 >= maxLevel0)
         {
            return false;
         }
         return true;
      }
      
      public function getOverExp() : Number
      {
         return this.overExp;
      }
      
      public function getMaxLife() : Number
      {
         var md0:PlayerData = null;
         var me0:EquipPropertyData = this.normalPlayerData.getLifeMerge();
         var b0:Number = this.getBaseLife();
         var v0:Number = (b0 * (1 + me0.lifeMul) + me0.life) * (1 + me0.vipDef.lifeMul) * (1 + me0.getLifeAll());
         var pd0:MorePlayerData = this.normalPlayerData as MorePlayerData;
         if(Boolean(pd0))
         {
            md0 = pd0.getMainPlayerData();
            if(Boolean(md0))
            {
               v0 *= 1 + md0.getLifeMerge().moreLifeMul;
            }
         }
         return Math.ceil(v0);
      }
      
      public function getBaseLife() : Number
      {
         var limitLv0:int = Gaming.LG.getMemoryLvLimit();
         if(limitLv0 < 100)
         {
            return TaskType.getHeroLife(limitLv0);
         }
         return Gaming.defineGroup.normal.getHeroLife(this.save.level);
      }
      
      public function getLifeRate() : Number
      {
         var me0:EquipPropertyData = this.normalPlayerData.getLifeMerge();
         return Math.ceil(me0.getLifeRate() * (1 + me0.lifeRateMul));
      }
      
      public function getHeadDefence() : Number
      {
         var me0:EquipPropertyData = this.normalPlayerData.getLifeMerge();
         return Math.ceil(me0.head * (1 + me0.headMul) * (1 + me0.vipDef.defenceMul));
      }
      
      public function getHeadHurtMul() : Number
      {
         var b0:Number = this.getBaseLife();
         var v0:Number = this.getHeadDefence() / b0 / 2;
         if(v0 > 0.8)
         {
            v0 = 0.8;
         }
         return v0;
      }
      
      public function getNextMaxExp() : Number
      {
         return Gaming.defineGroup.normal.getHeroExp(this.save.level);
      }
      
      public function getNextMaxExpByLevel(lv0:int) : Number
      {
         return Gaming.defineGroup.normal.getHeroExp(lv0);
      }
      
      public function getNowExp() : Number
      {
         return this.save.exp;
      }
      
      public function setUIName(name0:String) : void
      {
         this.save.playerName = name0;
      }
      
      public function getUIName() : String
      {
         return this.save.playerName;
      }
      
      public function getChangeNameMust() : MustDefine
      {
         return Gaming.defineGroup.must.changeName;
      }
      
      public function getChangeNameMaxChar() : int
      {
         return 8;
      }
      
      public function zuobiPan() : String
      {
         return "";
      }
   }
}

