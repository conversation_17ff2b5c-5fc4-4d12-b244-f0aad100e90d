package dataAll.body.attack
{
   import com.sounto.oldUtils.ComMethod;
   
   public class HurtKind
   {
      public static const no:String = "";
      
      public static const fire:String = ElementHurt.fire;
      
      public static const electric:String = ElementHurt.electric;
      
      public static const water:String = "water";
      
      public static const poison:String = ElementHurt.poison;
      
      public static const soil:String = "soil";
      
      public static const wind:String = "wind";
      
      public static const boom:String = "boom";
      
      public static const hit:String = "hit";
      
      public static const wave:String = "wave";
      
      public static const particle:String = "particle";
      
      public static const light:String = "light";
      
      public static const arr:Array = [no,fire,electric,water,poison,soil,wind,boom,hit,wave,particle,light];
      
      private static const cnObj:Object = {
         "fire":"火",
         "electric":"电",
         "water":"水",
         "poison":"毒",
         "soil":"土",
         "wind":"风",
         "boom":"爆炸",
         "hit":"动能",
         "wave":"波",
         "particle":"粒子",
         "light":"光"
      };
      
      private static const colorObj:Object = {
         "fire":"#FF4F4F",
         "electric":"#E03CFF",
         "water":"#3399FF",
         "poison":"#66FF00",
         "soil":"#A17316",
         "wind":"#ECFFD0",
         "boom":"#FF6600",
         "hit":"#FFFF00",
         "wave":"#807EFF",
         "particle":"#2AD29F",
         "light":"#80E6FF"
      };
      
      public function HurtKind()
      {
         super();
      }
      
      public static function getNumber1Obj() : Object
      {
         var name0:* = null;
         var obj0:Object = {};
         for each(name0 in arr)
         {
            obj0[name0] = 1;
         }
         return obj0;
      }
      
      public static function getByEle(ele0:String) : String
      {
         if(ele0 == "")
         {
            return no;
         }
         if(ele0 == ElementHurt.frozen)
         {
            return water;
         }
         return ele0;
      }
      
      public static function getEle(kind0:String) : String
      {
         if(kind0 == fire)
         {
            return ElementHurt.fire;
         }
         if(kind0 == electric)
         {
            return ElementHurt.electric;
         }
         if(kind0 == poison)
         {
            return ElementHurt.poison;
         }
         if(kind0 == water)
         {
            return ElementHurt.frozen;
         }
         return ElementHurt.no;
      }
      
      public static function getColor(name0:String) : String
      {
         return colorObj[name0];
      }
      
      public static function getColorOneCn(name0:String) : String
      {
         var cn0:String = getOneCn(name0);
         return ComMethod.color("[" + cn0 + "]",getColor(name0));
      }
      
      public static function getColorCn(name0:String) : String
      {
         return ComMethod.color(getCn(name0),getColor(name0));
      }
      
      public static function getCn(name0:String) : String
      {
         return cnObj[name0];
      }
      
      public static function getOneCn(name0:String) : String
      {
         if(name0 == hit)
         {
            return "动";
         }
         if(name0 == boom)
         {
            return "爆";
         }
         if(name0 == particle)
         {
            return "粒";
         }
         return getCn(name0);
      }
   }
}

