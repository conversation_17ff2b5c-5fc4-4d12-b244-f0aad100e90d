package dataAll._player.count.define
{
   public class CountDefineGroup
   {
      public var obj:Object = {};
      
      public var arr:Array = [];
      
      public function CountDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var d0:CountDefine = null;
         var thingsXML0:* = xml0.count;
         for(n in thingsXML0)
         {
            d0 = new CountDefine();
            d0.inData_byXML(thingsXML0[n]);
            this.obj[d0.name] = d0;
            this.arr.push(d0);
         }
      }
      
      public function getText() : String
      {
         var d0:CountDefine = null;
         var str0:String = "";
         for each(d0 in this.arr)
         {
            str0 += d0.getText();
         }
         return str0;
      }
   }
}

