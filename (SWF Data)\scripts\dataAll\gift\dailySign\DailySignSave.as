package dataAll.gift.dailySign
{
   import com.common.text.TextWay;
   import com.sounto.utils.ClassProperty;
   
   public class DailySignSave
   {
      public static var pro_arr:Array = [];
      
      public var signArr:Array = [];
      
      public var all:Number = 0;
      
      public var giftGetObj:Object = {};
      
      public var vipGiftGetObj:Object = {};
      
      public function DailySignSave()
      {
         super();
      }
      
      public function DailySignData() : *
      {
      }
      
      public function inData_byObj(obj:Object) : *
      {
         ClassProperty.inData_bySaveObj(this,obj,pro_arr);
         this.giftGetObj = ClassProperty.copyObj(obj["giftGetObj"]);
         this.vipGiftGetObj = ClassProperty.copyObj(obj["vipGiftGetObj"]);
      }
      
      public function newDayCtrl() : void
      {
         this.setGetGift("0",false);
         this.setGetVipGift("0",false);
      }
      
      public function clearAll() : void
      {
         this.signArr.length = 0;
         this.giftGetObj = {};
         this.vipGiftGetObj = {};
      }
      
      public function addSign(dataStr0:String) : *
      {
         var str1:String = TextWay.toCode(dataStr0);
         if(this.signArr.indexOf(str1) == -1)
         {
            this.signArr.push(str1);
            ++this.all;
         }
      }
      
      public function getSignNum() : int
      {
         return this.signArr.length;
      }
      
      public function getSignArr() : Array
      {
         return this.dencodeArray(this.signArr);
      }
      
      private function dencodeArray(arr0:Array) : Array
      {
         var n:* = undefined;
         var arr1:Array = [];
         for(n in arr0)
         {
            arr1.push(TextWay.getText(arr0[n]));
         }
         return arr1;
      }
      
      public function todayHaveGetGiftB() : Boolean
      {
         return this.haveGetGift("0");
      }
      
      public function haveGetGift(name0:String) : Boolean
      {
         var str1:String = TextWay.toCode(name0);
         if(this.giftGetObj.hasOwnProperty(str1))
         {
            return this.giftGetObj[str1];
         }
         return false;
      }
      
      public function setGetGift(name0:String, bb0:Boolean) : void
      {
         var str1:String = TextWay.toCode(name0);
         this.giftGetObj[str1] = bb0;
      }
      
      public function haveGetVipGift(name0:String) : Boolean
      {
         var str1:String = TextWay.toCode(name0);
         if(this.vipGiftGetObj.hasOwnProperty(str1))
         {
            return this.vipGiftGetObj[str1];
         }
         return false;
      }
      
      public function setGetVipGift(name0:String, bb0:Boolean) : void
      {
         var str1:String = TextWay.toCode(name0);
         this.vipGiftGetObj[str1] = bb0;
      }
   }
}

