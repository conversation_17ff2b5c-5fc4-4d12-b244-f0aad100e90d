package dataAll._app.peak
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.cf.NumberEncodeObj;
   import com.sounto.utils.ClassProperty;
   
   public class PeakSave
   {
      public static var pro_arr:Array = null;
      
      public static const labelArr:Array = ["","2","3"];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var pointObj:NumberEncodeObj = new NumberEncodeObj();
      
      public var pointObj2:NumberEncodeObj = new NumberEncodeObj();
      
      public var pointObj3:NumberEncodeObj = new NumberEncodeObj();
      
      public var now:String = "";
      
      public function PeakSave()
      {
         super();
         this.lv = 1;
      }
      
      public static function get MAX_LV() : int
      {
         return 120;
      }
      
      public static function get MAX_DAY_EXP() : int
      {
         return 200000000;
      }
      
      public static function get MAX_WHOLE_DPS_NUM() : Number
      {
         return 25;
      }
      
      public static function get WHOLE_DPS_DAY() : Number
      {
         return 2;
      }
      
      public static function get WHOLE_DPS_ONE() : Number
      {
         return 0.005;
      }
      
      public static function get WHOLE_DPS_LV() : int
      {
         return 100;
      }
      
      public static function getWholeDpsMax() : Number
      {
         return MAX_WHOLE_DPS_NUM * WHOLE_DPS_ONE;
      }
      
      public function get lv() : Number
      {
         return this.CF.getAttribute("lv");
      }
      
      public function set lv(v0:Number) : void
      {
         this.CF.setAttribute("lv",v0);
      }
      
      public function get exp() : Number
      {
         return this.CF.getAttribute("exp");
      }
      
      public function set exp(v0:Number) : void
      {
         this.CF.setAttribute("exp",v0);
      }
      
      public function get dayExp() : Number
      {
         return this.CF.getAttribute("dayExp");
      }
      
      public function set dayExp(v0:Number) : void
      {
         this.CF.setAttribute("dayExp",v0);
      }
      
      public function get dpN() : Number
      {
         return this.CF.getAttribute("dpN");
      }
      
      public function set dpN(v0:Number) : void
      {
         this.CF.setAttribute("dpN",v0);
      }
      
      public function get dpDay() : Number
      {
         return this.CF.getAttribute("dpDay");
      }
      
      public function set dpDay(v0:Number) : void
      {
         this.CF.setAttribute("dpDay",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         var v0:Number = this.pointObj.getAttribute("vehicleDefMul");
         if(v0 > 0)
         {
            this.pointObj.setAttribute("vehicleDpsMul",v0);
            this.pointObj.clearName("vehicleDefMul");
         }
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.dayExp = 0;
         this.dpDay = 0;
      }
      
      public function getNow() : NumberEncodeObj
      {
         return this["pointObj" + this.now];
      }
      
      public function swapPlan(label0:String) : Boolean
      {
         if(labelArr.indexOf(label0) >= 0)
         {
            this.now = label0;
            return true;
         }
         return false;
      }
   }
}

