package dataAll._app.union.building
{
   import UI.base.tip.TextGatherAnalyze;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldNiuBiCF;
   import dataAll._app.union.building.cooking.CookingState;
   import dataAll._app.union.building.cooking.UnionCookingData;
   import dataAll._app.union.building.cooking.UnionCookingSave;
   import dataAll._app.union.building.define.UnionCookingDefine;
   import dataAll._app.union.building.define.UnionWatchmenDefine;
   import dataAll._app.union.building.federal.UnionSendTaskDataGroup;
   import dataAll._app.union.building.geology.UnionGeologyData;
   import dataAll._player.IO_PlayerLevelGetter;
   import dataAll._player.PlayerData;
   import dataAll.equip.add.EquipAddChild;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.ui.StateIconData;
   
   public class UnionBuildingDataGroup
   {
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var playerData:PlayerData;
      
      public var saveGroup:UnionBuildingSaveGroup;
      
      public var obj:Object = {};
      
      public var cookingObj:Object = {};
      
      public var sendTask:UnionSendTaskDataGroup = new UnionSendTaskDataGroup();
      
      public var geology:UnionGeologyData = new UnionGeologyData();
      
      public var fleshB:Boolean = false;
      
      private var tempStateData:StateIconData = new StateIconData();
      
      public function UnionBuildingDataGroup()
      {
         super();
         this.allFederalNum = 0;
      }
      
      public function get allFederalNum() : Number
      {
         return this.CF.getAttribute("allFederalNum");
      }
      
      public function set allFederalNum(v0:Number) : void
      {
         this.CF.setAttribute("allFederalNum",v0);
      }
      
      public function inData_bySaveGroup(sg0:UnionBuildingSaveGroup) : void
      {
         var s0:UnionBuildingSave = null;
         var cs0:UnionCookingSave = null;
         var da0:UnionBuildingData = null;
         var cda0:UnionCookingData = null;
         this.saveGroup = sg0;
         this.obj = {};
         for each(s0 in sg0.obj)
         {
            da0 = new UnionBuildingData();
            da0.inData_bySave(s0,this);
            this.obj[da0.name] = da0;
         }
         this.cookingObj = {};
         for each(cs0 in sg0.cookingObj)
         {
            cda0 = new UnionCookingData();
            cda0.inData_bySave(cs0,this.getData("cooking"));
            this.cookingObj[cda0.name] = cda0;
         }
         this.sendTask.inData_bySaveGroup(sg0,this.getData("federal"));
         this.geology.inData_bySave(sg0.geology,this.getData("geology"));
      }
      
      public function newDayCtrl() : void
      {
         this.saveGroup.newDayCtrl();
         this.sendTask.newDayCtrl();
         this.geology.newDayCtrl();
         Gaming.uiGroup.unionTaskBox.fleshDataByUnion();
      }
      
      public function getData(name0:String) : UnionBuildingData
      {
         return this.obj[name0];
      }
      
      private function fleshPlayerDataProAdd() : void
      {
         this.playerData.fleshAllByEquip();
      }
      
      public function getSkillLabelArr() : Array
      {
         var arr0:Array = [];
         return arr0.concat(this.getWatchmenSkillLabelArr());
      }
      
      public function getAddObj() : Object
      {
         var obj0:Object = {};
         obj0 = ComMethod.fixedObj(obj0,this.getWatchmenAddObj());
         return ComMethod.fixedObj(obj0,this.getCookingAddObj());
      }
      
      public function inProAddTipObj(tipObj0:Object) : void
      {
         EquipAddChild.addTipInObj(this.getWatchmenAddObj(),tipObj0,"建筑守望者");
         EquipAddChild.addTipInObj(this.getCookingAddObj(),tipObj0,"建筑炊事馆");
      }
      
      public function getBuildGrowLv(name0:String) : int
      {
         var da0:UnionBuildingData = this.getData(name0);
         if(da0 is UnionBuildingData)
         {
            return da0.save.lv;
         }
         return 1;
      }
      
      public function getBuildLv(name0:String) : int
      {
         var da0:UnionBuildingData = this.getData(name0);
         if(da0 is UnionBuildingData)
         {
            return da0.getBuildingLv();
         }
         return 1;
      }
      
      public function getPlayerStateDataArr() : Array
      {
         var da0:StateIconData = null;
         var cda0:UnionCookingData = this.getNowCookingData();
         if(cda0 is UnionCookingData)
         {
            da0 = this.tempStateData;
            da0.name = cda0.def.name;
            da0.iconUrl = cda0.def.iconUrl + "16";
            da0.time = cda0.save.surplusTime;
            return [da0];
         }
         return [];
      }
      
      public function uplevelOne(da0:UnionBuildingData) : void
      {
         da0.uplevel();
         if(da0.def.fleshAddDataB)
         {
            this.fleshPlayerDataProAdd();
         }
         if(da0 == this.geology.buildData)
         {
            this.geology.fleshDefineByLv();
         }
      }
      
      public function getWatchmenUnlockDefineArr() : Array
      {
         var d0:UnionWatchmenDefine = null;
         var arr0:Array = [];
         var dArr0:Array = Gaming.defineGroup.union.building.watchmenArr;
         for each(d0 in dArr0)
         {
            if(this.panWatchmenUnlock(d0))
            {
               arr0.push(d0);
            }
         }
         return arr0;
      }
      
      private function getWatchmenAddObj() : Object
      {
         var d0:UnionWatchmenDefine = null;
         var obj0:Object = {};
         var arr0:Array = this.getWatchmenUnlockDefineArr();
         for each(d0 in arr0)
         {
            obj0 = ComMethod.fixedObj(obj0,d0.getAddObj());
         }
         return obj0;
      }
      
      private function getWatchmenSkillLabelArr() : Array
      {
         var d0:UnionWatchmenDefine = null;
         var arr0:Array = null;
         var skillName0:String = null;
         var dArr0:Array = Gaming.defineGroup.union.building.watchmenArr;
         var typeObj0:Object = {};
         for each(d0 in dArr0)
         {
            if(this.panWatchmenUnlock(d0))
            {
               skillName0 = d0.getSkillLabel();
               if(skillName0 != "")
               {
                  typeObj0[d0.type] = skillName0;
               }
            }
         }
         return ComMethod.objToArr(typeObj0);
      }
      
      public function panWatchmenUnlock(d0:UnionWatchmenDefine) : Boolean
      {
         var lv0:int = this.getBuildGrowLv("watchmen");
         return lv0 >= d0.unlockLv;
      }
      
      public function getCookingDataArr() : Array
      {
         var d0:UnionCookingDefine = null;
         var arr0:Array = [];
         var dArr0:Array = Gaming.defineGroup.union.building.cookingArr;
         for each(d0 in dArr0)
         {
            arr0.push(this.cookingObj[d0.name]);
         }
         return arr0;
      }
      
      private function getCookingAddObj() : Object
      {
         var cookingDa0:UnionCookingData = null;
         var obj0:Object = {};
         for each(cookingDa0 in this.cookingObj)
         {
            if(cookingDa0.getState() == CookingState.ING)
            {
               obj0 = ComMethod.fixedObj(obj0,cookingDa0.getAddObj());
            }
         }
         return obj0;
      }
      
      public function getNowCookingData() : UnionCookingData
      {
         var cookingDa0:UnionCookingData = null;
         for each(cookingDa0 in this.cookingObj)
         {
            if(cookingDa0.getState() == CookingState.ING)
            {
               return cookingDa0;
            }
         }
         return null;
      }
      
      public function eatOne(da0:UnionCookingData) : void
      {
         this.clearAllHaveEatState();
         da0.startEat();
         ++this.saveGroup.eatNum;
         this.fleshPlayerDataProAdd();
      }
      
      public function getEatCookingDataTip(da0:UnionCookingData) : String
      {
         var nowDa0:UnionCookingData = null;
         var nowArr0:Array = this.getIngCookingDataArr();
         var num0:int = int(nowArr0.length);
         if(num0 >= 1)
         {
            nowDa0 = nowArr0[0];
            return TextGatherAnalyze.swapText("食用后，当前食物效果将覆盖上一个食物<green " + nowDa0.def.cnName + "/>的效果，\n是否继续？");
         }
         return "";
      }
      
      private function getIngCookingDataArr() : Array
      {
         var da0:UnionCookingData = null;
         var arr0:Array = [];
         for each(da0 in this.cookingObj)
         {
            if(da0.getState() == CookingState.ING)
            {
               arr0.push(da0);
            }
         }
         return arr0;
      }
      
      private function clearAllHaveEatState() : void
      {
         var da0:UnionCookingData = null;
         for each(da0 in this.cookingObj)
         {
            if(da0.getState() == CookingState.ING)
            {
               da0.setState(CookingState.NO);
            }
         }
      }
      
      public function getTodayCanEatNum() : int
      {
         var lv0:int = this.getBuildGrowLv("cooking");
         if(lv0 > 0)
         {
            return UnionBuildingCtrl.getDayEatNum() - this.saveGroup.eatNum;
         }
         return 0;
      }
      
      public function setFederalTaskState(v0:int) : void
      {
         this.saveGroup.federalState = v0;
      }
      
      public function getFederalTaskMustNum() : int
      {
         var lv0:int = this.getBuildLv("federal");
         return int(Gaming.defineGroup.union.building.property.getPropertyValue("federalTaskMust",lv0));
      }
      
      public function getFederalTaskGift() : GiftAddDefineGroup
      {
         var num0:int = this.getFederalGiftNowNum();
         var d0:GiftAddDefineGroup = new GiftAddDefineGroup();
         d0.addGiftByStr("things;militarySupplies;" + num0);
         return d0;
      }
      
      public function getFederalGiftMaxNum() : int
      {
         var lv0:int = this.getBuildGrowLv("federal");
         return int(Gaming.defineGroup.union.building.property.getPropertyValue("federalTaskGift",lv0));
      }
      
      public function getFederalGiftNowNum() : int
      {
         var num0:int = this.getFederalGiftMaxNum();
         var mul0:Number = this.allFederalNum / this.getFederalTaskMustNum();
         if(mul0 > 1.6)
         {
            mul0 = 1.6;
         }
         return int(num0 * mul0);
      }
      
      public function FTimerSecond(lg0:IO_PlayerLevelGetter) : void
      {
         var da0:UnionCookingData = null;
         var fleshB0:Boolean = false;
         if(lg0.isOnlyIng())
         {
            for each(da0 in this.cookingObj)
            {
               da0.FTimerSecond();
               if(da0.getState() == CookingState.ING)
               {
                  fleshB0 = true;
               }
            }
         }
         this.fleshB = fleshB0;
         this.sendTask.FTimerSecond();
      }
   }
}

