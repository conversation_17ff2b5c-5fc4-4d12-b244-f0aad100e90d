package dataAll.pay
{
   import UI.api.shop.ShopBuyObject;
   import com.adobe.serialization.json.JSON2;
   import com.sounto.utils.ClassProperty;
   
   public class PaySave
   {
      public static var pro_arr:Array = [];
      
      public static var VER:Number = 1.7;
      
      public var obj:Object = {};
      
      public var ver:Number = 1.5;
      
      public function PaySave()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         var n:* = undefined;
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.obj = ClassProperty.copyObj(obj0["obj"]);
         if(this.ver != VER)
         {
            this.obj = {};
            this.ver = VER;
         }
         var obj2:Object = {};
         for(n in this.obj)
         {
            obj2[String(int(n))] = this.obj[n];
         }
         this.obj = obj2;
      }
      
      public function add(propsId0:String, num0:int) : void
      {
         if(this.obj.hasOwnProperty(propsId0))
         {
            this.obj[propsId0] += num0;
         }
         else
         {
            this.obj[propsId0] = num0;
         }
      }
      
      public function addShopBuyObject(obj0:ShopBuyObject) : void
      {
         if(!obj0)
         {
            return;
         }
         this.add(obj0.propId,obj0.count);
      }
      
      public function getExtra() : String
      {
         return JSON2.encode(this.obj);
      }
      
      public function getNum(propsId0:String) : int
      {
         if(this.obj.hasOwnProperty(propsId0))
         {
            return this.obj[propsId0];
         }
         return 0;
      }
   }
}

