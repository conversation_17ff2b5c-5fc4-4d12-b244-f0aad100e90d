package dataAll._app.vip
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ObjectMethod;
   import dataAll._app.vip.define.VipLevelDefine;
   import dataAll._player.PlayerData;
   import dataAll.equip.add.EquipAddChild;
   import dataAll.equip.add.IO_EquipAddGetter;
   import dataAll.equip.add.IO_EquipAddTipGetter;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class VipData implements IO_EquipAddTipGetter, IO_EquipAddGetter
   {
      public var playerData:PlayerData;
      
      public var save:VipSave;
      
      public var def:VipLevelDefine = new VipLevelDefine();
      
      private var tempTipObj:Object = null;
      
      private var tempMaxObj:Object = null;
      
      public function VipData()
      {
         super();
      }
      
      public function inData_bySave(s0:VipSave) : void
      {
         this.save = s0;
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.save.newDayCtrl(timeStr0);
      }
      
      public function getProAddObj() : Object
      {
         var obj0:Object = {};
         if(this.isVipB())
         {
            obj0.dpsVip = this.def.dpsMul;
            obj0.lifeVip = this.def.lifeMul;
            obj0.headVip = this.def.defenceMul;
            obj0.expVip = this.def.expMul;
         }
         return obj0;
      }
      
      public function getProAddMaxObj(pro0:String) : Object
      {
         var v0:Number = NaN;
         var maxD0:VipLevelDefine = null;
         var obj0:Object = null;
         if(this.tempMaxObj == null)
         {
            this.tempMaxObj = {};
         }
         if(this.tempMaxObj.hasOwnProperty(pro0) == false)
         {
            v0 = 0;
            maxD0 = Gaming.defineGroup.vip.getMaxLevelDefine();
            if(pro0 == "dpsVip")
            {
               v0 = maxD0.dpsMul;
            }
            else if(pro0 == "lifeVip")
            {
               v0 = maxD0.lifeMul;
            }
            else if(pro0 == "headVip")
            {
               v0 = maxD0.defenceMul;
            }
            else if(pro0 == "expVip")
            {
               v0 = maxD0.expMul;
            }
            obj0 = {};
            EquipAddChild.IN_MaxObj(obj0,v0,maxD0.getName());
            this.tempMaxObj[pro0] = obj0;
         }
         return this.tempMaxObj[pro0];
      }
      
      public function getProAddTipObj() : Object
      {
         return this.tempTipObj;
      }
      
      public function isVipB() : Boolean
      {
         return this.def.getTrueLevel() > 0;
      }
      
      public function getDayGiftB() : Boolean
      {
         return ObjectMethod.getObjElementNum(this.save.dayObj) > 0;
      }
      
      public function getNowDayGift() : GiftAddDefineGroup
      {
         var d0:VipLevelDefine = null;
         var gift0:GiftAddDefineGroup = null;
         if(this.save.getDayGiftB(this.def.must))
         {
            return null;
         }
         d0 = this.getVipBeforeDayGetDefine();
         gift0 = this.def.dayGift.clone();
         if(Boolean(d0))
         {
            gift0.deduct(d0.dayGift);
         }
         return gift0;
      }
      
      public function getVipBeforeDayGetDefine() : VipLevelDefine
      {
         var i:int = 0;
         var d0:VipLevelDefine = null;
         var dayGiftB0:Boolean = false;
         var nowIndex0:int = this.def.index;
         if(nowIndex0 > 0)
         {
            for(i = nowIndex0; i >= 0; i--)
            {
               d0 = Gaming.defineGroup.vip.arr[i];
               dayGiftB0 = this.save.getDayGiftB(d0.must);
               if(dayGiftB0)
               {
                  return d0;
               }
            }
         }
         return null;
      }
      
      public function fleshByTotalRecharged(v0:int, tipB0:Boolean = true) : void
      {
         var prev_d0:VipLevelDefine = null;
         var must0:int = 0;
         var upLevelB0:Boolean = false;
         var d0:VipLevelDefine = Gaming.defineGroup.vip.findUnderDefine(v0);
         if(d0 is VipLevelDefine)
         {
            prev_d0 = this.def;
            this.def = d0;
            must0 = this.def.must;
            upLevelB0 = this.save.haveUpLevelB(must0);
            if(!upLevelB0)
            {
               this.upLevelUnderMust(must0);
               this.upLevelEvent(prev_d0,tipB0);
            }
            this.playerData.fleshAllByEquip();
         }
         else
         {
            this.def = new VipLevelDefine();
         }
         this.save.level = this.def.getTrueLevel();
      }
      
      public function fleshBySave() : void
      {
         var lv0:int = this.save.level;
         if(lv0 == 0)
         {
            this.def = new VipLevelDefine();
         }
         else
         {
            this.def = Gaming.defineGroup.vip.getDefineByIndex(lv0 - 1);
         }
      }
      
      private function upLevelEvent(prev_d0:VipLevelDefine, tipB0:Boolean) : void
      {
         var unlockNum0:int = 0;
         if(prev_d0 is VipLevelDefine)
         {
            unlockNum0 = this.def.bag - prev_d0.bag;
            if(unlockNum0 > 0)
            {
               this.playerData.armsBag.saveGroup.unlockNextSiteNum(unlockNum0);
               this.playerData.equipBag.saveGroup.unlockNextSiteNum(unlockNum0);
            }
         }
         Gaming.uiGroup.gameWorldUI.propsNumChangeEvent();
         if(tipB0)
         {
            Gaming.uiGroup.alertBox.showSuccess("您已升级至" + ComMethod.color(this.def.getName(),"#FFFF00") + "，并获得了更高等级的特权！\n更多详情请前往VIP界面查看。");
         }
      }
      
      private function upLevelUnderMust(must0:int) : void
      {
         var n:* = undefined;
         var underMustArr0:Array = Gaming.defineGroup.vip.getMustArrUnder(must0);
         for(n in underMustArr0)
         {
            this.save.upLevel(underMustArr0[n]);
         }
      }
   }
}

