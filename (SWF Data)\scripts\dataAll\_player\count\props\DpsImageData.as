package dataAll._player.count.props
{
   import com.sounto.oldUtils.ComMethod;
   
   public class DpsImageData
   {
      public var name:String = "";
      
      public var value:Number = 0;
      
      public var color:String = "FFFF00";
      
      public var alpha:Number = 1;
      
      public var valueArr:Array = [];
      
      public function DpsImageData()
      {
         super();
      }
      
      public function getStr() : String
      {
         return ComMethod.color(ComMethod.numberToSmall(Math.ceil(this.value)),"#" + this.color);
      }
      
      public function getMax() : Number
      {
         var v0:Number = NaN;
         var max0:Number = 100;
         for each(v0 in this.valueArr)
         {
            if(v0 > max0)
            {
               max0 = v0;
            }
         }
         return max0;
      }
      
      public function getValue(index0:int) : Number
      {
         return DpsCountData.getAveInArr(this.valueArr,index0);
      }
   }
}

