package dataAll._app.book
{
   import dataAll.gift.define.GiftAddDefine;
   
   public interface IO_BookDefine
   {
      function getBookIconUrl(param1:int = 0, param2:int = 0) : String;
      
      function getName() : String;
      
      function getCnName() : String;
      
      function getBookImgUrl() : String;
      
      function getBookInfo() : String;
      
      function getBookId() : String;
      
      function haveCardB() : Boolean;
      
      function getBookCanGet() : Boolean;
      
      function getBookGift() : GiftAddDefine;
   }
}

