package dataAll._app.top.extra
{
   import com.sounto.utils.ClassProperty;
   
   public class BossEditTopExtra extends TopExtra
   {
      public static var pro_arr:Array = null;
      
      public var o:String = "";
      
      public function BossEditTopExtra()
      {
         super();
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      override public function setToSimulated() : void
      {
         this.o = "CgsBBXBuBiXkuKjlvbHmnIjkuKjnrJnmrYwFbXAGE0hvc3BpdGFsNQV2ZQMFY24GDeWIuumqqAVwZQMFZHAEBQV0bQQeA24GF0xhc3RkYXlUYW5rBXNpAwVzawkLAQYJMTNfMQYJMTNfMgYJMTNfMwYJMTNfNAYJMTNfNQV1ZAYXNjA3ODc2MTM4XzIFcHIDBXBhAwVsaQRkAQ==";
      }
   }
}

