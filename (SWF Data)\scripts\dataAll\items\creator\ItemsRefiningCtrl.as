package dataAll.items.creator
{
   import UI.UIOrder;
   import UI.bag.ItemsGripBtnListCtrl;
   import UI.base.tip.TextGatherAnalyze;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldNiuBiCF;
   import dataAll.arms.ArmsData;
   import dataAll.equip.define.EquipColor;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.things.ThingsData;
   import dataAll.things.ThingsDataGroup;
   import dataAll.things.define.ThingsDefine;
   
   public class ItemsRefiningCtrl
   {
      private static var nowDa:IO_ItemsData;
      
      private static var nowDg:ItemsDataGroup;
      
      private static var CF:OldNiuBiCF = new OldNiuBiCF();
      
      private static var nowDaArr:Array = [];
      
      private static var yesRefiningFun:Function = null;
      
      private static var tipString:String = "";
      
      public function ItemsRefiningCtrl()
      {
         super();
      }
      
      public static function get nowNum() : Number
      {
         return CF.getAttribute("nowNum");
      }
      
      public static function set nowNum(v0:Number) : void
      {
         CF.setAttribute("nowNum",v0);
      }
      
      private static function getStoreState(num0:int = 1) : void
      {
         nowNum = num0;
         UIOrder.getStoreState(afterStoreState);
      }
      
      private static function afterStoreState(v0:*) : void
      {
         Gaming.uiGroup.connectUI.hide();
         if(yesRefiningFun is Function)
         {
            yesRefiningFun();
         }
      }
      
      private static function afterSave(v0:* = null) : void
      {
         if(tipString != "")
         {
            Gaming.uiGroup.alertBox.showSuccess(tipString);
         }
      }
      
      public static function refiningDataArr(arr0:Array, dg0:ItemsDataGroup) : void
      {
         var g0:GiftAddDefineGroup = null;
         var bagStr0:String = null;
         if(arr0.length > 0)
         {
            nowDaArr = arr0;
            nowDg = dg0;
            g0 = getGiftByArr(arr0);
            bagStr0 = GiftAddit.bagSpacePan(g0);
            if(bagStr0 != "")
            {
               Gaming.uiGroup.alertBox.showError(bagStr0);
            }
            else
            {
               yesRefiningArr();
            }
         }
      }
      
      private static function yesRefiningArr() : void
      {
         var da0:IO_ItemsData = null;
         var dg0:ItemsDataGroup = nowDg;
         var g0:GiftAddDefineGroup = getGiftByArr(nowDaArr,true);
         dg0.removeDataArr(nowDaArr);
         tipString = g0.arr.length > 0 ? "分解成功！获得：\n" + g0.getDescription(1) : "分解成功！\n不过你啥都没获得。";
         GiftAddit.add(g0,"");
         var haveBlackB0:Boolean = false;
         for each(da0 in nowDaArr)
         {
            if(da0.getColor() == EquipColor.BLACK)
            {
               haveBlackB0 = true;
            }
         }
         if(haveBlackB0)
         {
            UIOrder.save(true,false,false,afterSave);
         }
         else
         {
            afterSave();
         }
      }
      
      public static function refining(da0:IO_ItemsData, dg0:ItemsDataGroup) : void
      {
         var str0:String = null;
         if(da0.getSave().getLockB())
         {
            Gaming.uiGroup.alertBox.showError("已锁物品不能被分解！");
            return;
         }
         nowDa = da0;
         nowDg = dg0;
         var typeCn0:String = da0.getCnType();
         if(da0.getColor() == EquipColor.BLACK)
         {
            str0 = "是否要分解当前" + typeCn0 + "？它有可能\n";
            str0 += "分解出<yellow 钛晶、稀有材料。/>";
         }
         else
         {
            str0 = "是否要分解当前" + typeCn0 + "？每个" + typeCn0 + "可\n";
            str0 += "分解出1个<yellow 钛晶/>。";
         }
         str0 = TextGatherAnalyze.swapText(str0);
         yesRefiningFun = yesRefining;
         Gaming.uiGroup.alertBox.showNormal(str0,"yesAndNo",getStoreStatePan);
      }
      
      private static function getStoreStatePan() : void
      {
         ItemsGripBtnListCtrl.importantCheck(nowDa,"分解",getStoreState);
      }
      
      private static function yesRefining() : void
      {
         var armsData0:ArmsData = null;
         var saveArr0:Array = null;
         var da0:IO_ItemsData = nowDa;
         var dg0:ItemsDataGroup = nowDg;
         var g0:GiftAddDefineGroup = getGift(da0);
         var bagStr0:String = GiftAddit.bagSpacePan(g0);
         if(bagStr0 != "")
         {
            Gaming.uiGroup.alertBox.showError(bagStr0);
         }
         else
         {
            armsData0 = da0 as ArmsData;
            if(Boolean(armsData0))
            {
               if(armsData0.havePartsB())
               {
                  saveArr0 = armsData0.partsData.saveGroup.arr;
                  bagStr0 = Gaming.PG.da.partsBag.bagSpacePanBySaveArr(saveArr0);
                  if(bagStr0 != "")
                  {
                     bagStr0 = "无法分解武器。\n" + bagStr0;
                     Gaming.uiGroup.alertBox.showError(bagStr0);
                     return;
                  }
               }
            }
            doRefining();
         }
      }
      
      private static function doRefining() : void
      {
         var saveArr0:Array = null;
         var da0:IO_ItemsData = nowDa;
         var dg0:ItemsDataGroup = nowDg;
         var armsData0:ArmsData = da0 as ArmsData;
         if(Boolean(armsData0))
         {
            if(armsData0.havePartsB())
            {
               saveArr0 = armsData0.partsData.saveGroup.arr;
               Gaming.PG.da.partsBag.addBySaveArr(saveArr0);
            }
         }
         dg0.removeData(da0);
         var g0:GiftAddDefineGroup = getGift(da0,true);
         tipString = g0.arr.length > 0 ? "分解成功！获得：\n" + g0.getDescription(1) : "分解成功！\n不过你啥都没获得。";
         GiftAddit.add(g0,"");
         if(da0.getColor() == EquipColor.BLACK)
         {
            UIOrder.save(true,false,false,afterSave);
         }
         else
         {
            afterSave();
         }
         Gaming.TG.dat.remove(da0,dg0);
         Gaming.uiGroup.bagUI.fleshNowBox();
         if(dg0.placeType == "wear")
         {
            Gaming.uiGroup.wearUI.fleshAllBox();
         }
      }
      
      private static function getGift(da0:IO_ItemsData, countNumB0:Boolean = false) : GiftAddDefineGroup
      {
         var g0:GiftAddDefineGroup = new GiftAddDefineGroup();
         if(da0.getColor() == EquipColor.BLACK)
         {
            g0 = getChipGift(getBlackChipNum(),null,countNumB0);
         }
         else
         {
            g0.addGiftByStr("things;armsTitanium;1");
         }
         return g0;
      }
      
      public static function getBlackChipNum() : int
      {
         return 400;
      }
      
      private static function getGiftByArr(daArr0:Array, countNumB0:Boolean = false) : GiftAddDefineGroup
      {
         var da0:IO_ItemsData = null;
         var g2:GiftAddDefineGroup = null;
         var g0:GiftAddDefineGroup = new GiftAddDefineGroup();
         for each(da0 in daArr0)
         {
            g2 = getGift(da0,countNumB0);
            g0.merge(g2);
         }
         return g0;
      }
      
      public static function refiningChip(da0:ThingsData, dg0:ThingsDataGroup) : void
      {
         nowDa = da0;
         nowDg = dg0;
         var num0:int = da0.getNowNum();
         var str0:String = "分解当前物品可能获得<yellow 钛晶、稀有材料/>。";
         str0 += "\n<green 请选择物品数量，至少<blue 20个/>，当前最多可选择<yellow " + num0 + "/>个/>";
         str0 = TextGatherAnalyze.swapText(str0);
         yesRefiningFun = yesRefiningChip;
         Gaming.uiGroup.alertBox.showNumChoose(str0,num0,num0,20,1,getStoreState);
         Gaming.uiGroup.alertBox.yesBtn.actived = num0 >= 20;
      }
      
      private static function yesRefiningChip() : void
      {
         var da0:ThingsData = nowDa as ThingsData;
         var dg0:ThingsDataGroup = nowDg as ThingsDataGroup;
         var g0:GiftAddDefineGroup = getChipGift(nowNum,da0,true);
         var bagStr0:String = GiftAddit.bagSpacePan(g0);
         if(bagStr0 != "")
         {
            Gaming.uiGroup.alertBox.showError(bagStr0);
         }
         else
         {
            dg0.useThings(da0.save.name,nowNum,false,false);
            tipString = g0.arr.length > 0 ? "分解成功！获得：\n" + g0.getDescription(1) : "分解成功！\n不过你啥都没获得。";
            GiftAddit.add(g0,"");
            UIOrder.save(true,false,false,afterSave);
            Gaming.uiGroup.bagUI.fleshNowBox();
         }
      }
      
      private static function getChipGift(num0:int, da0:ThingsData = null, countNumB0:Boolean = false) : GiftAddDefineGroup
      {
         var n:* = undefined;
         var index0:int = 0;
         var chipName0:String = null;
         var g0:GiftAddDefineGroup = new GiftAddDefineGroup();
         if(Boolean(da0))
         {
            if(!da0.canRefiningB())
            {
               return g0;
            }
         }
         var obj0:Object = {};
         var blackArmsChipArr0:Array = ["rifleHornet","sniperCicada","shotgunSkunk"];
         var blackPro0:Number = 0.004;
         var refiningBlackNum0:int = Gaming.PG.save.drop.refiningBlackNum;
         if(refiningBlackNum0 > 10)
         {
            blackPro0 *= 0.3;
         }
         else if(refiningBlackNum0 > 5)
         {
            blackPro0 *= 0.6;
         }
         var proArr0:Array = [0.33,blackPro0,0.66];
         for(var i:int = 0; i < num0; i++)
         {
            index0 = ComMethod.getPro_byArrSum(proArr0);
            if(index0 == 0)
            {
               ComMethod.addNumInObj(obj0,"armsTitanium",1);
            }
            else if(index0 == 1)
            {
               if(countNumB0)
               {
                  ++Gaming.PG.save.drop.refiningBlackNum;
               }
               chipName0 = blackArmsChipArr0[int(Math.random() * blackArmsChipArr0.length)];
               ComMethod.addNumInObj(obj0,chipName0,1);
            }
         }
         for(n in obj0)
         {
            g0.addGiftByStr("things;" + n + ";" + obj0[n]);
         }
         return g0;
      }
      
      public static function refiningParstChest(da0:ThingsData, dg0:ThingsDataGroup) : void
      {
         var gd0:GiftAddDefine = null;
         var str0:String = null;
         nowDa = da0;
         nowDg = dg0;
         var d0:ThingsDefine = da0.save.getDefine();
         var num0:int = da0.getNowNum();
         var g0:GiftAddDefineGroup = d0.getPartsChestRefiningGift();
         if(Boolean(g0))
         {
            gd0 = g0.arr[0];
            str0 = "分解1个当前物品可能获得<yellow " + gd0.getDescription(false) + "/>。";
            str0 += "\n<green 请选择物品数量，当前最多可选择<yellow " + num0 + "/>个/>";
            str0 = TextGatherAnalyze.swapText(str0);
            Gaming.uiGroup.alertBox.showNumChoose(str0,1,num0,1,1,yesRefiningParstChest);
            Gaming.uiGroup.alertBox.yesBtn.actived = num0 >= 1;
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("当前物品无法分解。");
         }
      }
      
      private static function yesRefiningParstChest(num0:int = 1) : void
      {
         var da0:ThingsData = nowDa as ThingsData;
         var dg0:ThingsDataGroup = nowDg as ThingsDataGroup;
         var d0:ThingsDefine = da0.save.getDefine();
         var g0:GiftAddDefineGroup = d0.getPartsChestRefiningGift(num0);
         var bagStr0:String = GiftAddit.bagSpacePan(g0);
         if(bagStr0 != "")
         {
            Gaming.uiGroup.alertBox.showError(bagStr0);
         }
         else
         {
            dg0.useThings(da0.save.name,num0,false,false);
            Gaming.uiGroup.alertBox.showSuccess("分解成功！获得：\n" + g0.getDescription(1));
            GiftAddit.add(g0,"");
            Gaming.uiGroup.bagUI.fleshNowBox();
         }
      }
   }
}

