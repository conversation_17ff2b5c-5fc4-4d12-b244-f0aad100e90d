package dataAll._app.wilder.define
{
   import dataAll._base.BaseDefineGroup;
   
   public class WilderDefineGroup extends BaseDefineGroup
   {
      public var fatherObj:Object = {};
      
      public var fatherArr:Array = [];
      
      private var indexNum:int = 0;
      
      private var maxId:int = 0;
      
      public function WilderDefineGroup()
      {
         super();
         defineClass = WilderDefine;
      }
      
      override protected function addFatherByXml(xml0:XML) : void
      {
         var d0:WilderFatherDefine = new WilderFatherDefine();
         d0.inData_byXML(xml0);
         this.fatherObj[d0.name] = d0;
         this.fatherArr.push(d0);
      }
      
      override protected function addDefine(d0:Object, father0:String) : void
      {
         super.addDefine(d0,father0);
         var d2:WilderDefine = d0 as WilderDefine;
         d2.index = this.indexNum;
         ++this.indexNum;
         var f0:WilderFatherDefine = this.getFather(father0);
         f0.wilderArr.push(d0);
         if(d2.id > this.maxId)
         {
            this.maxId = d2.id;
         }
      }
      
      public function getDefine(name0:String) : WilderDefine
      {
         return obj[name0];
      }
      
      public function getFather(name0:String) : <PERSON>FatherDefine
      {
         return this.fatherObj[name0];
      }
      
      public function getMaxId() : int
      {
         return this.maxId;
      }
      
      public function unlockAllWider() : void
      {
         var d0:WilderDefine = null;
         for each(d0 in obj)
         {
            d0.openTime = Gaming.api.save.getNowServerDate().toString();
            d0.day = 99999;
         }
      }
      
      public function testId() : String
      {
         var d0:WilderDefine = null;
         var i:int = 0;
         var id0:int = 0;
         var idObj0:Object = {};
         var repeatArr0:Array = [];
         var noArr0:Array = [];
         for each(d0 in obj)
         {
            id0 = d0.id;
            if(id0 > 0)
            {
               if(idObj0.hasOwnProperty(id0))
               {
                  repeatArr0.push(id0);
               }
               else
               {
                  idObj0[id0] = d0;
               }
            }
         }
         for(i = 1; i <= this.maxId; i++)
         {
            if(!idObj0.hasOwnProperty(id0))
            {
               noArr0.push(id0);
            }
         }
         var tip0:String = "";
         if(repeatArr0.length > 0)
         {
            tip0 += "重复的编号：" + repeatArr0;
         }
         if(noArr0.length > 0)
         {
            tip0 += "\n缺少的编号：" + noArr0;
         }
         return tip0;
      }
   }
}

