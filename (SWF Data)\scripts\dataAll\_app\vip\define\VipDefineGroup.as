package dataAll._app.vip.define
{
   import com.sounto.utils.TextMethod;
   
   public class VipDefineGroup
   {
      public var arr:Array = [];
      
      public var obj:Object = {};
      
      public function VipDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var i:* = undefined;
         var barXML0:XML = null;
         var d0:VipLevelDefine = null;
         var xml_list0:XMLList = xml0.level;
         for(i in xml_list0)
         {
            barXML0 = xml_list0[i];
            d0 = new VipLevelDefine();
            d0.inData_byXML(barXML0);
            d0.index = i;
            this.arr.push(d0);
            this.obj[d0.must] = d0;
         }
      }
      
      public function getDefineByIndex(i0:int) : VipLevelDefine
      {
         return this.arr[i0];
      }
      
      public function findUnderDefine(totalRecharged0:Number) : VipLevelDefine
      {
         var before_d0:VipLevelDefine = null;
         var n:* = undefined;
         var d0:VipLevelDefine = null;
         for(n in this.arr)
         {
            d0 = this.arr[n];
            if(d0.must > totalRecharged0)
            {
               break;
            }
            before_d0 = d0;
         }
         return before_d0;
      }
      
      public function getMustArrUnder(v0:int) : Array
      {
         var n:* = undefined;
         var d0:VipLevelDefine = null;
         var arr0:Array = [];
         for(n in this.arr)
         {
            d0 = this.arr[n];
            if(d0.must <= v0)
            {
               arr0.push(d0.must);
            }
         }
         return arr0;
      }
      
      public function getNextDefineByTrueLevel(lv0:int) : VipLevelDefine
      {
         return this.arr[lv0];
      }
      
      public function getPrevDefineByTrueLevel(lv0:int) : VipLevelDefine
      {
         return this.arr[lv0 - 2];
      }
      
      public function getMaxLevelDefine() : VipLevelDefine
      {
         return this.arr[this.arr.length - 1];
      }
      
      public function getVipTip(proName0:String, baseV0:Number, addV0:Number, nowLv0:int = -1) : String
      {
         var d0:VipLevelDefine = null;
         var s0:String = "无VIP   " + baseV0 + "次";
         for each(d0 in this.arr)
         {
            s0 += "\n" + d0.getLabelString() + "   " + (d0.getValueByName(proName0) + addV0) + "次";
            if(nowLv0 == d0.getTrueLevel())
            {
               s0 += TextMethod.color(" √","#00FF00");
            }
         }
         return s0;
      }
   }
}

