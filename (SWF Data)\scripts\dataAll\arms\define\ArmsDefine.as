package dataAll.arms.define
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.StringMethod;
   import dataAll._app.book.IO_BookDefine;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._app.goods.define.PriceType;
   import dataAll._app.parts.PartsAddData;
   import dataAll._app.parts.PartsCreator;
   import dataAll._base.IO_TipDefine;
   import dataAll.arms.*;
   import dataAll.arms.bookGet.ArmsBookGetter;
   import dataAll.arms.creator.*;
   import dataAll.arms.save.ArmsSave;
   import dataAll.body.attack.HurtKind;
   import dataAll.bullet.*;
   import dataAll.equip.define.EquipColor;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.IO_GiftDefine;
   import dataAll.items.define.IO_ComposeItemsDefine;
   import dataAll.items.define.IO_ItemsDefine;
   import dataAll.pro.PropertyArrayDefine;
   import dataAll.skill.define.SkillDefine;
   import dataAll.things.define.ThingsDefine;
   import dataAll.ui.text.ProTipType;
   import dataAll.ui.tip.CheckData;
   
   public class ArmsDefine extends BulletDefine implements IO_ComposeItemsDefine, IO_GiftDefine, IO_ItemsDefine, IO_TipDefine, IO_BookDefine
   {
      public static var TEXTURE_ARR:Array = [];
      
      public static var T_TEXTURE_ARR:Array = [];
      
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var randomPro:Number = 0;
      
      public var index:int = 1;
      
      public var rareDropLevel:int = 1;
      
      public var dropLevelArr:Array = [];
      
      public var color:String = "";
      
      public var dropBodyArr:Array = [];
      
      public var chipB:Boolean = false;
      
      public var dpsMul:Number = 1;
      
      public var uiDpsMul:Number = 1;
      
      public var extraMul:Number = 1;
      
      public var capacity:Number = 0;
      
      public var armsWeight:Number = 0;
      
      public var reloadGap:Number = 0;
      
      public var noShakeTime:Number = 0;
      
      public var gunNum:int = 1;
      
      public var armsArmMul:Number = 0.5;
      
      public var handShootGap:Number = 0;
      
      public var upValue:int = 0;
      
      public var focusAngleRange:Number = 0;
      
      public var leftHandImg:int = 0;
      
      public var shootShakeAngle:Number = 0;
      
      public var crossbowD:ArmsCrossbowDefine = new ArmsCrossbowDefine();
      
      public var recordD:ArmsRecordDefine = new ArmsRecordDefine();
      
      public var twoShootPro:Number = 0;
      
      public var armsImgLabel:String = "";
      
      private var imgOneB:Boolean = false;
      
      public var iconUrl:String = "";
      
      public var allImgPartArr:Array = [];
      
      public var allImgRange:Array = [];
      
      public var textureImgRange:Array = [];
      
      public var bodyImgRange:Array = [];
      
      public var barrelImgRange:Array = [];
      
      public var gripImgRange:Array = [];
      
      public var bulletImgRange:Array = [];
      
      public var stockImgRange:Array = [];
      
      public var glassImgRange:Array = [];
      
      public var fireImgType:String = "";
      
      public var shootSoundUrl:String = "";
      
      public var description:String = "";
      
      public var info:String = "";
      
      private var zodiacB:uint = 0;
      
      private var bookCanGetB:Boolean = false;
      
      public function ArmsDefine()
      {
         super();
         this.chipNum = 0;
         this.composeLv = 81;
         this.composeMax = 0;
         this.evoMaxLv = 0;
         this.evoMustFirstLv = 0;
      }
      
      public function get evoMaxLv() : Number
      {
         return this.CF.getAttribute("evoMaxLv");
      }
      
      public function set evoMaxLv(v0:Number) : void
      {
         this.CF.setAttribute("evoMaxLv",v0);
      }
      
      public function get evoMustFirstLv() : Number
      {
         return this.CF.getAttribute("evoMustFirstLv");
      }
      
      public function set evoMustFirstLv(v0:Number) : void
      {
         this.CF.setAttribute("evoMustFirstLv",v0);
      }
      
      public function get chipNum() : Number
      {
         return this.CF.getAttribute("chipNum");
      }
      
      public function set chipNum(v0:Number) : void
      {
         this.CF.setAttribute("chipNum",v0);
      }
      
      public function get composeLv() : Number
      {
         return this.CF.getAttribute("composeLv");
      }
      
      public function set composeLv(v0:Number) : void
      {
         this.CF.setAttribute("composeLv",v0);
      }
      
      public function get composeMax() : Number
      {
         return this.CF.getAttribute("composeMax");
      }
      
      public function set composeMax(v0:Number) : void
      {
         this.CF.setAttribute("composeMax",v0);
      }
      
      public function inData_bySaveObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      override public function inData_byXML(xml0:XML) : void
      {
         var i:* = undefined;
         var part_name0:String = null;
         var arr_name0:String = null;
         var xx0:int = 0;
         super.inData_byXML(xml0);
         this.index = int(xml0.@index);
         this.rareDropLevel = int(xml0.@rareDropLevel);
         this.color = String(xml0.@color);
         this.dropLevelArr = String(xml0.@dropLevelArr).split(",");
         if(String(xml0.@dropBodyArr) != "")
         {
            this.dropBodyArr = String(xml0.@dropBodyArr).split(",");
         }
         this.evoMaxLv = int(xml0.@evoMaxLv);
         this.evoMustFirstLv = int(xml0.@evoMustFirstLv);
         this.chipB = Boolean(int(xml0.@chipB));
         this.chipNum = int(xml0.@chipNum);
         this.composeLv = int(xml0.@composeLv);
         if(this.composeLv == 0)
         {
            this.composeLv = 81;
         }
         this.composeMax = int(xml0.@composeMax);
         this.randomPro = Number(xml0.randomPro);
         if(String(xml0.randomPro) == "")
         {
            this.randomPro = 0;
         }
         this.dpsMul = Number(xml0.dpsMul);
         if(this.dpsMul == 0)
         {
            this.dpsMul = 1;
         }
         this.uiDpsMul = Number(xml0.uiDpsMul);
         if(this.uiDpsMul == 0)
         {
            this.uiDpsMul = 1;
         }
         this.extraMul = Number(xml0.extraMul);
         if(this.extraMul == 0)
         {
            this.extraMul = 1;
         }
         this.capacity = int(xml0.capacity);
         this.armsWeight = Number(xml0.armsWeight);
         this.reloadGap = Number(xml0.reloadGap);
         this.gunNum = int(xml0.gunNum);
         if(this.gunNum == 0)
         {
            this.gunNum = 1;
         }
         this.armsArmMul = Number(xml0.armsArmMul);
         if(this.armsArmMul == 0)
         {
            this.armsArmMul = 0.5;
         }
         this.handShootGap = int(xml0.handShootGap);
         this.upValue = int(xml0.upValue);
         this.focusAngleRange = Number(xml0.focusAngleRange);
         this.leftHandImg = int(xml0.leftHandImg);
         this.shootShakeAngle = Number(xml0.shootShakeAngle);
         this.twoShootPro = Number(xml0.twoShootPro);
         this.crossbowD.inData_byXML(xml0.crossbowD[0]);
         this.recordD.inData_byXML(xml0.recordD[0]);
         this.iconUrl = String(xml0.iconUrl);
         this.armsImgLabel = String(xml0.armsImgLabel);
         this.fireImgType = String(xml0.fireImgType);
         this.shootSoundUrl = String(xml0.shootSoundUrl);
         this.description = String(xml0.description);
         this.info = String(xml0.info);
         if(xml0.allImgPartArr.length() == 0)
         {
            this.allImgPartArr = GunPart.DEFINE_ARR;
         }
         else
         {
            this.allImgPartArr = String(xml0.allImgPartArr).split(",");
         }
         this.allImgRange = String(xml0.allImgRange).split(",");
         var p_arr0:Array = GunPart.ARR;
         for(i in p_arr0)
         {
            part_name0 = p_arr0[i];
            arr_name0 = part_name0 + "ImgRange";
            if(String(xml0[arr_name0]) == "")
            {
               this[arr_name0] = [];
            }
            else
            {
               this[arr_name0] = String(xml0[arr_name0]).split(",");
            }
         }
         this.imgOneB = this.allImgRange[0] == "" && this.barrelImgRange.length == 0 && this.gripImgRange.length == 0;
         if(this.imgOneB)
         {
         }
         this.fleshImgRange();
         if(bulletAngle > -100)
         {
            xx0 = 0;
         }
      }
      
      public function setArmsType(type0:String) : void
      {
         armsType = type0;
         if(kind == HurtKind.no)
         {
            kind = ArmsType.getHurtKind(armsType);
         }
      }
      
      public function getTypeCn() : String
      {
         return "武器";
      }
      
      private function fleshImgRange() : void
      {
         var n:* = undefined;
         var swf_name0:String = null;
         var p_arr0:Array = null;
         var i:* = undefined;
         var part_name0:String = null;
         var m_arr0:Array = null;
         var url0:String = null;
         for(n in this.allImgRange)
         {
            swf_name0 = this.allImgRange[n];
            if(!(!swf_name0 || swf_name0 == ""))
            {
               p_arr0 = this.allImgPartArr;
               for(i in p_arr0)
               {
                  part_name0 = p_arr0[i];
                  m_arr0 = this[part_name0 + "ImgRange"];
                  url0 = swf_name0 + "/" + part_name0;
                  m_arr0.push(url0);
               }
            }
         }
      }
      
      public function isImgOne() : Boolean
      {
         return this.imgOneB;
      }
      
      public function isMoreRedB() : Boolean
      {
         var index0:int = EquipColor.getIndex(this.color);
         if(index0 >= 5)
         {
            return true;
         }
         return false;
      }
      
      public function isCanEvoB() : Boolean
      {
         return this.evoMaxLv > 0;
      }
      
      public function getDpsMul() : Number
      {
         return this.dpsMul * ArmsType.getTrueDpsMul(this.color,armsType);
      }
      
      public function getChipName() : String
      {
         return name;
      }
      
      public function canResolveB() : Boolean
      {
         return this.chipB;
      }
      
      public function haveChipB() : Boolean
      {
         return this.chipNum > 0;
      }
      
      public function isPianoB() : Boolean
      {
         return this.recordD.piano != "";
      }
      
      public function getPianoMaxLen() : int
      {
         return 40;
      }
      
      public function isZodiacB() : Boolean
      {
         if(this.zodiacB == 0)
         {
            this.zodiacB = name.indexOf("year") == 0 ? 1 : 2;
         }
         if(this.zodiacB == 1)
         {
            return true;
         }
         return false;
      }
      
      public function isConsB() : Boolean
      {
         return name.indexOf("cons") == 0;
      }
      
      public function isUltiB() : Boolean
      {
         return ArmsName.ultiArr.indexOf(name) >= 0;
      }
      
      public function getRandomImageLabel(color0:String, attackSpeed_p:Number, capacity_p:Number) : String
      {
         var i:* = undefined;
         var part_name0:String = null;
         var label0:String = null;
         var skillTexture0:String = null;
         var t_pro0:Number = NaN;
         var t_arr0:Array = null;
         var m_arr0:Array = null;
         var pro2:Number = NaN;
         var p_arr0:Array = GunPart.ARR;
         var name0:String = "";
         for(i in p_arr0)
         {
            part_name0 = p_arr0[i];
            label0 = "0";
            if(part_name0 == "texture")
            {
               skillTexture0 = "";
               if(skillTexture0 != "")
               {
                  label0 = skillTexture0;
               }
               else
               {
                  t_pro0 = 0.95;
                  if(color0 == "purple" || color0 == "orange" || color0 == "red")
                  {
                     t_pro0 = 0.1;
                  }
                  t_arr0 = this.textureImgRange;
                  if(this.textureImgRange.length == 0)
                  {
                     if(Math.random() < t_pro0)
                     {
                        t_arr0 = TEXTURE_ARR;
                     }
                     else
                     {
                        t_arr0 = T_TEXTURE_ARR;
                     }
                  }
                  label0 = t_arr0[ComMethod.gotoIndex(t_arr0.length * capacity_p,t_arr0.length)];
               }
               label0 = label0.replace("/","$");
               name0 = label0;
            }
            else
            {
               m_arr0 = this[part_name0 + "ImgRange"];
               if(m_arr0.length > 0)
               {
                  pro2 = Math.random();
                  if(part_name0 == "barrel")
                  {
                     pro2 = attackSpeed_p;
                  }
                  label0 = m_arr0[ComMethod.gotoIndex(m_arr0.length * pro2,m_arr0.length)];
               }
               label0 = label0.replace("/","$");
               if(i == 0)
               {
                  name0 = label0;
               }
               else
               {
                  name0 += "_" + label0;
               }
            }
         }
         return name0;
      }
      
      public function getImageLabelByArrArr(arr0:Array) : String
      {
         var i:* = undefined;
         var part_name0:String = null;
         var label0:String = null;
         var rarr0:Array = null;
         var p_arr0:Array = GunPart.ARR;
         var name0:String = "";
         for(i in p_arr0)
         {
            part_name0 = p_arr0[i];
            label0 = "0";
            rarr0 = arr0[i];
            if(part_name0 == "texture")
            {
               if(!rarr0)
               {
                  label0 = TEXTURE_ARR[0];
               }
               else
               {
                  label0 = rarr0[int(Math.random() * rarr0.length)];
               }
            }
            else if(Boolean(rarr0))
            {
               label0 = rarr0[int(Math.random() * rarr0.length)];
            }
            label0 = label0.replace("/","$");
            if(i == 0)
            {
               name0 = label0;
            }
            else
            {
               name0 += "_" + label0;
            }
         }
         return name0;
      }
      
      public function getImageLabelByBodySuffix(suffix:String) : String
      {
         var bodyName0:String = this.getFirstBodyImgUrl(suffix);
         return this.getImageLabelByArrArr([null,[bodyName0],null,null,this.bulletImgRange]);
      }
      
      public function getFirstBodyImgUrl(suffix:String) : String
      {
         return this.bodyImgRange[0] + suffix;
      }
      
      public function getTrueReloadGap() : Number
      {
         if(this.reloadGap < attackGap)
         {
            return attackGap;
         }
         return this.reloadGap;
      }
      
      public function haveSpecialB() : Boolean
      {
         return penetrationGap > 0 || penetrationNum > 0 || bounceD.floor > 0 || bounceD.body > 0 || critD.mul > 0 || this.twoShootPro > 0;
      }
      
      public function getArmsTypeCn() : String
      {
         var d0:ArmsChargerDefine = Gaming.defineGroup.armsCharger.getDefine(armsType);
         if(!d0)
         {
            INIT.showError("找不到ArmsChargerDefine定义：" + armsType);
         }
         var str0:String = d0.cnName;
         if(this.gunNum == 2)
         {
            str0 += ComMethod.color("(双枪)","#00FF00");
         }
         return str0;
      }
      
      private function getTipDefine() : ArmsTipDefine
      {
         var obj0:ArmsTipDefine = new ArmsTipDefine();
         obj0.hurtRatio = this.hurtRatio * bulletNum;
         obj0.shootSpeed = 1 / attackGap;
         obj0.capacity = this.capacity;
         obj0.reloadGap = this.reloadGap;
         obj0.shootWidth = getShowShootRange();
         obj0.precision = getOnlyAnglePrecision();
         return obj0;
      }
      
      public function getGatherTip(cDa0:ArmsDefine = null, compactB0:Boolean = false, showArmsSkillB:Boolean = true, tipType0:String = "no") : String
      {
         var n:* = undefined;
         var pro0:String = null;
         var pd0:PropertyArrayDefine = null;
         var v0:Number = NaN;
         var lastIcon0:String = null;
         var v_str0:String = null;
         var rangeStr0:String = null;
         var v2:Number = NaN;
         var aiShootLen0:Number = NaN;
         var pstr0:String = null;
         var trueDa0:ArmsData = this as ArmsData;
         var rangeD0:ArmsRangeDefine = Gaming.defineGroup.bullet.getRangeOnlyRange(name);
         var str0:String = "";
         str0 += "<i1>|<blue <b>属性：</b>/>";
         if(!compactB0)
         {
            str0 += "\n武器类型|" + this.getArmsTypeCn();
            if(this.isRandomB())
            {
               str0 += "<gray2 (随机)/>";
            }
            if(kind != HurtKind.no)
            {
               str0 += HurtKind.getColorOneCn(kind);
            }
         }
         var td0:ArmsTipDefine = this.getTipDefine();
         var td2:ArmsTipDefine = null;
         if(Boolean(cDa0))
         {
            td2 = cDa0.getTipDefine();
         }
         var pro_arr0:Array = ArmsTipDefine.pro_arr;
         for(n in pro_arr0)
         {
            pro0 = pro_arr0[n];
            pd0 = ArmsTipDefine.getDefine(pro0);
            v0 = pd0.fixedNumber(td0[pro0]);
            lastIcon0 = "";
            if(Boolean(td2))
            {
               v2 = pd0.fixedNumber(td2[pro0]);
               if(v0 > v2)
               {
                  lastIcon0 = pd0.bigBestB ? "|<i6>" : "|<i7>";
               }
               else if(v0 < v2)
               {
                  lastIcon0 = pd0.bigBestB ? "|<i7>" : "|<i6>";
               }
            }
            v_str0 = pd0.getValueString(v0);
            if(pro0 == "hurtRatio")
            {
               if(bulletNum > 1)
               {
                  v_str0 = pd0.getFixedValueString(this.hurtRatio) + "x" + bulletNum;
               }
            }
            else if(pro0 == "shootWidth")
            {
               aiShootLen0 = getAIShootRange();
               v_str0 += ComMethod.graydrak("(" + ComMethod.color("AI:","#0099CC") + NumberMethod.toFixed(aiShootLen0,0) + ")");
            }
            rangeStr0 = "";
            if(Boolean(rangeD0))
            {
               if(showArmsSkillB)
               {
                  rangeStr0 = rangeD0.getRangeGather(pro0);
               }
            }
            str0 += "\n<" + pd0.gatherColor + " " + pd0.cnName + "/>|<" + pd0.gatherColor + " " + v_str0 + "/>" + rangeStr0 + lastIcon0;
         }
         if(ArmsTipType.specialB(tipType0))
         {
            if(this.haveSpecialB() || trueDa0.haveElementB())
            {
               str0 += "\n<i1>|<blue <b>特殊属性</b>：/>";
               if(Boolean(trueDa0))
               {
                  str0 += trueDa0.getElementGather();
               }
               str0 += ArmsSpecialAndSkill.getSpecialTip(this);
            }
         }
         var partsAddDa0:PartsAddData = this.getPartsAddData();
         if(Boolean(partsAddDa0))
         {
            if(partsAddDa0.haveRareB())
            {
               pstr0 = PartsCreator.getRareGather(partsAddDa0,"",Boolean(cDa0) ? cDa0.getPartsAddData() : null,this,tipType0);
               if(pstr0 != "")
               {
                  str0 += "\n<i1>|<blue <b>稀零属性</b>：/>";
                  str0 += "\n" + pstr0;
               }
            }
         }
         if(ArmsTipType.skillB(tipType0))
         {
            if(skillArr.length > 0)
            {
               if(!compactB0)
               {
                  str0 += "\n<i1>|<blue <b>技能：</b>/>\n";
               }
               else
               {
                  str0 += "\n<i1>|<blue <b>技能：</b>/>";
               }
               str0 += ArmsSpecialAndSkill.getAllSkillTip(skillArr,compactB0 || !showArmsSkillB);
            }
            if(godSkillArr.length > 0)
            {
               str0 += "\n<red <b>神级技能：</b>/>";
               if(!compactB0)
               {
                  str0 += "\n";
               }
               str0 += ArmsSpecialAndSkill.getAllSkillTip(godSkillArr,compactB0 || !showArmsSkillB);
            }
         }
         return str0;
      }
      
      override public function get hurtRatio() : Number
      {
         return Math.round(_hurtRatio * V);
      }
      
      public function getGetMethodStr() : String
      {
         if(this.description != "")
         {
            return this.description;
         }
         if(this.dropLevelArr.length > 0)
         {
            if(this.dropLevelArr[0] != "" && this.dropLevelArr[0] != "999")
            {
               return "消灭" + String(this.dropLevelArr).replace(",","、") + "级普通关卡首领有几率掉落";
            }
         }
         return "";
      }
      
      public function getPartsAddData() : PartsAddData
      {
         return null;
      }
      
      public function isRandomB() : Boolean
      {
         return this.randomPro > 0 || name == "shotgunLock";
      }
      
      public function getComposeThingsDefine() : ThingsDefine
      {
         return Gaming.defineGroup.things.getDefine(name);
      }
      
      public function getComposeMustBlack() : Number
      {
         return this.chipNum;
      }
      
      public function getBookIconUrl(w0:int = 0, h0:int = 0) : String
      {
         return this.getBookImgUrl();
      }
      
      public function getBookImgUrl() : String
      {
         return this.bodyImgRange[0] + "Bmp";
      }
      
      public function getBookInfo() : String
      {
         var str0:String = "";
         str0 += ProTipType.getBookTitleMixed("属性");
         str0 += "\n武器类型：" + this.getArmsTypeCn();
         if(kind != HurtKind.no)
         {
            str0 += "\n伤害类型：" + HurtKind.getColorCn(kind);
         }
         if(this.color != "")
         {
            str0 += "\n成色：" + EquipColor.getColorCn(this.color);
         }
         if(this.haveChipB() && this.composeLv > 0)
         {
            str0 += "\n等级：" + this.composeLv + "级";
         }
         if(this.isCanEvoB())
         {
            str0 += "\n<blueness>可进化至第" + this.evoMaxLv + "级（" + ArmsEvoCtrl.getFirstCn(this.evoMaxLv,this) + "）</>";
         }
         if(this.info != "")
         {
            str0 += "\n" + ComMethod.yellow(this.info);
         }
         str0 += this.getSpecialSkillBookInfo();
         str0 += "\n\n" + ProTipType.getBookTitleMixed("获得方式");
         return str0 + this.getBookGetMothedStr();
      }
      
      private function getBookGetMothedStr() : String
      {
         var thingsD0:ThingsDefine = null;
         var chipGoodsArr0:Array = null;
         var goodsD0:GoodsDefine = null;
         var s0:String = this.fleshBookGetter();
         if(this.haveChipB())
         {
            thingsD0 = Gaming.defineGroup.things.getDefine(this.getChipName());
            s0 += "\n·" + thingsD0.cnName + "合成";
            s0 = StringMethod.addNewLine(s0,this.getGetMethodStr(),"  ·");
            chipGoodsArr0 = Gaming.defineGroup.goods.getDefineArrByDefineLabel(this.getChipName());
            if(chipGoodsArr0.length > 0)
            {
               for each(goodsD0 in chipGoodsArr0)
               {
                  if(goodsD0.inBagType == "things" && goodsD0.priceType != PriceType.TAX_STAMP)
                  {
                     s0 += "\n  ·" + PriceType.getMethodCn(goodsD0.priceType);
                  }
               }
            }
         }
         else
         {
            s0 = StringMethod.addNewLine(s0,this.getGetMethodStr(),"\n·");
         }
         var goodsArr0:Array = Gaming.defineGroup.goods.getDefineArrByDefineLabel(name);
         for each(goodsD0 in goodsArr0)
         {
            if(goodsD0.inBagType != "things" && goodsD0.priceType != PriceType.TAX_STAMP)
            {
               s0 += "\n·" + PriceType.getMethodCn(goodsD0.priceType);
            }
         }
         return s0;
      }
      
      private function getSpecialSkillBookInfo() : String
      {
         var name0:* = null;
         var d0:SkillDefine = null;
         var s0:String = null;
         var str0:String = null;
         var skillS0:String = "";
         for each(name0 in godSkillArr)
         {
            d0 = Gaming.defineGroup.skill.getDefine(name0);
            if(d0.noRandomListB)
            {
               s0 = d0.cnName + "：" + d0.getDescription();
               skillS0 += "\n" + s0;
            }
         }
         if(skillS0.length > 0)
         {
            str0 = "\n\n" + ProTipType.getBookTitleMixed("特别技能");
            return str0 + skillS0;
         }
         return "";
      }
      
      public function getBookId() : String
      {
         return name;
      }
      
      public function haveCardB() : Boolean
      {
         return false;
      }
      
      private function fleshBookGetter() : String
      {
         var id0:String = this.getBookId();
         var c0:CheckData = ArmsBookGetter.getCheckById(id0);
         if(Boolean(c0))
         {
            this.bookCanGetB = c0.bb;
            return "\n" + c0.info;
         }
         this.bookCanGetB = false;
         return "";
      }
      
      public function getBookCanGet() : Boolean
      {
         return this.bookCanGetB;
      }
      
      public function getBookGift() : GiftAddDefine
      {
         var gift0:GiftAddDefine = new GiftAddDefine();
         gift0.inData_byStr("arms;" + name + ";1");
         return gift0;
      }
      
      public function getPathType() : String
      {
         if(name == "greedySnake")
         {
            return ArmsType.LINE;
         }
         if(name == "flySnake")
         {
            return ArmsType.POINT;
         }
         return ArmsType.LINE;
      }
      
      public function getPathPointMax() : int
      {
         var type0:String = this.getPathType();
         if(type0 == ArmsType.LINE)
         {
            return bulletLife * 30;
         }
         if(type0 == ArmsType.POINT)
         {
            return bulletNum;
         }
         return 0;
      }
      
      public function getPathPoint2(i0:int) : Array
      {
         return null;
      }
      
      public function setValueByPro(pro0:String, v0:*) : void
      {
         this[pro0] = v0;
      }
      
      public function getTipStr() : String
      {
         return "";
      }
      
      public function traceSavePro() : void
      {
         var n:* = undefined;
         var name0:String = null;
         var v0:* = undefined;
         INIT.TRACE(name + " ============= ");
         var pro_arr:Array = ArmsSave.pro_arr;
         for(n in pro_arr)
         {
            name0 = pro_arr[n];
            v0 = this[n];
            if(isNaN(v0))
            {
               INIT.TRACE(name0 + ":" + v0);
            }
         }
      }
      
      public function isOutB() : Boolean
      {
         return Gaming.defineGroup.bullet.getOutDef(name);
      }
      
      public function getGiftCn() : String
      {
         return cnName;
      }
      
      public function getGiftTip() : String
      {
         return this.getGatherTip();
      }
      
      public function getGiftIconUrl() : String
      {
         return this.iconUrl;
      }
   }
}

