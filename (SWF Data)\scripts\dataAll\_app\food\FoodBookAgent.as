package dataAll._app.food
{
   import com.sounto.oldUtils.ComMethod;
   
   public class FoodBookAgent
   {
      private var def:FoodBookDefine;
      
      private var foodData:IO_FoodAgentFather;
      
      private var _unlockB:int = 0;
      
      public function FoodBookAgent()
      {
         super();
      }
      
      public function init(def0:FoodBookDefine, foodData0:IO_FoodAgentFather) : void
      {
         this.def = def0;
         this.foodData = foodData0;
      }
      
      public function getDef() : FoodBookDefine
      {
         return this.def;
      }
      
      public function get cnName() : String
      {
         return this.def.cnName;
      }
      
      public function getGripTitle() : String
      {
         var s0:String = this.def.cnName;
         var num0:int = this.num;
         if(num0 > 0)
         {
            s0 += ComMethod.color("x" + num0,"#FFFF00");
         }
         return s0;
      }
      
      public function get name() : String
      {
         return this.def.name;
      }
      
      public function get num() : int
      {
         return this.foodData.getBookNum(this.def.name);
      }
      
      public function get iconUrl() : String
      {
         return this.def.iconUrl;
      }
      
      public function getRawIconArr() : Array
      {
         return this.def.getRawIconArr();
      }
      
      public function getGatherTip() : String
      {
         var s0:String = "";
         if(!this.unlockB)
         {
            s0 = "解锁需要厨艺值：" + ComMethod.mustColor(this.foodData.getProfiAll(),this.def.profi);
         }
         else
         {
            s0 = this.def.getGatherTip();
         }
         return s0;
      }
      
      public function dayEatPan() : Boolean
      {
         return this.foodData.dayEatPan(this.name);
      }
      
      public function getSmallIconLabel() : String
      {
         if(this.unlockB)
         {
            if(this.foodData.getEatName() == this.name)
            {
               return "eat";
            }
            if(this.dayEatPan())
            {
               return "dayEat";
            }
            return "";
         }
         return "lock";
      }
      
      public function get unlockB() : Boolean
      {
         if(this._unlockB == 0)
         {
            return this.fleshUnlock();
         }
         return this._unlockB == 1;
      }
      
      public function fleshUnlock() : Boolean
      {
         if(this.foodData.getProfiAll() >= this.def.profi)
         {
            this._unlockB = 1;
            return true;
         }
         this._unlockB = -1;
         return false;
      }
      
      public function composeEnoughB() : Boolean
      {
         var rawName0:* = null;
         var num0:int = 0;
         var rawArr0:Array = this.def.getRawArr();
         for each(rawName0 in rawArr0)
         {
            num0 = int(this.foodData.getRawNum(rawName0));
            if(num0 < 1)
            {
               return false;
            }
         }
         return true;
      }
   }
}

