package dataAll._app.union.define
{
   import dataAll._app.union.battle.UnionBattleDefine;
   import dataAll._app.union.building.define.UnionBuildingDefineGroup;
   import dataAll._app.union.task.UnionTaskDefineGroup;
   import dataAll.pro.PropertyArrayDefineGroup;
   
   public class UnionDefineGroup
   {
      public var military:MilitaryDefineGroup = new MilitaryDefineGroup();
      
      public var dataPro:PropertyArrayDefineGroup = new PropertyArrayDefineGroup();
      
      public var task:UnionTaskDefineGroup = new UnionTaskDefineGroup();
      
      public var battle:UnionBattleDefine = new UnionBattleDefine();
      
      public var building:UnionBuildingDefineGroup = new UnionBuildingDefineGroup();
      
      private var moneyDonation:DonationDefine = new DonationDefine();
      
      private var otherDonation:DonationDefine = new DonationDefine();
      
      public function UnionDefineGroup()
      {
         super();
      }
      
      public function inXml(militaryXml0:XML, dataXml0:XML, taskXml0:XML, battleXml0:XML, buildingXml0:XML, buildingProXml0:XML) : void
      {
         this.military.inData_byXML(militaryXml0);
         this.dataPro.inData_byXML(dataXml0);
         this.task.inData_byXML(taskXml0);
         this.battle.inData_byXML(battleXml0);
         this.building.inData_byXML(buildingXml0);
         this.building.property.inData_byXML(buildingProXml0);
         this.initDonation();
      }
      
      private function initDonation() : void
      {
         this.moneyDonation.must.money = 10;
         this.moneyDonation.contribution = 100;
         this.moneyDonation.dayNum = 8;
         this.moneyDonation.gift.addGiftByStr("things;converStone;7");
         this.moneyDonation.gift.addGiftByStr("things;godStone;7");
         this.moneyDonation.gift.addGiftByStr("things;exploitCards;1");
         this.otherDonation.must.coin = 15000;
         this.otherDonation.contribution = 70;
         this.otherDonation.dayNum = 4;
      }
      
      public function fleshDefine() : void
      {
         this.building.fleshDefine();
      }
      
      public function getDonationDefine(name0:String) : DonationDefine
      {
         return this[name0 + "Donation"];
      }
   }
}

