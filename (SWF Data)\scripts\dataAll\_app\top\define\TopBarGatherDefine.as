package dataAll._app.top.define
{
   import com.sounto.utils.ClassProperty;
   
   public class TopBarGatherDefine
   {
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var normalB:Boolean = false;
      
      public var info:String = "";
      
      public function TopBarGatherDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
   }
}

