package dataAll.scene
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll.level.define.mapRect.MapRectGroup;
   import flash.geom.Rectangle;
   
   public class SceneDefine
   {
      public var fatherName:String = "";
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var swfUrl:String = "";
      
      public var imgUrl:String = "";
      
      public var sceneWidth:int = 0;
      
      public var sceneHeight:int = 0;
      
      public var viewRangeRect:Rectangle = new Rectangle();
      
      public var heroStandardY:int = 0;
      
      public var layerArr:Array = [];
      
      public var rectG:MapRectGroup = new MapRectGroup();
      
      public var spiderData:String = "";
      
      public var skillArr:Array = [];
      
      public var effectArr:Array = [];
      
      public var floorY:int = 0;
      
      public var gravity:Number = 1;
      
      public var noFloorB:Boolean = false;
      
      public function SceneDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML, father0:String, viewWidth:int, viewHeight:int) : void
      {
         var n:* = undefined;
         var layer0:SceneLayerDefine = null;
         this.fatherName = father0;
         this.name = xml0.name;
         this.cnName = xml0.cnName;
         this.swfUrl = xml0.swfUrl;
         this.imgUrl = xml0.imgUrl;
         this.sceneWidth = int(xml0.sceneWidth);
         this.sceneHeight = int(xml0.sceneHeight);
         this.heroStandardY = int(xml0.heroStandardY);
         this.floorY = int(xml0.floorY);
         if(String(xml0.gravity) != "")
         {
            this.gravity = Number(xml0.gravity);
         }
         if(String(xml0.skillArr) != "")
         {
            this.skillArr = String(xml0.skillArr).split(",");
         }
         if(String(xml0.effectArr) != "")
         {
            this.effectArr = String(xml0.effectArr).split(",");
         }
         this.viewRangeRect = ComMethod.getRect(xml0.viewRangeRect);
         if(this.swfUrl == "")
         {
            this.swfUrl = "swf/scene/" + this.name + ".swf";
         }
         if(this.imgUrl == "")
         {
            this.imgUrl = this.name + "/s1";
         }
         this.noFloorB = Boolean(int(xml0.@noFloorB));
         var levelXML:XMLList = xml0.layer;
         for(n in levelXML)
         {
            layer0 = new SceneLayerDefine();
            layer0.inData_byXML(levelXML[n]);
            layer0.cy = -(this.heroStandardY - viewHeight / 2) * (1 - layer0.moveRateY);
            layer0.beforeMoveRateX = layer0.moveRateX;
            if(layer0.moveRateX == 0)
            {
               layer0.moveRateX = (layer0.width - viewWidth) / (this.viewRangeRect.width - viewWidth);
               layer0.cx = this.viewRangeRect.x;
            }
            if(layer0.moveRateY == 0)
            {
               layer0.moveRateY = (layer0.height - viewHeight) / (this.viewRangeRect.height - viewHeight);
               layer0.cy = this.viewRangeRect.y;
            }
            this.layerArr.push(layer0);
            if(layer0.imgName == "back2")
            {
               if(layer0.beforeMoveRateX == 0)
               {
                  INIT.tempTrace(this.name + "   " + this.cnName + ":back2 moveRateX=0");
               }
            }
         }
         this.spiderData = String(xml0.spiderData);
      }
      
      public function isSpaceB() : Boolean
      {
         return this.fatherName == SceneFather.space;
      }
      
      public function isAirB() : Boolean
      {
         return this.name == "SkyScene";
      }
      
      public function getMapDefine() : WorldMapDefine
      {
         return Gaming.defineGroup.worldMap.getDefine(this.name);
      }
      
      public function getGravityMul() : Number
      {
         return this.gravity;
      }
      
      public function getOtherSwfUrlLabelArr() : Array
      {
         return this.effectArr;
      }
   }
}

