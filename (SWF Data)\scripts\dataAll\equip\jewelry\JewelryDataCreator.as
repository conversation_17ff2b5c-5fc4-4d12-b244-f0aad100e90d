package dataAll.equip.jewelry
{
   import dataAll._player.PlayerData;
   import dataAll.must.define.MustDefine;
   
   public class JewelryDataCreator
   {
      public function JewelryDataCreator()
      {
         super();
      }
      
      public static function getSave(name0:String, num0:int) : JewelrySave
      {
         var d0:JewelryDefine = Gaming.defineGroup.jewelry.getDefine(name0);
         if(!(d0 is JewelryDefine))
         {
            INIT.showError("找不到定义JewelryDefine：" + name0);
         }
         return getSaveByDefine(d0,num0);
      }
      
      public static function getSaveByDefine(d0:JewelryDefine, num0:int) : JewelrySave
      {
         var s0:JewelrySave = new JewelrySave();
         s0.inDataByDefine(d0);
         s0.nowNum = num0;
         return s0;
      }
      
      public static function getTempData(d0:JewelryDefine, pd0:PlayerData) : JewelryData
      {
         var s0:JewelrySave = getSaveByDefine(d0,1);
         var da0:JewelryData = new JewelryData();
         da0.inData_bySave(s0,pd0,null);
         return da0;
      }
      
      public static function getMustTip(d0:JewelryDefine) : String
      {
         var lv0:int = 0;
         var nowD0:JewelryDefine = null;
         var mustD0:MustDefine = null;
         var s0:String = "";
         var maxLv0:int = d0.maxLv;
         if(maxLv0 > 1)
         {
            for(lv0 = 2; lv0 <= maxLv0; lv0++)
            {
               nowD0 = Gaming.defineGroup.jewelry.getDefine(d0.baseLabel + "_" + lv0);
               mustD0 = getUpgradeMust(d0,lv0);
               if(s0 != "")
               {
                  s0 += "\n";
               }
               s0 += "<b><yellow 第" + lv0 + "阶/></b>：" + mustD0.getThingsMustTip(99);
            }
         }
         return s0;
      }
      
      public static function getUpgradeMust(jewelryDefine0:JewelryDefine, lv0:int) : MustDefine
      {
         var d0:MustDefine = new MustDefine();
         var num0:int = getUpgradeMustThingsNum(lv0,jewelryDefine0);
         var thingArr0:Array = [jewelryDefine0.getUpgradeMustThingsName() + ";" + num0];
         d0.inThingsDataByArr(thingArr0);
         d0.coin = num0 * 200;
         return d0;
      }
      
      private static function getUpgradeMustThingsNum(lv0:int, jewelryDefine0:JewelryDefine) : int
      {
         return lv0 * 50 + 200;
      }
   }
}

