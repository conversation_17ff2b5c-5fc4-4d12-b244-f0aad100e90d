package dataAll._app.top.extra
{
   import com.sounto.utils.ClassProperty;
   
   public class UnionBattleExtra extends TopExtra
   {
      public static var pro_arr:Array = [];
      
      public var unionId:int = 0;
      
      public var title:String = "";
      
      public var time:String = "";
      
      public var lt:String = "";
      
      public function UnionBattleExtra()
      {
         super();
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      override public function setToSimulated() : void
      {
         this.unionId = 0;
         this.title = "沃龙";
         this.lt = "20.1";
      }
   }
}

