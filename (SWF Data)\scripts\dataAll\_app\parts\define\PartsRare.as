package dataAll._app.parts.define
{
   import dataAll._app.parts.PartsAddData;
   import dataAll.arms.ArmsData;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsName;
   import dataAll.arms.define.ArmsType;
   import dataAll.bullet.BulletLineDefine;
   import dataAll.equip.define.EquipColor;
   import dataAll.things.ThingsData;
   import dataAll.things.define.ThingsDefine;
   
   public class PartsRare
   {
      private static var darkgoldCpuLine:BulletLineDefine = null;
      
      private static var purgoldCpuLine:BulletLineDefine = null;
      
      public function PartsRare()
      {
         super();
      }
      
      public static function getComposeMustNum(lv0:int, d0:ThingsDefine) : int
      {
         var n0:int = 100;
         var base0:String = d0.baseLabel;
         if(base0 == "lshapedParts")
         {
            n0 = 3;
         }
         else if(lv0 == 1)
         {
            n0 = 4;
         }
         else if(lv0 >= 2)
         {
            n0 = 3;
         }
         return n0;
      }
      
      public static function addDeal(da0:PartsAddData, tda0:ThingsData, armsDa0:ArmsData = null) : void
      {
         var d0:ThingsDefine = tda0.save.getDefine();
         var fun0:Function = PartsRare[d0.baseLabel + "_add"];
         if(fun0 is Function)
         {
            fun0(da0,tda0,armsDa0);
         }
      }
      
      public static function specialDeal(tDa0:ThingsData, armsDa0:ArmsData) : void
      {
         var d0:ThingsDefine = tDa0.save.getDefine();
         var fun0:Function = PartsRare[d0.baseLabel];
         if(fun0 is Function)
         {
            fun0(tDa0,armsDa0);
         }
      }
      
      private static function balloonParts(tDa0:ThingsData, armsDa0:ArmsData) : void
      {
         if(armsDa0.isRect())
         {
            armsDa0.hitImg = Gaming.defineGroup.imageUrl.getDefine("christmasGun_hit");
            armsDa0.bulletImg = Gaming.defineGroup.imageUrl.getDefine("balloonBullet");
            armsDa0.shootSoundUrl = "specialGun/yearRabbitSound";
            if(!(Boolean(armsDa0) && armsDa0.name == "extremeGun"))
            {
               armsDa0.bulletSpeed *= 0.7;
            }
         }
      }
      
      private static function mooncakeParts(tDa0:ThingsData, armsDa0:ArmsData) : void
      {
         if(armsDa0.isRect())
         {
            armsDa0.hitImg = Gaming.defineGroup.imageUrl.getDefine("stoneBoom");
            armsDa0.bulletImg = Gaming.defineGroup.imageUrl.getDefine("mooncakeBullet");
         }
      }
      
      private static function pistolCopyParts(tDa0:ThingsData, armsDa0:ArmsData) : void
      {
         if(armsDa0.armsType == ArmsType.pistol)
         {
            armsDa0.gunNum = 2;
         }
      }
      
      private static function shotgunCopyParts(tDa0:ThingsData, armsDa0:ArmsData) : void
      {
         if(armsDa0.armsType == ArmsType.shotgun)
         {
            armsDa0.gunNum = 2;
         }
      }
      
      private static function lshapedParts(tDa0:ThingsData, armsDa0:ArmsData) : void
      {
         armsDa0.bulletAngle = 179.99;
      }
      
      private static function darkgoldCpu_add(da0:PartsAddData, thingsDa0:ThingsData = null, armsDa0:ArmsData = null) : void
      {
         var add0:Number = NaN;
         var armsD0:ArmsDefine = null;
         var name0:String = null;
         var evoLv0:int = 0;
         if(Boolean(armsDa0) && Boolean(thingsDa0))
         {
            add0 = 0;
            armsD0 = armsDa0.def;
            name0 = armsD0.name;
            evoLv0 = armsDa0.getEvoLv();
            if(armsD0.isRandomB())
            {
               if(armsDa0.getSaveColor() == EquipColor.RED || armsDa0.getSaveColor() == EquipColor.BLACK)
               {
                  add0 = 4;
               }
            }
            if(add0 > 0)
            {
               da0.colorHurtMul = add0;
               da0.color = EquipColor.DARKGOLD;
            }
         }
      }
      
      private static function darkgoldCpu(tDa0:ThingsData, armsDa0:ArmsData) : void
      {
         var l0:BulletLineDefine = null;
         if(armsDa0.isRandomB())
         {
            l0 = darkgoldCpuLine;
            if(l0 == null)
            {
               l0 = new BulletLineDefine();
               l0.color = "0xFFFF93";
               l0.lightColor = "0xFCA500";
               l0.size = 2;
               l0.lightSize = 6;
               l0.blendMode = "add";
               l0.type = "one";
               darkgoldCpuLine = l0;
            }
            armsDa0.lineD = l0;
         }
      }
      
      private static function purgoldCpu_add(da0:PartsAddData, thingsDa0:ThingsData = null, armsDa0:ArmsData = null) : void
      {
         var add0:Number = NaN;
         var armsD0:ArmsDefine = null;
         var name0:String = null;
         var evoLv0:int = 0;
         var saveColor0:String = null;
         if(Boolean(armsDa0) && Boolean(thingsDa0))
         {
            add0 = 0;
            armsD0 = armsDa0.def;
            name0 = armsD0.name;
            evoLv0 = armsDa0.getEvoLv();
            if(armsD0.isRandomB())
            {
               saveColor0 = armsDa0.getSaveColor();
               if(saveColor0 == EquipColor.RED)
               {
                  add0 = 6;
               }
               else if(saveColor0 == EquipColor.BLACK)
               {
                  add0 = 10;
               }
            }
            else if(name0 == ArmsName.yearDog)
            {
               add0 = 1.2;
            }
            else if(name0 == ArmsName.beadCrossbow)
            {
               add0 = 3;
            }
            else if(name0 == ArmsName.bangerGun)
            {
               add0 = 3;
            }
            else if(name0 == ArmsName.christmasGun)
            {
               if(evoLv0 >= 2)
               {
                  add0 = 1;
                  da0.addNewAndRareSkillArr(["oldSugar"]);
               }
            }
            if(add0 > 0)
            {
               da0.colorHurtMul = add0;
               da0.color = EquipColor.PURGOLD;
            }
         }
      }
      
      private static function purgoldCpu(tDa0:ThingsData, armsDa0:ArmsData) : void
      {
         var l0:BulletLineDefine = null;
         if(armsDa0.isRandomB())
         {
            l0 = purgoldCpuLine;
            if(l0 == null)
            {
               l0 = new BulletLineDefine();
               l0.color = "0xFFE7F0";
               l0.lightColor = "0xAF00BF";
               l0.size = 3;
               l0.lightSize = 6;
               l0.blendMode = "add";
               l0.type = "one";
               purgoldCpuLine = l0;
            }
            armsDa0.lineD = l0;
         }
      }
   }
}

