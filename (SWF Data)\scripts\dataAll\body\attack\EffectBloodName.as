package dataAll.body.attack
{
   import dataAll.body.define.NormalBodyDefine;
   
   public class EffectBloodName
   {
      public static const greenBlood:String = "greenBlood";
      
      public static const spark:String = "spark";
      
      public static const whiteBlood:String = "whiteBlood";
      
      public static const fire:String = "fire";
      
      public static const stone:String = "stone";
      
      public function EffectBloodName()
      {
         super();
      }
      
      public static function getByRaceType(type0:String) : String
      {
         if(type0 == NormalBodyDefine.RACE_ROBOT)
         {
            return spark;
         }
         if(type0 == NormalBodyDefine.RACE_SNOW)
         {
            return whiteBlood;
         }
         if(type0 == NormalBodyDefine.RACE_SHIP)
         {
            return fire;
         }
         if(type0 == NormalBodyDefine.RACE_STONE)
         {
            return stone;
         }
         return greenBlood;
      }
      
      public static function getDieMul(name0:String) : Number
      {
         if(name0 == fire)
         {
            return 2;
         }
         return 1;
      }
   }
}

