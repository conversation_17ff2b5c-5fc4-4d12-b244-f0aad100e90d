package dataAll.arms.creator
{
   import com.sounto.net.SWFLoaderManager;
   import dataAll.arms.define.GunPart;
   import flash.display.Sprite;
   import flash.utils.getTimer;
   
   public class GunImageManager
   {
      public var swfM:SWFLoaderManager = null;
      
      public var obj:Object = new Object();
      
      public function GunImageManager()
      {
         super();
      }
      
      public function addImage(name0:String, iconB:Boolean = true) : GunImage
      {
         var part_name0:* = null;
         var img0:GunImage = null;
         var part_label0:String = null;
         var sp0:* = undefined;
         var t_sp0:Sprite = null;
         var gun0:GunImage = this.getImage(name0);
         if(Boolean(gun0))
         {
            if(!(iconB && !gun0.iconBmp))
            {
               return gun0;
            }
            gun0.clearBmpData();
         }
         var tt0:Number = getTimer();
         var sp_arr:Array = [];
         var partUrlObj:Object = GunPart.getPartUrlObj(name0);
         var partNum0:int = 0;
         var bodySp0:Sprite = null;
         for each(part_name0 in GunPart.PART_ARR)
         {
            part_label0 = partUrlObj[part_name0];
            if(part_label0 == "0")
            {
               sp_arr.push(null);
            }
            else
            {
               sp0 = this.swfM.getResourceFull(part_label0);
               sp_arr.push(sp0);
               partNum0++;
               if(part_name0 == GunPart.body)
               {
                  bodySp0 = sp0;
               }
            }
         }
         img0 = null;
         if(partNum0 <= 3 && Boolean(bodySp0))
         {
            img0 = GunImageCreator.creatOnlyBodyImage(bodySp0);
         }
         else
         {
            t_sp0 = this.swfM.getResourceFull(partUrlObj[GunPart.texture]) as Sprite;
            img0 = GunImageCreator.creatGunImage(sp_arr,t_sp0,iconB);
         }
         this.obj[name0] = img0;
         return img0;
      }
      
      public function getImage(name0:String, noAndAddB:Boolean = false) : GunImage
      {
         if(this.obj.hasOwnProperty(name0))
         {
            return this.obj[name0];
         }
         if(noAndAddB)
         {
            return this.addImage(name0,true);
         }
         return null;
      }
      
      public function getImageCopy(name0:String) : GunImage
      {
         var img0:GunImage = this.obj[name0];
         if(Boolean(img0))
         {
            return img0.clone();
         }
         INIT.showError("找不到武器图像：" + name0);
         return null;
      }
      
      public function fleshObj() : void
      {
         var i:* = undefined;
         var g0:GunImage = null;
         var obj0:Object = {};
         for(i in this.obj)
         {
            g0 = this.obj[i];
            if(g0.die == 0)
            {
               obj0[i] = g0;
            }
         }
         this.obj = obj0;
      }
      
      public function clearResourceByNoArray(arr0:Array) : void
      {
         var n:* = undefined;
         var g0:GunImage = null;
         for(n in this.obj)
         {
            g0 = this.obj[n];
            if(Boolean(g0))
            {
               if(g0.clearResourceB)
               {
                  if(arr0.indexOf(n) == -1)
                  {
                     g0.clearBmpData();
                     g0.die = 1;
                  }
               }
            }
         }
         this.fleshObj();
      }
   }
}

