package dataAll._app.outfit.define
{
   public class OutfitDefineGroup
   {
      public var obj:Object = {};
      
      public var arr:Array = [];
      
      public function OutfitDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var x2:XML = null;
         var d0:OutfitDefine = null;
         var xl0:XMLList = xml0.one;
         for(n in xl0)
         {
            x2 = xl0[n];
            d0 = new OutfitDefine();
            d0.inData_byXML(x2);
            this.add(d0);
         }
      }
      
      private function add(d0:OutfitDefine) : void
      {
         this.obj[d0.name] = d0;
         this.arr.push(d0);
      }
   }
}

