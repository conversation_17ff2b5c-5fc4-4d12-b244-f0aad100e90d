package dataAll._app.worldMap.define
{
   import dataAll.pro.dataList.DataListDefine;
   
   public class WorldMapDefineGroup
   {
      public var nameArr:Array = [];
      
      public var arr:Array = [];
      
      public var obj:Object = {};
      
      public var cnObj:Object = {};
      
      public var fatherObj:Object = {};
      
      public var fatherArrObj:Object = {};
      
      public var bigObj:Object = {};
      
      public var priorityArr:Array = [];
      
      private var nowIndex:int = 0;
      
      private var lastLevelObj:Object = null;
      
      public function WorldMapDefineGroup()
      {
         super();
         for(var i:int = 0; i < 20; i++)
         {
            this.priorityArr[i] = [];
         }
      }
      
      public function afterDeal() : void
      {
         var d0:DataListDefine = Gaming.defineGroup.dataList.getDefine("lastLevelName");
         this.lastLevelObj = d0.getTrueValueObj();
      }
      
      public function getLastLevel(mapName0:String) : String
      {
         if(this.lastLevelObj.hasOwnProperty(mapName0))
         {
            return this.lastLevelObj[mapName0];
         }
         return "";
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var i:* = undefined;
         var fatherName0:String = null;
         var placeXML0:* = undefined;
         var n:* = undefined;
         var d0:WorldMapDefine = null;
         var arr0:Array = this.arr;
         var fatherXML0:XMLList = xml0.father;
         for(i in fatherXML0)
         {
            fatherName0 = fatherXML0[i].@name;
            placeXML0 = fatherXML0[i].place;
            for(n in placeXML0)
            {
               d0 = new WorldMapDefine();
               d0.inData_byXML(placeXML0[n],fatherName0);
               if(fatherName0 == "big")
               {
                  this.bigObj[d0.name] = d0;
               }
               else
               {
                  d0.index = this.nowIndex;
                  this.arr.push(d0);
                  this.obj[d0.name] = d0;
                  this.cnObj[d0.cnName] = d0;
                  this.nameArr.push(d0.name);
                  if(!this.fatherObj.hasOwnProperty(fatherName0))
                  {
                     this.fatherObj[fatherName0] = {};
                     this.fatherArrObj[fatherName0] = [];
                  }
                  this.fatherObj[fatherName0][d0.name] = d0;
                  this.fatherArrObj[fatherName0].push(d0);
                  this.priorityArr[d0.priority].push(d0);
                  ++this.nowIndex;
               }
            }
         }
      }
      
      public function getDefine(name0:String) : WorldMapDefine
      {
         return this.obj[name0];
      }
      
      public function getDefineByCn(cn0:String) : WorldMapDefine
      {
         return this.cnObj[cn0];
      }
      
      public function getDefineObjByFather(name0:String) : Object
      {
         return this.fatherObj[name0];
      }
      
      public function getArrByFather(name0:String) : Array
      {
         return this.fatherArrObj[name0];
      }
      
      public function getShowMapArr() : Array
      {
         return this.fatherArrObj[WorldMapDefine.INFECTED_AREA];
      }
      
      public function getNameArrByLv(lv0:int) : Array
      {
         var n:* = undefined;
         var d0:WorldMapDefine = null;
         var arr0:Array = [];
         for(n in this.arr)
         {
            d0 = this.arr[n];
            if(d0.lv <= lv0)
            {
               arr0.push(d0.name);
            }
         }
         return arr0;
      }
      
      public function getArrByPriority(random0:Number) : Array
      {
         var i:* = undefined;
         var pArr0:Array = null;
         var pArr2:Array = null;
         var arr0:Array = [];
         for(i in this.priorityArr)
         {
            pArr0 = this.priorityArr[this.priorityArr.length - i - 1];
            pArr2 = pArr0.concat([]);
            pArr2.sort(function():*
            {
               return Math.random() > 0.5 ? 1 : -1;
            });
            arr0 = arr0.concat(pArr2);
         }
         if(Math.random() < random0)
         {
            this.horseArr(arr0,0,4);
         }
         if(Math.random() < random0)
         {
            this.horseArr(arr0,1,4);
         }
         if(Math.random() < random0)
         {
            this.horseArr(arr0,2,4);
         }
         return arr0;
      }
      
      private function horseArr(arr0:Array, num0:int, gap0:int) : void
      {
         var obj0:Object = arr0.pop();
         var index0:int = num0 * gap0 + Math.random() * gap0;
         arr0.splice(index0,0,obj0);
      }
      
      public function panInBigMap(name0:String) : int
      {
         var d0:WorldMapDefine = this.getDefine(name0);
         if(d0.lv <= 35)
         {
            return 0;
         }
         return -1;
      }
      
      public function clearAllDemTip() : void
      {
         var d0:WorldMapDefine = null;
         for each(d0 in this.arr)
         {
            d0.clearDemTip();
         }
      }
      
      public function test2() : void
      {
         var d0:WorldMapDefine = null;
         var arr0:Array = this.getShowMapArr();
         for each(d0 in arr0)
         {
            INIT.tempTrace(d0.name);
         }
      }
      
      public function traceCn() : void
      {
         var d0:WorldMapDefine = null;
         for each(d0 in this.arr)
         {
            trace(d0.lv + " " + d0.cnName);
         }
      }
      
      public function test() : void
      {
         var d0:WorldMapDefine = null;
         var arr0:Array = this.getArrByPriority(0.6);
         for each(d0 in arr0)
         {
         }
      }
      
      public function testCountMap() : void
      {
         var _5a:Array = null;
         var cnArr0:Array = null;
         var index0:int = 0;
         var cn0:String = null;
         var arr0:Array = this.cutList();
         var _5arr0:Array = [];
         for(var i:int = 0; i < 7; i++)
         {
            _5a = [];
            for each(var _loc10_ in arr0)
            {
               cnArr0 = _loc10_;
               _loc10_;
               index0 = Math.random() * cnArr0.length;
               cn0 = cnArr0[index0];
               cnArr0.splice(index0,1);
               _5a.push("\"" + cn0 + "\"");
            }
            _5arr0.push(_5a);
            trace(_5a);
         }
      }
      
      private function cutList() : Array
      {
         var d0:WorldMapDefine = null;
         var index0:int = 0;
         var arr_len0:int = 0;
         var i:int = 0;
         var lvMax0:int = 0;
         var lvMaxArr0:Array = [13,46,60,74,84];
         var lvarr0:Array = [];
         for each(var _loc10_ in this.arr)
         {
            d0 = _loc10_;
            _loc10_;
            index0 = -1;
            arr_len0 = int(lvMaxArr0.length);
            for(i = 0; i < arr_len0; i++)
            {
               lvMax0 = int(lvMaxArr0[i]);
               if(d0.lv <= lvMax0)
               {
                  index0 = i;
                  break;
               }
            }
            if(index0 >= 0)
            {
               if(lvarr0[index0] == null)
               {
                  lvarr0[index0] = [];
               }
               lvarr0[index0].push(d0.name);
            }
         }
         return lvarr0;
      }
   }
}

