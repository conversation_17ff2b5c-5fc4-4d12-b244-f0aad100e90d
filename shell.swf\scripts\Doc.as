package
{
   import com.junkbyte.console.Cc;
   import flash.display.Loader;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.media.SoundMixer;
   import flash.media.SoundTransform;
   
   public class Doc extends MovieClip
   {
      private var l:Loader;
      
      public var BagProxy:Class;
      
      public var GoodConfig:Class;
      
      public function Doc()
      {
         super();
         if(stage)
         {
            this.init();
         }
         else
         {
            this.addEventListener(Event.ADDED_TO_STAGE,this.init);
         }
      }
      
      private function init(param1:Event = null) : void
      {
         this.l = new Loader();
         this.l.contentLoaderInfo.addEventListener(Event.COMPLETE,this.l_complete);
         this.l.loadBytes(new MySWFClass());
      }
      
      private function l_complete(param1:Event) : void
      {
         this.addChild(this.l);
         Cc.startOnStage(this,"");
         Cc.visible = true;
         Cc.commandLine = true;
         Cc.config.commandLineAllowed = true;
         // 基础功能
         Cc.addSlashCommand("CreateGoods",this.create_goods,"刷道具 - 用法: /CreateGoods 道具名 数量");
         Cc.addSlashCommand("CreateArms",this.create_arms,"刷武器 - 用法: /CreateArms 武器名 数量");
         Cc.addSlashCommand("CreateEquip",this.create_equip,"刷装备 - 用法: /CreateEquip 装备名 数量");
         Cc.addSlashCommand("CreateParts",this.create_parts,"刷零件 - 用法: /CreateParts 零件名 数量");
         Cc.addSlashCommand("CreateGenes",this.create_genes,"刷基因体 - 用法: /CreateGenes 基因体名 数量");
         Cc.addSlashCommand("CreateVehicle",this.create_vehicle,"刷载具 - 用法: /CreateVehicle 载具名 数量");
         Cc.addSlashCommand("CreateFashion",this.create_fashion,"刷时装 - 用法: /CreateFashion 时装名 数量");
         Cc.addSlashCommand("CreateDevice",this.create_device,"刷装置 - 用法: /CreateDevice 装置名 数量");
         Cc.addSlashCommand("CreatePartner",this.create_partner,"刷副将 - 用法: /CreatePartner 副将名 数量");
         Cc.addSlashCommand("AddCoin",this.add_coin,"刷金币 - 用法: /AddCoin 数量");
         Cc.addSlashCommand("AddExp",this.add_exp,"刷经验 - 用法: /AddExp 数量");
         Cc.addSlashCommand("GetPlayerInfo",this.get_player_info,"获取玩家信息");
         Cc.addSlashCommand("ListBag",this.list_bag,"查看背包物品");

         // 人物相关
         Cc.addSlashCommand("SetLevel",this.set_level,"设置等级 - 用法: /SetLevel 等级");
         Cc.addSlashCommand("SetName",this.set_name,"设置昵称 - 用法: /SetName 昵称");
         Cc.addSlashCommand("AddTitle",this.add_title,"一键添加所有称号");
         Cc.addSlashCommand("AddSingleTitle",this.add_single_title,"添加指定称号 - 用法: /AddSingleTitle 称号名");

         // 货币相关
         Cc.addSlashCommand("SetScore",this.set_score,"设置积分 - 用法: /SetScore 数量");
         Cc.addSlashCommand("SetAnniCoin",this.set_anni_coin,"设置纪念币 - 用法: /SetAnniCoin 数量");
         Cc.addSlashCommand("SetTenCoin",this.set_ten_coin,"设置十年币 - 用法: /SetTenCoin 数量");

         // 背包相关
         Cc.addSlashCommand("SetBagSize",this.set_bag_size,"设置背包大小 - 用法: /SetBagSize 类型 数量");
         Cc.addSlashCommand("ClearBag",this.clear_bag,"清空背包 - 用法: /ClearBag 类型");

         // 地图相关
         Cc.addSlashCommand("UnlockAllMaps",this.unlock_all_maps,"解锁所有地图");
         Cc.addSlashCommand("CompleteAllMaps",this.complete_all_maps,"通关所有地图");

         // 成就相关
         Cc.addSlashCommand("CompleteAllAchieve",this.complete_all_achieve,"完成所有成就");

         // 游戏内功能
         Cc.addSlashCommand("KillAllEnemy",this.kill_all_enemy,"秒杀全图敌人");
         Cc.addSlashCommand("WinLevel",this.win_level,"通关当前关卡");
         Cc.addSlashCommand("RestartLevel",this.restart_level,"重新开始关卡");

         // 尸宠类功能
         Cc.addSlashCommand("SetPetName",this.set_pet_name,"设置尸宠昵称 - 用法: /SetPetName 昵称");
         Cc.addSlashCommand("SetPetLevel",this.set_pet_level,"设置尸宠等级 - 用法: /SetPetLevel 等级");
         Cc.addSlashCommand("SetPetExp",this.set_pet_exp,"设置尸宠经验 - 用法: /SetPetExp 经验");
         Cc.addSlashCommand("ClearAllPets",this.clear_all_pets,"清除所有宠物");
         Cc.addSlashCommand("SetPetDropRate",this.set_pet_drop_rate,"设置基因体掉落概率 - 用法: /SetPetDropRate 概率");

         // 厨艺类功能
         Cc.addSlashCommand("AddAllFood",this.add_all_food,"添加所有食材 - 用法: /AddAllFood 数量");
         Cc.addSlashCommand("SetCookingSkill",this.set_cooking_skill,"设置总厨艺值 - 用法: /SetCookingSkill 数值");

         // 飞船类功能
         Cc.addSlashCommand("SetSpaceLevel",this.set_space_level,"设置飞船等级 - 用法: /SetSpaceLevel 等级");
         Cc.addSlashCommand("AddSpaceExp",this.add_space_exp,"添加飞船经验 - 用法: /AddSpaceExp 经验");

         // 军队类功能
         Cc.addSlashCommand("CompleteUnionTasks",this.complete_union_tasks,"完成军队任务");
         Cc.addSlashCommand("SetMilitaryRank",this.set_military_rank,"设置军衔等级 - 用法: /SetMilitaryRank 等级");
         Cc.addSlashCommand("SetContribution",this.set_contribution,"设置个人贡献 - 用法: /SetContribution 数值");
         Cc.addSlashCommand("ClearUnionData",this.clear_union_data,"清除军队数据");

         // 增益类功能
         Cc.addSlashCommand("SetDoubleMaterial",this.set_double_material,"设置双倍材料时间 - 用法: /SetDoubleMaterial 时间");
         Cc.addSlashCommand("SetDoubleExp",this.set_double_exp,"设置双倍经验时间 - 用法: /SetDoubleExp 时间");
         Cc.addSlashCommand("SetDoubleWeapon",this.set_double_weapon,"设置双倍武器时间 - 用法: /SetDoubleWeapon 时间");
         Cc.addSlashCommand("SetDoubleEquip",this.set_double_equip,"设置双倍装备时间 - 用法: /SetDoubleEquip 时间");

         // 魂卡类功能
         Cc.addSlashCommand("SetNormalCardCount",this.set_normal_card_count,"设置普通已抽次数 - 用法: /SetNormalCardCount 次数");
         Cc.addSlashCommand("SetHighCardCount",this.set_high_card_count,"设置高级已抽次数 - 用法: /SetHighCardCount 次数");
         Cc.addSlashCommand("SetCardBagSize",this.set_card_bag_size,"设置魂卡背包 - 用法: /SetCardBagSize 数量");
         Cc.addSlashCommand("UnlimitedHighCard",this.unlimited_high_card,"无限高级抽卡");

         // 地图类功能
         Cc.addSlashCommand("UnlockSecretMaps",this.unlock_secret_maps,"解锁所有秘境");
         Cc.addSlashCommand("SetSecretKeys",this.set_secret_keys,"设置秘境钥匙 - 用法: /SetSecretKeys 数量");
         Cc.addSlashCommand("SetSweepCount",this.set_sweep_count,"设置扫荡次数 - 用法: /SetSweepCount 次数");
         Cc.addSlashCommand("UnlockAllDifficulty",this.unlock_all_difficulty,"解锁所有难度");

         // 时间类功能
         Cc.addSlashCommand("SetNewDay",this.set_new_day,"设置新的一天");
         Cc.addSlashCommand("SetNewWeek",this.set_new_week,"设置新的一周");
         Cc.addSlashCommand("ToggleLocalTime",this.toggle_local_time,"本地时间开关");
         Cc.addSlashCommand("ClearDoubleTime",this.clear_double_time,"清除双倍时间");

         // 存档类功能
         Cc.addSlashCommand("CheckSaveError",this.check_save_error,"查询异常原因");
         Cc.addSlashCommand("InitSave",this.init_save,"初始化本存档");
         Cc.addSlashCommand("RepairSave",this.repair_save,"修复存档数据");
         Cc.addSlashCommand("RemoveSaveError",this.remove_save_error,"解除异常存档");
         Cc.addSlashCommand("SetSaveError",this.set_save_error,"设置存档异常");

         // 高级功能
         Cc.addSlashCommand("SetTimeSpeed",this.set_time_speed,"设置时间倍速 - 用法: /SetTimeSpeed 倍数");
         Cc.addSlashCommand("SetFrameRate",this.set_frame_rate,"设置游戏帧数 - 用法: /SetFrameRate 帧数");
         Cc.addSlashCommand("UnlockSkillSystem",this.unlock_skill_system,"解锁技能系统");
         Cc.addSlashCommand("SetArenaCount",this.set_arena_count,"设置竞技场次数 - 用法: /SetArenaCount 次数");
         Cc.addSlashCommand("SetArenaScore",this.set_arena_score,"设置竞技场分数 - 用法: /SetArenaScore 分数");
         Cc.addSlashCommand("SetTowerLevel",this.set_tower_level,"设置天塔层数 - 用法: /SetTowerLevel 层数");
         Cc.addSlashCommand("SetPhantomTower",this.set_phantom_tower,"设置幻塔层数 - 用法: /SetPhantomTower 层数");
         Cc.addSlashCommand("AddRandomWeapon",this.add_random_weapon,"添加随机武器");
         Cc.addSlashCommand("AddRandomEquip",this.add_random_equip,"添加随机装备");
         Cc.addSlashCommand("CompleteCurrentTask",this.complete_current_task,"完成当前任务");
         Cc.addSlashCommand("UnlockAllTasks",this.unlock_all_tasks,"解锁所有主线任务");

         // 主角类功能
         Cc.addSlashCommand("SwitchToP1",this.switch_to_p1,"表哥切换");
         Cc.addSlashCommand("BackToP1",this.back_to_p1,"主角切换");
         Cc.addSlashCommand("SetHeroDefine",this.set_hero_define,"主角定义 - 用法: /SetHeroDefine 英雄名");
         Cc.addSlashCommand("ClearOverflowItems",this.clear_overflow_items,"清除溢出物品");

         // 物品类功能
         Cc.addSlashCommand("AddAllWeapons",this.add_all_weapons,"添加所有武器类");
         Cc.addSlashCommand("AddAllEquips",this.add_all_equips,"添加所有装备类");
         Cc.addSlashCommand("AddAllThings",this.add_all_things,"添加所有物品类");
         Cc.addSlashCommand("AddAllGenes",this.add_all_genes,"添加所有基因类");
         Cc.addSlashCommand("AddAllDevices",this.add_all_devices,"添加所有装置类");
         Cc.addSlashCommand("AddAllParts",this.add_all_parts,"添加所有零件类");
         Cc.addSlashCommand("AddAllShields",this.add_all_shields,"添加所有护盾类");

         // 成就数据类功能
         Cc.addSlashCommand("SetKillCount",this.set_kill_count,"设置杀敌数值 - 用法: /SetKillCount 数值");
         Cc.addSlashCommand("SetBossCount",this.set_boss_count,"设置boss数值 - 用法: /SetBossCount 数值");
         Cc.addSlashCommand("SetDeathCount",this.set_death_count,"设置死亡数值 - 用法: /SetDeathCount 数值");
         Cc.addSlashCommand("SetWeaponKill",this.set_weapon_kill,"设置副手击杀 - 用法: /SetWeaponKill 数值");
         Cc.addSlashCommand("SetVehicleKill",this.set_vehicle_kill,"设置载具击杀 - 用法: /SetVehicleKill 数值");
         Cc.addSlashCommand("SetWeaponDrop",this.set_weapon_drop,"设置武器掉落 - 用法: /SetWeaponDrop 数值");
         Cc.addSlashCommand("SetEquipDrop",this.set_equip_drop,"设置装备掉落 - 用法: /SetEquipDrop 数值");

         // 货币扩展功能
         Cc.addSlashCommand("SetPumpkin",this.set_pumpkin,"设置小南瓜 - 用法: /SetPumpkin 数量");
         Cc.addSlashCommand("SetPartsTicket",this.set_parts_ticket,"设置零件券 - 用法: /SetPartsTicket 数量");
         Cc.addSlashCommand("SetZongzi",this.set_zongzi,"设置粽子 - 用法: /SetZongzi 数量");
         Cc.addSlashCommand("SetGoldRecharge",this.set_gold_recharge,"设置黄金累充 - 用法: /SetGoldRecharge 数量");
         Cc.addSlashCommand("SetGold",this.set_gold,"设置黄金 - 用法: /SetGold 数量");

         // 任务扩展功能
         Cc.addSlashCommand("UnlockTaskSystem",this.unlock_task_system,"解锁任务系统");
         Cc.addSlashCommand("AcceptCurrentTask",this.accept_current_task,"接取当前任务");
         Cc.addSlashCommand("SetTaskProgress",this.set_task_progress,"设置当前任务目标次数 - 用法: /SetTaskProgress 次数");
         Cc.addSlashCommand("UnlockTaskByType",this.unlock_task_by_type,"解锁任务 - 用法: /UnlockTaskByType 代码");

         // 极品号功能
         Cc.addSlashCommand("LoadTopSave",this.load_top_save,"极品存档 - 用法: /LoadTopSave 模型编号(0-7)");

         // 自定义编辑功能
         Cc.addSlashCommand("EditWeapon",this.edit_weapon,"武器自定义编辑 - 用法: /EditWeapon 属性代码*数值&属性代码*数值");
         Cc.addSlashCommand("EditEquip",this.edit_equip,"装备自定义编辑 - 用法: /EditEquip 属性代码*数值&属性代码*数值");
         Cc.addSlashCommand("EditSkill",this.edit_skill,"技能自定义编辑 - 用法: /EditSkill 属性代码*数值");
         Cc.addSlashCommand("EditVehicle",this.edit_vehicle,"载具自定义编辑 - 用法: /EditVehicle 属性代码*数值");

         // 排行榜和竞技场功能
         Cc.addSlashCommand("ForceOpenRanking",this.force_open_ranking,"强开排行榜");
         Cc.addSlashCommand("ForceOpenArena",this.force_open_arena,"强开竞技场");
         Cc.addSlashCommand("StealSaveFromRanking",this.steal_save_from_ranking,"从排行榜偷存档 - 用法: /StealSaveFromRanking UID_索引");

         // 按键指令功能
         Cc.addSlashCommand("QuickCompleteTask",this.quick_complete_task,"快速完成当前任务 (R键功能)");
         Cc.addSlashCommand("QuickCompleteAchieve",this.quick_complete_achieve,"快速完成所有成就 (T键功能)");
         Cc.addSlashCommand("QuickAddRandomWeapon",this.quick_add_random_weapon,"快速添加随机武器 (J键功能)");
         Cc.addSlashCommand("QuickClearBag",this.quick_clear_bag,"快速清除背包 (O键功能)");
         Cc.addSlashCommand("QuickAddTitle",this.quick_add_title,"快速添加当前称号 (N键功能)");
         Cc.addSlashCommand("QuickKillAll",this.quick_kill_all,"快速秒杀全图 (K键功能)");
         Cc.addSlashCommand("QuickLoadSave",this.quick_load_save,"快速加载存档 (U键功能) - 用法: /QuickLoadSave UID_索引");

         // 基于真实ID的精确功能
         Cc.addSlashCommand("AddSpecificItem",this.add_specific_item,"添加指定物品 - 用法: /AddSpecificItem ID 数量");
         Cc.addSlashCommand("AddSpecificSkill",this.add_specific_skill,"添加指定技能 - 用法: /AddSpecificSkill 技能ID");
         Cc.addSlashCommand("ListItemIDs",this.list_item_ids,"显示常用物品ID列表");
         Cc.addSlashCommand("ListSkillIDs",this.list_skill_ids,"显示常用技能ID列表");
         Cc.addSlashCommand("SearchID",this.search_id,"搜索ID - 用法: /SearchID 关键词");

         // 快速添加常用物品
         Cc.addSlashCommand("AddArmsBox",this.add_arms_box,"添加武器箱 - 用法: /AddArmsBox 数量");
         Cc.addSlashCommand("AddEquipBox",this.add_equip_box,"添加装备箱 - 用法: /AddEquipBox 数量");
         Cc.addSlashCommand("AddExpCard",this.add_exp_card,"添加经验卡 - 用法: /AddExpCard 等级 数量");
         Cc.addSlashCommand("AddDoubleCard",this.add_double_card,"添加双倍卡 - 用法: /AddDoubleCard 类型 数量");
         Cc.addSlashCommand("AddRebirthStone",this.add_rebirth_stone,"添加重生石 - 用法: /AddRebirthStone 数量");

         // 基于真实ID的特殊物品
         Cc.addSlashCommand("AddSpecialStones",this.add_special_stones,"添加特殊石头 - 用法: /AddSpecialStones 类型 数量");
         Cc.addSlashCommand("AddTimeItems",this.add_time_items,"添加时装碎片 - 用法: /AddTimeItems 时装名 数量");
         Cc.addSlashCommand("AddVehicleParts",this.add_vehicle_parts,"添加载具碎片 - 用法: /AddVehicleParts 载具名 数量");
         Cc.addSlashCommand("AddSpecialCards",this.add_special_cards,"添加特殊卡片 - 用法: /AddSpecialCards 卡片类型 数量");

         // 快速刷取常用物品
         Cc.addSlashCommand("QuickArms",this.quick_arms,"快速刷常用武器 - 用法: /QuickArms 品质");
         Cc.addSlashCommand("QuickEquips",this.quick_equips,"快速刷常用装备 - 用法: /QuickEquips 品质");
         Cc.addSlashCommand("QuickParts",this.quick_parts,"快速刷常用零件");
         Cc.addSlashCommand("QuickGenes",this.quick_genes,"快速刷常用基因体");

         // 系统功能
         Cc.addSlashCommand("SaveGame",this.save_game,"保存游戏");
         Cc.addSlashCommand("GetSaveData",this.get_save_data,"获取存档数据(复制到剪贴板)");
         Cc.addSlashCommand("ExportAllIDs",this.export_all_ids,"导出所有游戏ID到桌面");
         Cc.addSlashCommand("Music",this.sound_s,"静音");
      }
      
      private function create_goods(param1:String) : void
      {
         try {
            var _loc2_:Array = this.parseParams(param1);
            var _loc3_:* = _loc2_[0]; // 道具名称
            var _loc4_:* = _loc2_[1]; // 道具数量

            // 方法1: 尝试使用GiftAddit.addByDefine (推荐)
            var GiftAddDefineClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.gift.define.GiftAddDefine") as Class;
            var GiftAdditClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.gift.GiftAddit") as Class;

            if (GiftAddDefineClass && GiftAdditClass) {
               var giftDefine:* = new GiftAddDefineClass();
               // 格式: "things;道具名;数量" 或 "base;基础属性名;数量"
               giftDefine.inData_byStr("things;" + _loc3_ + ";" + _loc4_);
               GiftAdditClass.addByDefine(giftDefine, this.getPlayerData());
               Cc.log("成功添加道具: " + _loc3_ + " x" + _loc4_);
               return;
            }

            // 方法2: 尝试使用GoodsAddit.addByGoodsData (备用)
            var GoodsDataClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll._app.goods.GoodsData") as Class;
            var GoodsDefineClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll._app.goods.define.GoodsDefine") as Class;
            var GoodsAdditClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll._app.goods.GoodsAddit") as Class;

            if (GoodsDataClass && GoodsDefineClass && GoodsAdditClass) {
               var goodsData:* = new GoodsDataClass();
               var goodsDefine:* = new GoodsDefineClass();
               goodsDefine.name = _loc3_;
               goodsDefine.defineLabel = _loc3_;
               goodsDefine.dataType = "things";
               goodsData.def = goodsDefine;
               goodsData.nowNum = parseInt(_loc4_);
               GoodsAdditClass.addByGoodsData(goodsData, true);
               Cc.log("成功添加道具: " + _loc3_ + " x" + _loc4_);
               return;
            }

            // 方法3: 直接添加到背包 (最直接的方法)
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.thingsBag) {
               playerData.thingsBag.addDataByName(_loc3_, parseInt(_loc4_));
               Cc.log("成功添加道具到背包: " + _loc3_ + " x" + _loc4_);
               return;
            }

            Cc.log("所有方法都失败了，请检查道具名称是否正确");

         } catch (error:Error) {
            Cc.log("添加道具失败: " + error.message);
         }
      }

      private function getPlayerData():* {
         try {
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.PG && GamingClass.PG.da) {
               return GamingClass.PG.da;
            }
         } catch (error:Error) {
            Cc.log("获取玩家数据失败: " + error.message);
         }
         return null;
      }
      
      private function add_coin(param1:String) : void
      {
         try {
            var amount:int = parseInt(param1);
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.main) {
               playerData.main.addCoin(amount);
               Cc.log("成功添加金币: " + amount);
            } else {
               Cc.log("无法获取玩家数据");
            }
         } catch (error:Error) {
            Cc.log("添加金币失败: " + error.message);
         }
      }

      private function add_exp(param1:String) : void
      {
         try {
            var amount:int = parseInt(param1);
            var playerData:* = this.getPlayerData();
            if (playerData) {
               playerData.addAllExp(amount);
               Cc.log("成功添加经验: " + amount);
            } else {
               Cc.log("无法获取玩家数据");
            }
         } catch (error:Error) {
            Cc.log("添加经验失败: " + error.message);
         }
      }

      private function get_player_info(param1:String) : void
      {
         try {
            var playerData:* = this.getPlayerData();
            if (playerData) {
               var info:String = "=== 玩家信息 ===\n";
               if (playerData.main) {
                  info += "金币: " + playerData.main.coin + "\n";
                  info += "等级: " + playerData.level + "\n";
                  info += "经验: " + playerData.exp + "\n";
               }
               if (playerData.thingsBag) {
                  info += "道具背包物品数: " + playerData.thingsBag.dataArr.length + "\n";
               }
               if (playerData.armsBag) {
                  info += "武器背包物品数: " + playerData.armsBag.dataArr.length + "\n";
               }
               if (playerData.equipBag) {
                  info += "装备背包物品数: " + playerData.equipBag.dataArr.length + "\n";
               }
               Cc.log(info);
            } else {
               Cc.log("无法获取玩家数据");
            }
         } catch (error:Error) {
            Cc.log("获取玩家信息失败: " + error.message);
         }
      }

      private function list_bag(param1:String) : void
      {
         try {
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.thingsBag) {
               var info:String = "=== 背包物品 ===\n";
               var items:Array = playerData.thingsBag.dataArr;
               for (var i:int = 0; i < Math.min(items.length, 20); i++) {
                  var item:* = items[i];
                  if (item && item.save) {
                     info += item.save.name + " x" + item.save.nowNum + "\n";
                  }
               }
               if (items.length > 20) {
                  info += "... 还有 " + (items.length - 20) + " 个物品\n";
               }
               Cc.log(info);
            } else {
               Cc.log("无法获取背包数据");
            }
         } catch (error:Error) {
            Cc.log("查看背包失败: " + error.message);
         }
      }

      // 人物相关功能
      private function set_level(param1:String) : void
      {
         try {
            var level:int = parseInt(param1);
            var playerData:* = this.getPlayerData();
            if (playerData) {
               playerData.level = level;
               Cc.log("成功设置等级为: " + level);
            } else {
               Cc.log("无法获取玩家数据");
            }
         } catch (error:Error) {
            Cc.log("设置等级失败: " + error.message);
         }
      }

      private function set_name(param1:String) : void
      {
         try {
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.main) {
               playerData.main.save.name = param1;
               Cc.log("成功设置昵称为: " + param1);
            } else {
               Cc.log("无法获取玩家数据");
            }
         } catch (error:Error) {
            Cc.log("设置昵称失败: " + error.message);
         }
      }

      private function add_title(param1:String) : void
      {
         try {
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.head) {
               var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
               if (GamingClass && GamingClass.api && GamingClass.api.save) {
                  var dateStr:String = GamingClass.api.save.getNowServerDate().getStr();

                  // 一键添加所有称号
                  var allTitles:Array = [
                     "新手上路", "初出茅庐", "小有名气", "声名鹊起", "名震一方",
                     "威名远扬", "举世闻名", "传奇英雄", "末日救星", "神话传说",
                     "屠尸新手", "屠尸能手", "屠尸专家", "屠尸大师", "屠尸宗师",
                     "屠尸圣手", "屠尸传说", "屠尸之神", "杀戮机器", "死神降临",
                     "武器收集者", "武器专家", "武器大师", "武器宗师", "武器之神",
                     "装备收集者", "装备专家", "装备大师", "装备宗师", "装备之神",
                     "速度之王", "敏捷之神", "闪电侠", "疾风剑豪", "风驰电掣",
                     "坚不可摧", "钢铁之躯", "不死之身", "永恒守护", "无敌战神",
                     "精准射手", "神枪手", "百步穿杨", "弹无虚发", "狙击之神",
                     "爆破专家", "爆破大师", "爆破之王", "毁灭之神", "末日审判",
                     "连击高手", "连击大师", "连击之王", "无限连击", "连击之神",
                     "生存专家", "生存大师", "生存之王", "不死传说", "永生不灭",
                     "探索者", "冒险家", "探险大师", "世界行者", "次元旅者",
                     "收集狂", "收集大师", "收集之王", "万物收藏", "宇宙收藏家",
                     "挑战者", "征服者", "统治者", "霸主", "至尊王者",
                     "幸运儿", "天选之子", "命运宠儿", "幸运女神", "奇迹创造者",
                     "富甲一方", "财神爷", "金钱帝国", "首富", "财富之神",
                     "时间管理大师", "效率专家", "速通王者", "时空掌控", "时间之神",
                     "团队领袖", "指挥官", "战略大师", "军团统帅", "无敌将军",
                     "孤胆英雄", "独行侠", "单人军团", "一人成军", "独步天下",
                     "新年快乐", "春节大吉", "元宵节快乐", "清明踏青", "劳动节快乐",
                     "儿童节快乐", "端午安康", "中秋团圆", "国庆快乐", "万圣节惊魂",
                     "感恩节", "圣诞快乐", "生日快乐", "周年庆典", "特殊纪念",
                     "内测玩家", "公测先锋", "老玩家", "资深玩家", "骨灰级玩家",
                     "充值达人", "VIP贵族", "至尊会员", "钻石玩家", "皇冠玩家",
                     "论坛达人", "社区精英", "意见领袖", "游戏大神", "传奇玩家"
                  ];

                  var addedCount:int = 0;
                  for each(var title:String in allTitles) {
                     try {
                        playerData.head.addHead(title, dateStr);
                        addedCount++;
                     } catch (e:Error) {
                        // 忽略单个称号添加失败的错误，继续添加其他称号
                     }
                  }

                  Cc.log("成功添加 " + addedCount + " 个称号！");
                  Cc.log("所有称号已添加完成！");
               }
            } else {
               Cc.log("无法获取玩家数据");
            }
         } catch (error:Error) {
            Cc.log("添加称号失败: " + error.message);
         }
      }

      // 刷武器功能
      private function create_arms(param1:String) : void
      {
         try {
            var _loc2_:Array = this.parseParams(param1);
            var _loc3_:* = _loc2_[0]; // 武器名称
            var _loc4_:* = _loc2_[1]; // 武器数量
            var count:int = parseInt(_loc4_) || 1;

            // 获取Gaming对象
            var Gaming:* = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming");
            var playerData:* = Gaming.PG.da;

            if (!playerData || !playerData.armsBag) {
               Cc.log("无法获取玩家数据");
               return;
            }

            // 使用Gaming.defineGroup.armsCreator创建武器
            for (var i:int = 0; i < count; i++) {
               var armsSave:* = Gaming.defineGroup.armsCreator.getSuperSaveByArmsRangeName(playerData.level, _loc3_);
               if (armsSave) {
                  armsSave.lockB = true;
                  playerData.armsBag.addSave(armsSave);

                  // 添加武器资源
                  var GameArmsCtrl:* = this.l.contentLoaderInfo.applicationDomain.getDefinition("gameAll.arms.GameArmsCtrl");
                  GameArmsCtrl.addArmsSaveResoure(armsSave);
               } else {
                  Cc.log("无法创建武器: " + _loc3_);
                  return;
               }
            }

            Cc.log("成功添加武器: " + _loc3_ + " x" + count);

         } catch (error:Error) {
            Cc.log("添加武器失败: " + error.message);
         }
      }

      // 刷装备功能
      private function create_equip(param1:String) : void
      {
         try {
            var _loc2_:Array = this.parseParams(param1);
            var _loc3_:* = _loc2_[0]; // 装备名称
            var _loc4_:* = _loc2_[1]; // 装备数量

            // 方法1: 使用GiftAddit.addByDefine
            var GiftAddDefineClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.gift.define.GiftAddDefine") as Class;
            var GiftAdditClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.gift.GiftAddit") as Class;

            if (GiftAddDefineClass && GiftAdditClass) {
               var giftDefine:* = new GiftAddDefineClass();
               giftDefine.inData_byStr("equip;" + _loc3_ + ";" + _loc4_);
               GiftAdditClass.addByDefine(giftDefine, this.getPlayerData());
               Cc.log("成功添加装备: " + _loc3_ + " x" + _loc4_);
               return;
            }

            // 方法2: 直接添加到装备背包
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.equipBag) {
               playerData.equipBag.addDataByName(_loc3_, parseInt(_loc4_));
               Cc.log("成功添加装备到背包: " + _loc3_ + " x" + _loc4_);
               return;
            }

            Cc.log("添加装备失败，请检查装备名称是否正确");

         } catch (error:Error) {
            Cc.log("添加装备失败: " + error.message);
         }
      }

      // 刷零件功能
      private function create_parts(param1:String) : void
      {
         try {
            // 支持空格和逗号两种分隔方式
            var _loc2_:Array;
            if (param1.indexOf(",") >= 0) {
               _loc2_ = param1.split(",");
            } else {
               _loc2_ = param1.split(" ");
            }
            var _loc3_:* = _loc2_[0]; // 零件名称
            var _loc4_:* = _loc2_[1]; // 零件数量

            // 方法1: 使用GiftAddit.addByDefine
            var GiftAddDefineClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.gift.define.GiftAddDefine") as Class;
            var GiftAdditClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.gift.GiftAddit") as Class;

            if (GiftAddDefineClass && GiftAdditClass) {
               var giftDefine:* = new GiftAddDefineClass();
               giftDefine.inData_byStr("parts;" + _loc3_ + ";" + _loc4_);
               GiftAdditClass.addByDefine(giftDefine, this.getPlayerData());
               Cc.log("成功添加零件: " + _loc3_ + " x" + _loc4_);
               return;
            }

            // 方法2: 直接添加到零件背包
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.partsBag) {
               playerData.partsBag.addDataByName(_loc3_, parseInt(_loc4_));
               Cc.log("成功添加零件到背包: " + _loc3_ + " x" + _loc4_);
               return;
            }

            Cc.log("添加零件失败，请检查零件名称是否正确");

         } catch (error:Error) {
            Cc.log("添加零件失败: " + error.message);
         }
      }

      // 刷基因体功能
      private function create_genes(param1:String) : void
      {
         try {
            // 支持空格和逗号两种分隔方式
            var _loc2_:Array;
            if (param1.indexOf(",") >= 0) {
               _loc2_ = param1.split(",");
            } else {
               _loc2_ = param1.split(" ");
            }
            var _loc3_:* = _loc2_[0]; // 基因体名称
            var _loc4_:* = _loc2_[1]; // 基因体数量

            // 方法1: 使用GiftAddit.addByDefine
            var GiftAddDefineClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.gift.define.GiftAddDefine") as Class;
            var GiftAdditClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.gift.GiftAddit") as Class;

            if (GiftAddDefineClass && GiftAdditClass) {
               var giftDefine:* = new GiftAddDefineClass();
               giftDefine.inData_byStr("genes;" + _loc3_ + ";" + _loc4_);
               GiftAdditClass.addByDefine(giftDefine, this.getPlayerData());
               Cc.log("成功添加基因体: " + _loc3_ + " x" + _loc4_);
               return;
            }

            // 方法2: 直接添加到基因体背包
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.genesBag) {
               playerData.genesBag.addDataByName(_loc3_, parseInt(_loc4_));
               Cc.log("成功添加基因体到背包: " + _loc3_ + " x" + _loc4_);
               return;
            }

            Cc.log("添加基因体失败，请检查基因体名称是否正确");

         } catch (error:Error) {
            Cc.log("添加基因体失败: " + error.message);
         }
      }

      // 刷载具功能
      private function create_vehicle(param1:String) : void
      {
         try {
            // 支持空格和逗号两种分隔方式
            var _loc2_:Array;
            if (param1.indexOf(",") >= 0) {
               _loc2_ = param1.split(",");
            } else {
               _loc2_ = param1.split(" ");
            }
            var _loc3_:* = _loc2_[0]; // 载具名称
            var _loc4_:* = _loc2_[1]; // 载具数量

            // 方法1: 使用GiftAddit.addByDefine
            var GiftAddDefineClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.gift.define.GiftAddDefine") as Class;
            var GiftAdditClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.gift.GiftAddit") as Class;

            if (GiftAddDefineClass && GiftAdditClass) {
               var giftDefine:* = new GiftAddDefineClass();
               giftDefine.inData_byStr("vehicle;" + _loc3_ + ";" + _loc4_);
               GiftAdditClass.addByDefine(giftDefine, this.getPlayerData());
               Cc.log("成功添加载具: " + _loc3_ + " x" + _loc4_);
               return;
            }

            // 方法2: 直接添加到载具背包
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.vehicleBag) {
               playerData.vehicleBag.addDataByName(_loc3_, parseInt(_loc4_));
               Cc.log("成功添加载具到背包: " + _loc3_ + " x" + _loc4_);
               return;
            }

            Cc.log("添加载具失败，请检查载具名称是否正确");

         } catch (error:Error) {
            Cc.log("添加载具失败: " + error.message);
         }
      }

      // 刷时装功能
      private function create_fashion(param1:String) : void
      {
         try {
            // 支持空格和逗号两种分隔方式
            var _loc2_:Array;
            if (param1.indexOf(",") >= 0) {
               _loc2_ = param1.split(",");
            } else {
               _loc2_ = param1.split(" ");
            }
            var _loc3_:* = _loc2_[0]; // 时装名称
            var _loc4_:* = _loc2_[1]; // 时装数量

            // 方法1: 使用GiftAddit.addByDefine
            var GiftAddDefineClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.gift.define.GiftAddDefine") as Class;
            var GiftAdditClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.gift.GiftAddit") as Class;

            if (GiftAddDefineClass && GiftAdditClass) {
               var giftDefine:* = new GiftAddDefineClass();
               giftDefine.inData_byStr("fashion;" + _loc3_ + ";" + _loc4_);
               GiftAdditClass.addByDefine(giftDefine, this.getPlayerData());
               Cc.log("成功添加时装: " + _loc3_ + " x" + _loc4_);
               return;
            }

            // 方法2: 直接添加到时装背包
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.fashionBag) {
               playerData.fashionBag.addDataByName(_loc3_, parseInt(_loc4_));
               Cc.log("成功添加时装到背包: " + _loc3_ + " x" + _loc4_);
               return;
            }

            Cc.log("添加时装失败，请检查时装名称是否正确");

         } catch (error:Error) {
            Cc.log("添加时装失败: " + error.message);
         }
      }

      private function add_single_title(param1:String) : void
      {
         try {
            if (!param1 || param1.length == 0) {
               Cc.log("请输入称号名称");
               return;
            }

            var playerData:* = this.getPlayerData();
            if (playerData && playerData.head) {
               var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
               if (GamingClass && GamingClass.api && GamingClass.api.save) {
                  var dateStr:String = GamingClass.api.save.getNowServerDate().getStr();
                  playerData.head.addHead(param1, dateStr);
                  Cc.log("成功添加称号: " + param1);
               }
            } else {
               Cc.log("无法获取玩家数据");
            }
         } catch (error:Error) {
            Cc.log("添加称号失败: " + error.message);
         }
      }

      // 刷装置功能
      private function create_device(param1:String) : void
      {
         try {
            // 支持空格和逗号两种分隔方式
            var _loc2_:Array;
            if (param1.indexOf(",") >= 0) {
               _loc2_ = param1.split(",");
            } else {
               _loc2_ = param1.split(" ");
            }
            var _loc3_:* = _loc2_[0]; // 装置名称
            var _loc4_:* = _loc2_[1]; // 装置数量

            // 方法1: 使用GiftAddit.addByDefine
            var GiftAddDefineClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.gift.define.GiftAddDefine") as Class;
            var GiftAdditClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.gift.GiftAddit") as Class;

            if (GiftAddDefineClass && GiftAdditClass) {
               var giftDefine:* = new GiftAddDefineClass();
               giftDefine.inData_byStr("device;" + _loc3_ + ";" + _loc4_);
               GiftAdditClass.addByDefine(giftDefine, this.getPlayerData());
               Cc.log("成功添加装置: " + _loc3_ + " x" + _loc4_);
               return;
            }

            // 方法2: 直接添加到装置背包
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.deviceBag) {
               playerData.deviceBag.addDataByName(_loc3_, parseInt(_loc4_));
               Cc.log("成功添加装置到背包: " + _loc3_ + " x" + _loc4_);
               return;
            }

            Cc.log("添加装置失败，请检查装置名称是否正确");

         } catch (error:Error) {
            Cc.log("添加装置失败: " + error.message);
         }
      }

      // 刷副将功能
      private function create_partner(param1:String) : void
      {
         try {
            // 支持空格和逗号两种分隔方式
            var _loc2_:Array;
            if (param1.indexOf(",") >= 0) {
               _loc2_ = param1.split(",");
            } else {
               _loc2_ = param1.split(" ");
            }
            var _loc3_:* = _loc2_[0]; // 副将名称
            var _loc4_:* = _loc2_[1]; // 副将数量

            // 方法1: 使用GiftAddit.addByDefine
            var GiftAddDefineClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.gift.define.GiftAddDefine") as Class;
            var GiftAdditClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll.gift.GiftAddit") as Class;

            if (GiftAddDefineClass && GiftAdditClass) {
               var giftDefine:* = new GiftAddDefineClass();
               giftDefine.inData_byStr("partner;" + _loc3_ + ";" + _loc4_);
               GiftAdditClass.addByDefine(giftDefine, this.getPlayerData());
               Cc.log("成功添加副将: " + _loc3_ + " x" + _loc4_);
               return;
            }

            // 方法2: 直接添加到副将背包
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.partnerBag) {
               playerData.partnerBag.addDataByName(_loc3_, parseInt(_loc4_));
               Cc.log("成功添加副将到背包: " + _loc3_ + " x" + _loc4_);
               return;
            }

            // 方法3: 尝试添加到心零背包 (副将可能存储在心零系统中)
            if (playerData && playerData.lingBag) {
               playerData.lingBag.addDataByName(_loc3_, parseInt(_loc4_));
               Cc.log("成功添加副将到心零背包: " + _loc3_ + " x" + _loc4_);
               return;
            }

            Cc.log("添加副将失败，请检查副将名称是否正确");

         } catch (error:Error) {
            Cc.log("添加副将失败: " + error.message);
         }
      }

      // 货币相关功能
      private function set_score(param1:String) : void
      {
         try {
            var amount:Number = parseFloat(param1);
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.main) {
               playerData.main.save.score = amount;
               Cc.log("成功设置积分为: " + amount);
            } else {
               Cc.log("无法获取玩家数据");
            }
         } catch (error:Error) {
            Cc.log("设置积分失败: " + error.message);
         }
      }

      private function set_anni_coin(param1:String) : void
      {
         try {
            var amount:Number = parseFloat(param1);
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.main) {
               playerData.main.save.anniCoin = amount;
               Cc.log("成功设置纪念币为: " + amount);
            } else {
               Cc.log("无法获取玩家数据");
            }
         } catch (error:Error) {
            Cc.log("设置纪念币失败: " + error.message);
         }
      }

      private function set_ten_coin(param1:String) : void
      {
         try {
            var amount:Number = parseFloat(param1);
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.main) {
               playerData.main.save.tenCoin = amount;
               Cc.log("成功设置十年币为: " + amount);
            } else {
               Cc.log("无法获取玩家数据");
            }
         } catch (error:Error) {
            Cc.log("设置十年币失败: " + error.message);
         }
      }

      // 背包相关功能
      private function set_bag_size(param1:String) : void
      {
         try {
            // 支持空格和逗号两种分隔方式
            var params:Array;
            if (param1.indexOf(",") >= 0) {
               params = param1.split(",");
            } else {
               params = param1.split(" ");
            }
            var bagType:String = params[0]; // arms, equip, things, gene, parts, skill, pet, bossCard
            var size:int = parseInt(params[1]);
            var playerData:* = this.getPlayerData();
            if (playerData) {
               var bagName:String = bagType + "Bag";
               if (playerData[bagName]) {
                  playerData[bagName].save.bag = size;
                  Cc.log("成功设置" + bagType + "背包大小为: " + size);
               } else {
                  Cc.log("背包类型不存在: " + bagType);
               }
            } else {
               Cc.log("无法获取玩家数据");
            }
         } catch (error:Error) {
            Cc.log("设置背包大小失败: " + error.message);
         }
      }

      private function clear_bag(param1:String) : void
      {
         try {
            var bagType:String = param1; // arms, equip, things, gene, parts, skill, pet, bossCard
            var playerData:* = this.getPlayerData();
            if (playerData) {
               var bagName:String = bagType + "Bag";
               if (playerData[bagName]) {
                  playerData[bagName].clearData();
                  Cc.log("成功清空" + bagType + "背包");
               } else {
                  Cc.log("背包类型不存在: " + bagType);
               }
            } else {
               Cc.log("无法获取玩家数据");
            }
         } catch (error:Error) {
            Cc.log("清空背包失败: " + error.message);
         }
      }

      // 地图相关功能
      private function unlock_all_maps(param1:String) : void
      {
         try {
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.worldMap) {
               playerData.worldMap.unlockAll();
               Cc.log("成功解锁所有地图");
            } else {
               Cc.log("无法获取地图数据");
            }
         } catch (error:Error) {
            Cc.log("解锁地图失败: " + error.message);
         }
      }

      private function complete_all_maps(param1:String) : void
      {
         try {
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.worldMap) {
               playerData.worldMap.winAll();
               Cc.log("成功通关所有地图");
            } else {
               Cc.log("无法获取地图数据");
            }
         } catch (error:Error) {
            Cc.log("通关地图失败: " + error.message);
         }
      }

      // 成就相关功能
      private function complete_all_achieve(param1:String) : void
      {
         try {
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.achieve) {
               playerData.achieve.completeAll();
               Cc.log("成功完成所有成就");
            } else {
               Cc.log("无法获取成就数据");
            }
         } catch (error:Error) {
            Cc.log("完成成就失败: " + error.message);
         }
      }

      // 游戏内功能
      private function kill_all_enemy(param1:String) : void
      {
         try {
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.TG && GamingClass.TG.hurt) {
               GamingClass.TG.hurt.killAllEnemy(null);
               Cc.log("成功秒杀全图敌人");
            } else {
               Cc.log("无法获取游戏战斗数据");
            }
         } catch (error:Error) {
            Cc.log("秒杀敌人失败: " + error.message);
         }
      }

      private function win_level(param1:String) : void
      {
         try {
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.LG) {
               GamingClass.LG.levelWin("r_over");
               Cc.log("成功通关当前关卡");
            } else {
               Cc.log("无法获取关卡数据");
            }
         } catch (error:Error) {
            Cc.log("通关关卡失败: " + error.message);
         }
      }

      private function restart_level(param1:String) : void
      {
         try {
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.LG) {
               GamingClass.LG.restartLevel();
               Cc.log("成功重新开始关卡");
            } else {
               Cc.log("无法获取关卡数据");
            }
         } catch (error:Error) {
            Cc.log("重新开始关卡失败: " + error.message);
         }
      }

      // 系统功能
      private function save_game(param1:String) : void
      {
         try {
            var UIOrderClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("UI.UIOrder") as Class;
            if (UIOrderClass) {
               UIOrderClass.save(true, false, false, null, null, true);
               Cc.log("成功保存游戏");
            } else {
               Cc.log("无法获取保存功能");
            }
         } catch (error:Error) {
            Cc.log("保存游戏失败: " + error.message);
         }
      }

      // 高级功能实现
      private function set_time_speed(param1:String) : void
      {
         try {
            var speed:Number = parseFloat(param1);
            var CountCtrlClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("UI.count.CountCtrl") as Class;
            if (CountCtrlClass) {
               CountCtrlClass.onlineTimePer = speed;
               Cc.log("成功设置时间加速倍数为: " + speed);
            } else {
               Cc.log("无法获取时间控制类");
            }
         } catch (error:Error) {
            Cc.log("设置时间倍速失败: " + error.message);
         }
      }

      private function set_frame_rate(param1:String) : void
      {
         try {
            var frameRate:int = parseInt(param1);
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.ME && GamingClass.ME.stage) {
               GamingClass.ME.stage.frameRate = frameRate;
               Cc.log("成功设置游戏帧数为: " + frameRate);
            } else {
               Cc.log("无法获取舞台对象");
            }
         } catch (error:Error) {
            Cc.log("设置帧数失败: " + error.message);
         }
      }

      private function unlock_skill_system(param1:String) : void
      {
         try {
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.worldMap && playerData.worldMap.saveGroup) {
               playerData.worldMap.saveGroup.unlockOne("XiChi");
               playerData.worldMap.saveGroup.winOne("XiChi", 0);
               Cc.log("成功解锁技能系统");
            } else {
               Cc.log("无法获取地图数据");
            }
         } catch (error:Error) {
            Cc.log("解锁技能系统失败: " + error.message);
         }
      }

      private function set_arena_count(param1:String) : void
      {
         try {
            var count:Number = parseFloat(param1);
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.PG && GamingClass.PG.save && GamingClass.PG.save.arena) {
               GamingClass.PG.save.arena.todayNum = count;
               Cc.log("成功设置竞技场次数为: " + count);
            } else {
               Cc.log("无法获取竞技场数据");
            }
         } catch (error:Error) {
            Cc.log("设置竞技场次数失败: " + error.message);
         }
      }

      private function set_arena_score(param1:String) : void
      {
         try {
            var score:Number = parseFloat(param1);
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.PG && GamingClass.PG.save && GamingClass.PG.save.arena) {
               GamingClass.PG.save.arena.score = score;
               Cc.log("成功设置竞技场分数为: " + score);
            } else {
               Cc.log("无法获取竞技场数据");
            }
         } catch (error:Error) {
            Cc.log("设置竞技场分数失败: " + error.message);
         }
      }

      private function set_tower_level(param1:String) : void
      {
         try {
            var level:int = parseInt(param1);
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.PG && GamingClass.PG.SAVE && GamingClass.PG.SAVE.tower) {
               GamingClass.PG.SAVE.tower.unendLv = level;
               Cc.log("成功设置天塔层数为: " + level);
            } else {
               Cc.log("无法获取天塔数据");
            }
         } catch (error:Error) {
            Cc.log("设置天塔层数失败: " + error.message);
         }
      }

      private function add_random_weapon(param1:String) : void
      {
         try {
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.testCtrl && GamingClass.testCtrl.arms) {
               GamingClass.testCtrl.arms.addRandomArms();
               Cc.log("成功添加随机武器");
            } else {
               Cc.log("无法获取武器生成器");
            }
         } catch (error:Error) {
            Cc.log("添加随机武器失败: " + error.message);
         }
      }

      private function add_random_equip(param1:String) : void
      {
         try {
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.testCtrl && GamingClass.testCtrl.arms) {
               GamingClass.testCtrl.arms.addRandomEquip();
               Cc.log("成功添加随机装备");
            } else {
               Cc.log("无法获取装备生成器");
            }
         } catch (error:Error) {
            Cc.log("添加随机装备失败: " + error.message);
         }
      }

      private function complete_current_task(param1:String) : void
      {
         try {
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.uiGroup && GamingClass.uiGroup.taskUI) {
               GamingClass.uiGroup.taskUI.test_nowTaskComplete();
               Cc.log("成功完成当前任务");
            } else {
               Cc.log("无法获取任务系统");
            }
         } catch (error:Error) {
            Cc.log("完成任务失败: " + error.message);
         }
      }

      private function unlock_all_tasks(param1:String) : void
      {
         try {
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.task) {
               playerData.task.unlockAllByType("main");
               Cc.log("成功解锁所有主线任务");
            } else {
               Cc.log("无法获取任务数据");
            }
         } catch (error:Error) {
            Cc.log("解锁任务失败: " + error.message);
         }
      }

      private function get_save_data(param1:String) : void
      {
         try {
            var JSON2Class:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("com.adobe.serialization.json.JSON2") as Class;
            var ClassPropertyClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("com.sounto.utils.ClassProperty") as Class;
            var SystemClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("flash.system.System") as Class;
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;

            if (JSON2Class && ClassPropertyClass && SystemClass && GamingClass && GamingClass.PG && GamingClass.PG.save) {
               var saveData:String = JSON2Class.encode(ClassPropertyClass.copyObj(GamingClass.PG.save));
               SystemClass.setClipboard(saveData);
               Cc.log("存档数据已复制到剪贴板");
            } else {
               Cc.log("无法获取存档数据");
            }
         } catch (error:Error) {
            Cc.log("获取存档数据失败: " + error.message);
         }
      }

      // 尸宠类功能实现
      private function set_pet_name(param1:String) : void
      {
         try {
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.pet && playerData.pet.dataArr && playerData.pet.dataArr.length > 0) {
               var pet:* = playerData.pet.dataArr[0];
               if (pet && pet.save) {
                  pet.save.cnName = param1;
                  Cc.log("成功设置尸宠昵称为: " + param1);
               } else {
                  Cc.log("没有找到宠物");
               }
            } else {
               Cc.log("无法获取宠物数据");
            }
         } catch (error:Error) {
            Cc.log("设置尸宠昵称失败: " + error.message);
         }
      }

      private function set_pet_level(param1:String) : void
      {
         try {
            var level:int = parseInt(param1);
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.pet && playerData.pet.dataArr && playerData.pet.dataArr.length > 0) {
               var pet:* = playerData.pet.dataArr[0];
               if (pet && pet.save) {
                  pet.save.level = level;
                  Cc.log("成功设置尸宠等级为: " + level);
               } else {
                  Cc.log("没有找到宠物");
               }
            } else {
               Cc.log("无法获取宠物数据");
            }
         } catch (error:Error) {
            Cc.log("设置尸宠等级失败: " + error.message);
         }
      }

      private function set_pet_exp(param1:String) : void
      {
         try {
            var exp:Number = parseFloat(param1);
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.pet && playerData.pet.dataArr && playerData.pet.dataArr.length > 0) {
               var pet:* = playerData.pet.dataArr[0];
               if (pet && pet.save) {
                  pet.save.exp = exp;
                  Cc.log("成功设置尸宠经验为: " + exp);
               } else {
                  Cc.log("没有找到宠物");
               }
            } else {
               Cc.log("无法获取宠物数据");
            }
         } catch (error:Error) {
            Cc.log("设置尸宠经验失败: " + error.message);
         }
      }

      private function clear_all_pets(param1:String) : void
      {
         try {
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.pet) {
               playerData.pet.clearData();
               Cc.log("成功清除所有宠物");
            } else {
               Cc.log("无法获取宠物数据");
            }
         } catch (error:Error) {
            Cc.log("清除宠物失败: " + error.message);
         }
      }

      private function set_pet_drop_rate(param1:String) : void
      {
         try {
            var rate:Number = parseFloat(param1);
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.testCtrl && GamingClass.testCtrl.cheating) {
               GamingClass.testCtrl.cheating.petDropPro = rate;
               Cc.log("成功设置基因体掉落概率为: " + rate);
            } else {
               Cc.log("无法获取测试控制器");
            }
         } catch (error:Error) {
            Cc.log("设置掉落概率失败: " + error.message);
         }
      }

      // 厨艺类功能实现
      private function add_all_food(param1:String) : void
      {
         try {
            var amount:Number = parseFloat(param1);
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.food) {
               playerData.food.addRawAll(amount);
               Cc.log("成功添加所有食材数量: " + amount);
            } else {
               Cc.log("无法获取厨艺数据");
            }
         } catch (error:Error) {
            Cc.log("添加食材失败: " + error.message);
         }
      }

      private function set_cooking_skill(param1:String) : void
      {
         try {
            var skill:Number = parseFloat(param1);
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.food && playerData.food.save) {
               playerData.food.save.profiAll = skill;
               playerData.food.addProfi(0);
               Cc.log("成功设置总厨艺值为: " + skill);
            } else {
               Cc.log("无法获取厨艺数据");
            }
         } catch (error:Error) {
            Cc.log("设置厨艺值失败: " + error.message);
         }
      }

      // 飞船类功能实现
      private function set_space_level(param1:String) : void
      {
         try {
            var level:int = parseInt(param1);
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.space && playerData.space.craft) {
               var craftData:* = playerData.space.craft.getNowData();
               if (craftData) {
                  craftData.setLevel(level);
                  Cc.log("成功设置飞船等级为: " + level);
               } else {
                  Cc.log("无法获取飞船数据");
               }
            } else {
               Cc.log("无法获取飞船系统");
            }
         } catch (error:Error) {
            Cc.log("设置飞船等级失败: " + error.message);
         }
      }

      private function add_space_exp(param1:String) : void
      {
         try {
            var exp:Number = parseFloat(param1);
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.space) {
               playerData.space.addNowCraftExp(exp);
               Cc.log("成功添加飞船经验: " + exp);
            } else {
               Cc.log("无法获取飞船数据");
            }
         } catch (error:Error) {
            Cc.log("添加飞船经验失败: " + error.message);
         }
      }

      // 军队类功能实现
      private function complete_union_tasks(param1:String) : void
      {
         try {
            var UnionTaskDataClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll._app.union.task.UnionTaskData") as Class;
            if (UnionTaskDataClass) {
               UnionTaskDataClass.TEST_COMPLETE_B = true;
               Cc.log("成功完成所有军队任务");
            } else {
               Cc.log("无法获取军队任务类");
            }
         } catch (error:Error) {
            Cc.log("完成军队任务失败: " + error.message);
         }
      }

      private function set_military_rank(param1:String) : void
      {
         try {
            var rank:int = parseInt(param1);
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.defineGroup && GamingClass.defineGroup.union && GamingClass.defineGroup.union.military) {
               var militaryDefine:* = GamingClass.defineGroup.union.military.getDefine(rank + "");
               if (militaryDefine && GamingClass.testCtrl && GamingClass.testCtrl.cheating) {
                  GamingClass.testCtrl.cheating.tempContribution = militaryDefine.totalMust;
                  Cc.log("成功设置军衔等级为: " + rank + " (" + militaryDefine.cnName + ")");
               } else {
                  Cc.log("无法找到军衔定义");
               }
            } else {
               Cc.log("无法获取军队系统");
            }
         } catch (error:Error) {
            Cc.log("设置军衔等级失败: " + error.message);
         }
      }

      private function set_contribution(param1:String) : void
      {
         try {
            var contribution:Number = parseFloat(param1);
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.testCtrl && GamingClass.testCtrl.cheating) {
               GamingClass.testCtrl.cheating.tempContribution = contribution;
               Cc.log("成功设置个人贡献为: " + contribution);
            } else {
               Cc.log("无法获取测试控制器");
            }
         } catch (error:Error) {
            Cc.log("设置个人贡献失败: " + error.message);
         }
      }

      private function clear_union_data(param1:String) : void
      {
         try {
            var playerData:* = this.getPlayerData();
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (playerData && playerData.union && GamingClass && GamingClass.testCtrl && GamingClass.testCtrl.cheating) {
               playerData.union.clearInfo();
               GamingClass.testCtrl.cheating.haveUnionDataB = false;
               Cc.log("成功清除军队数据");
            } else {
               Cc.log("无法获取军队数据");
            }
         } catch (error:Error) {
            Cc.log("清除军队数据失败: " + error.message);
         }
      }

      // 增益类功能实现
      private function set_double_material(param1:String) : void
      {
         try {
            var time:Number = parseFloat(param1);
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.PG && GamingClass.PG.SAVE && GamingClass.PG.SAVE.time) {
               GamingClass.PG.SAVE.time.doubleMaterialsDropTime = time;
               Cc.log("成功设置双倍材料时间为: " + time);
            } else {
               Cc.log("无法获取时间数据");
            }
         } catch (error:Error) {
            Cc.log("设置双倍材料时间失败: " + error.message);
         }
      }

      private function set_double_exp(param1:String) : void
      {
         try {
            var time:Number = parseFloat(param1);
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.PG && GamingClass.PG.SAVE && GamingClass.PG.SAVE.time) {
               GamingClass.PG.SAVE.time.doubleExpTime = time;
               Cc.log("成功设置双倍经验时间为: " + time);
            } else {
               Cc.log("无法获取时间数据");
            }
         } catch (error:Error) {
            Cc.log("设置双倍经验时间失败: " + error.message);
         }
      }

      private function set_double_weapon(param1:String) : void
      {
         try {
            var time:Number = parseFloat(param1);
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.PG && GamingClass.PG.SAVE && GamingClass.PG.SAVE.time) {
               GamingClass.PG.SAVE.time.doubleArmsDropTime = time;
               Cc.log("成功设置双倍武器时间为: " + time);
            } else {
               Cc.log("无法获取时间数据");
            }
         } catch (error:Error) {
            Cc.log("设置双倍武器时间失败: " + error.message);
         }
      }

      private function set_double_equip(param1:String) : void
      {
         try {
            var time:Number = parseFloat(param1);
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.PG && GamingClass.PG.SAVE && GamingClass.PG.SAVE.time) {
               GamingClass.PG.SAVE.time.doubleEquipDropTime = time;
               Cc.log("成功设置双倍装备时间为: " + time);
            } else {
               Cc.log("无法获取时间数据");
            }
         } catch (error:Error) {
            Cc.log("设置双倍装备时间失败: " + error.message);
         }
      }

      // 魂卡类功能实现
      private function set_normal_card_count(param1:String) : void
      {
         try {
            var count:Number = parseFloat(param1);
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.PG && GamingClass.PG.save && GamingClass.PG.save.bossCard) {
               GamingClass.PG.save.bossCard.num = count;
               Cc.log("成功设置普通已抽次数为: " + count);
            } else {
               Cc.log("无法获取魂卡数据");
            }
         } catch (error:Error) {
            Cc.log("设置普通已抽次数失败: " + error.message);
         }
      }

      private function set_high_card_count(param1:String) : void
      {
         try {
            var count:Number = parseFloat(param1);
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.PG && GamingClass.PG.save && GamingClass.PG.save.bossCard) {
               GamingClass.PG.save.bossCard.hNum = count;
               Cc.log("成功设置高级已抽次数为: " + count);
            } else {
               Cc.log("无法获取魂卡数据");
            }
         } catch (error:Error) {
            Cc.log("设置高级已抽次数失败: " + error.message);
         }
      }

      private function set_card_bag_size(param1:String) : void
      {
         try {
            var size:Number = parseFloat(param1);
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.PG && GamingClass.PG.save && GamingClass.PG.save.bossCard) {
               GamingClass.PG.save.bossCard.bag = size;
               Cc.log("成功设置魂卡背包为: " + size);
            } else {
               Cc.log("无法获取魂卡数据");
            }
         } catch (error:Error) {
            Cc.log("设置魂卡背包失败: " + error.message);
         }
      }

      private function unlimited_high_card(param1:String) : void
      {
         try {
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.PG && GamingClass.PG.save && GamingClass.PG.save.bossCard) {
               GamingClass.PG.save.bossCard.num = 200;
               GamingClass.PG.save.bossCard.hNum = -999999;
               Cc.log("成功设置无限高级抽卡");
            } else {
               Cc.log("无法获取魂卡数据");
            }
         } catch (error:Error) {
            Cc.log("设置无限高级抽卡失败: " + error.message);
         }
      }

      // 地图类扩展功能实现
      private function unlock_secret_maps(param1:String) : void
      {
         try {
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.worldMap) {
               playerData.worldMap.unlockAllSecret();
               Cc.log("成功解锁所有秘境");
            } else {
               Cc.log("无法获取地图数据");
            }
         } catch (error:Error) {
            Cc.log("解锁秘境失败: " + error.message);
         }
      }

      private function set_secret_keys(param1:String) : void
      {
         try {
            var keys:Number = parseFloat(param1);
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.worldMap && playerData.worldMap.save) {
               playerData.worldMap.save.secretKey = keys;
               Cc.log("成功设置秘境钥匙为: " + keys);
            } else {
               Cc.log("无法获取地图数据");
            }
         } catch (error:Error) {
            Cc.log("设置秘境钥匙失败: " + error.message);
         }
      }

      private function set_sweep_count(param1:String) : void
      {
         try {
            var count:Number = parseFloat(param1);
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.worldMap && playerData.worldMap.save) {
               playerData.worldMap.save.sweepCount = count;
               Cc.log("成功设置扫荡次数为: " + count);
            } else {
               Cc.log("无法获取地图数据");
            }
         } catch (error:Error) {
            Cc.log("设置扫荡次数失败: " + error.message);
         }
      }

      private function unlock_all_difficulty(param1:String) : void
      {
         try {
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.worldMap) {
               playerData.worldMap.unlockAllDifficulty();
               Cc.log("成功解锁所有难度");
            } else {
               Cc.log("无法获取地图数据");
            }
         } catch (error:Error) {
            Cc.log("解锁难度失败: " + error.message);
         }
      }

      // 设置幻塔层数
      private function set_phantom_tower(param1:String) : void
      {
         try {
            var level:int = parseInt(param1);
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.PG && GamingClass.PG.SAVE && GamingClass.PG.SAVE.tower) {
               GamingClass.PG.SAVE.tower.blv = level;
               Cc.log("成功设置幻塔层数为: " + level);
            } else {
               Cc.log("无法获取天塔数据");
            }
         } catch (error:Error) {
            Cc.log("设置幻塔层数失败: " + error.message);
         }
      }

      // 时间类功能实现
      private function set_new_day(param1:String) : void
      {
         try {
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            var playerData:* = this.getPlayerData();
            if (GamingClass && GamingClass.api && GamingClass.api.save && playerData) {
               var dateStr:String = GamingClass.api.save.getNowServerDate().getStr();
               playerData.newDayCtrl(dateStr);
               Cc.log("成功设置新的一天");
            } else {
               Cc.log("无法获取时间数据");
            }
         } catch (error:Error) {
            Cc.log("设置新的一天失败: " + error.message);
         }
      }

      private function set_new_week(param1:String) : void
      {
         try {
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            var playerData:* = this.getPlayerData();
            if (GamingClass && GamingClass.api && GamingClass.api.save && playerData) {
               var dateStr:String = GamingClass.api.save.getNowServerDate().getStr();
               playerData.newWeek(dateStr);
               Cc.log("成功设置新的一周");
            } else {
               Cc.log("无法获取时间数据");
            }
         } catch (error:Error) {
            Cc.log("设置新的一周失败: " + error.message);
         }
      }

      private function toggle_local_time(param1:String) : void
      {
         try {
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.api && GamingClass.api.save) {
               GamingClass.api.save.localTimeB = !GamingClass.api.save.localTimeB;
               Cc.log("本地时间开关: " + GamingClass.api.save.localTimeB);
            } else {
               Cc.log("无法获取时间设置");
            }
         } catch (error:Error) {
            Cc.log("切换本地时间失败: " + error.message);
         }
      }

      private function clear_double_time(param1:String) : void
      {
         try {
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.time) {
               playerData.time.clearAllTime();
               Cc.log("成功清除所有双倍时间");
            } else {
               Cc.log("无法获取时间数据");
            }
         } catch (error:Error) {
            Cc.log("清除双倍时间失败: " + error.message);
         }
      }

      // 存档类功能实现
      private function check_save_error(param1:String) : void
      {
         try {
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.PG && GamingClass.PG.save && GamingClass.PG.save.main) {
               var reason:String = GamingClass.PG.save.main.zuobiReason;
               Cc.log("异常原因: " + reason);
            } else {
               Cc.log("无法获取存档数据");
            }
         } catch (error:Error) {
            Cc.log("查询异常原因失败: " + error.message);
         }
      }

      private function init_save(param1:String) : void
      {
         try {
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.PG) {
               GamingClass.PG.initSave();
               Cc.log("成功初始化本存档");
            } else {
               Cc.log("无法获取存档系统");
            }
         } catch (error:Error) {
            Cc.log("初始化存档失败: " + error.message);
         }
      }

      private function repair_save(param1:String) : void
      {
         try {
            var PlayerDataSuppleClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("dataAll._player.supple.PlayerDataSupple") as Class;
            var playerData:* = this.getPlayerData();
            if (PlayerDataSuppleClass && playerData) {
               PlayerDataSuppleClass.dealSummer(playerData);
               Cc.log("成功修复存档数据");
            } else {
               Cc.log("无法获取修复工具");
            }
         } catch (error:Error) {
            Cc.log("修复存档数据失败: " + error.message);
         }
      }

      private function remove_save_error(param1:String) : void
      {
         try {
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.PG && GamingClass.PG.save && GamingClass.PG.save.main) {
               GamingClass.PG.save.main.isZuobiB = false;
               Cc.log("成功解除异常存档");
            } else {
               Cc.log("无法获取存档数据");
            }
         } catch (error:Error) {
            Cc.log("解除异常存档失败: " + error.message);
         }
      }

      private function set_save_error(param1:String) : void
      {
         try {
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.PG && GamingClass.PG.save && GamingClass.PG.save.main) {
               GamingClass.PG.save.main.isZuobiB = true;
               Cc.log("成功设置存档异常");
            } else {
               Cc.log("无法获取存档数据");
            }
         } catch (error:Error) {
            Cc.log("设置存档异常失败: " + error.message);
         }
      }

      // 极品号功能实现
      private function load_top_save(param1:String) : void
      {
         try {
            var modelIndex:String = param1;
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.api && GamingClass.api.top) {
               GamingClass.api.top.getUserData("205534935", modelIndex, this.yes_getSaveByUid, this.no_getSaveByUid);
               Cc.log("正在加载模型" + modelIndex + "存档...");
            } else {
               Cc.log("无法获取API接口");
            }
         } catch (error:Error) {
            Cc.log("加载极品存档失败: " + error.message);
         }
      }

      private function yes_getSaveByUid(ud0:*) : void
      {
         try {
            var JSON2Class:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("com.adobe.serialization.json.JSON2") as Class;
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;

            if (JSON2Class && GamingClass && GamingClass.uiGroup && GamingClass.PG) {
               var str0:String = JSON2Class.encode(ud0.data);
               var obj0:Object = JSON2Class.decode(str0);
               GamingClass.uiGroup.loginUI.outLoginEvent();
               GamingClass.PG.loginData.newSave(0);
               GamingClass.uiGroup.loginUI.yes_read(obj0);
               Cc.log("极品存档加载成功！");
            } else {
               Cc.log("存档数据处理失败");
            }
         } catch (error:Error) {
            Cc.log("存档加载失败: " + error.message);
         }
      }

      private function no_getSaveByUid(str0:String = "") : void
      {
         Cc.log("存档加载失败: " + str0);
      }

      // 自定义编辑功能实现 (基于实际的SWF文件中的类)
      private function edit_weapon(param1:String) : void
      {
         try {
            var playerData:* = this.getPlayerData();
            if (!playerData || !playerData.armsBag || !playerData.armsBag.dataArr || playerData.armsBag.dataArr.length == 0) {
               Cc.log("无法获取武器数据或背包为空");
               return;
            }

            var weapon:* = playerData.armsBag.dataArr[0]; // 编辑第一个武器
            if (!weapon || !weapon.save) {
               Cc.log("无法获取武器存档数据");
               return;
            }

            var params:Array = param1.split("&");
            for each(var paramPair:String in params) {
               var parts:Array = paramPair.split("*");
               if (parts.length != 2) continue;

               var code:String = parts[0];
               var value:String = parts[1];

               // 直接修改武器存档的属性
               switch(code) {
                  case "00": // 武器名字
                     weapon.save.cnName = value;
                     Cc.log("设置武器名称为: " + value);
                     break;
                  case "01": // 武器品质
                     weapon.save.color = value;
                     Cc.log("设置武器颜色为: " + value);
                     break;
                  case "02": // 武器类型
                     weapon.save.armsType = value;
                     Cc.log("设置武器类型为: " + value);
                     break;
                  case "03": // 武器弹容
                     weapon.save.capacity = parseInt(value);
                     Cc.log("设置武器弹容为: " + value);
                     break;
                  case "04": // 武器单发
                     weapon.save.bulletNum = parseInt(value);
                     Cc.log("设置武器单发为: " + value);
                     break;
                  case "05": // 武器等级
                     weapon.save.itemsLevel = parseFloat(value);
                     Cc.log("设置武器等级为: " + value);
                     break;
                  case "06": // 武器射速
                     weapon.save.attackGap = parseFloat(value);
                     Cc.log("设置武器射速为: " + value);
                     break;
                  case "08": // 换弹速度
                     weapon.save.reloadGap = parseFloat(value);
                     Cc.log("设置换弹速度为: " + value);
                     break;
                  case "09": // 武器伤害
                     weapon.save.hurtRatio = parseFloat(value);
                     Cc.log("设置武器伤害为: " + value);
                     break;
                  case "10": // 强化等级
                     weapon.save.strengthenLv = parseFloat(value);
                     Cc.log("设置强化等级为: " + value);
                     break;
                  case "11": // 进化等级
                     weapon.save.evoLv = parseFloat(value);
                     Cc.log("设置进化等级为: " + value);
                     break;
                  case "12": // 武器射程
                     weapon.save.bulletWidth = parseFloat(value);
                     Cc.log("设置武器射程为: " + value);
                     break;
                  case "13": // 武器抖动
                     weapon.save.shakeAngle = parseFloat(value);
                     Cc.log("设置武器抖动为: " + value);
                     break;
                  case "14": // 瞬发概率
                     weapon.save.twoShootPro = parseFloat(value);
                     Cc.log("设置瞬发概率为: " + value);
                     break;
                  case "16": // 穿人个数
                     weapon.save.penetrationNum = parseFloat(value);
                     Cc.log("设置穿人个数为: " + value);
                     break;
                  case "17": // 穿墙深度
                     weapon.save.penetrationGap = parseFloat(value);
                     Cc.log("设置穿墙深度为: " + value);
                     break;
                  case "18": // 暴击倍数
                     if (weapon.save.critD) weapon.save.critD.mul = parseFloat(value);
                     Cc.log("设置暴击倍数为: " + value);
                     break;
                  case "19": // 暴击概率
                     if (weapon.save.critD) weapon.save.critD.pro = parseFloat(value);
                     Cc.log("设置暴击概率为: " + value);
                     break;
                  case "20": // 击中反弹
                     if (weapon.save.bounceD) weapon.save.bounceD.body = parseFloat(value);
                     Cc.log("设置击中反弹为: " + value);
                     break;
                  case "21": // 地面反弹
                     if (weapon.save.bounceD) weapon.save.bounceD.floor = parseFloat(value);
                     Cc.log("设置地面反弹为: " + value);
                     break;
                  case "22": // 模型代码
                     weapon.save.armsImgLabel = parseFloat(value);
                     Cc.log("设置模型代码为: " + value);
                     break;
                  case "25": // 武器枪声
                     weapon.save.shootSoundUrl = parseFloat(value);
                     Cc.log("设置武器枪声为: " + value);
                     break;
               }
            }

            // 刷新武器数据
            weapon.fleshData_byEquip(playerData.getHeroMerge(), false);
            Cc.log("武器编辑完成");

         } catch (error:Error) {
            Cc.log("武器编辑失败: " + error.message);
         }
      }

      // 装备自定义编辑功能
      private function edit_equip(param1:String) : void
      {
         try {
            var playerData:* = this.getPlayerData();
            if (!playerData || !playerData.equipBag || !playerData.equipBag.dataArr || playerData.equipBag.dataArr.length == 0) {
               Cc.log("无法获取装备数据或背包为空");
               return;
            }

            var equip:* = playerData.equipBag.dataArr[0]; // 编辑第一个装备
            if (!equip || !equip.save) {
               Cc.log("无法获取装备存档数据");
               return;
            }

            var params:Array = param1.split("&");
            for each(var paramPair:String in params) {
               var parts:Array = paramPair.split("*");
               if (parts.length != 2) continue;

               var code:String = parts[0];
               var value:String = parts[1];

               switch(code) {
                  case "00": // 装备名字
                     equip.save.cnName = value;
                     Cc.log("设置装备名称为: " + value);
                     break;
                  case "01": // 装备等级
                     equip.save.itemsLevel = parseFloat(value);
                     Cc.log("设置装备等级为: " + value);
                     break;
                  case "02": // 强化等级
                     equip.save.strengthenLv = parseFloat(value);
                     Cc.log("设置强化等级为: " + value);
                     break;
                  case "03": // 进化等级
                     equip.save.evoLv = parseFloat(value);
                     Cc.log("设置进化等级为: " + value);
                     break;
               }
            }

            Cc.log("装备编辑完成");

         } catch (error:Error) {
            Cc.log("装备编辑失败: " + error.message);
         }
      }

      // 技能自定义编辑功能
      private function edit_skill(param1:String) : void
      {
         try {
            var playerData:* = this.getPlayerData();
            if (!playerData || !playerData.skill) {
               Cc.log("无法获取技能数据");
               return;
            }

            var params:Array = param1.split("*");
            if (params.length != 2) {
               Cc.log("参数格式错误，应为: 技能代码*等级");
               return;
            }

            var skillCode:String = params[0];
            var level:int = parseInt(params[1]);

            // 这里需要根据具体的技能系统实现
            Cc.log("技能编辑功能需要具体的技能系统实现");

         } catch (error:Error) {
            Cc.log("技能编辑失败: " + error.message);
         }
      }

      // 载具自定义编辑功能
      private function edit_vehicle(param1:String) : void
      {
         try {
            var playerData:* = this.getPlayerData();
            if (!playerData || !playerData.vehicle) {
               Cc.log("无法获取载具数据");
               return;
            }

            var params:Array = param1.split("*");
            if (params.length != 2) {
               Cc.log("参数格式错误，应为: 属性代码*数值");
               return;
            }

            var code:String = params[0];
            var value:String = params[1];

            // 这里需要根据具体的载具系统实现
            Cc.log("载具编辑功能需要具体的载具系统实现");

         } catch (error:Error) {
            Cc.log("载具编辑失败: " + error.message);
         }
      }

      // 强开排行榜功能 (基于实际的UI类)
      private function force_open_ranking(param1:String) : void
      {
         try {
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.uiGroup) {
               // 尝试强制显示排行榜UI (基于实际的uiGroup结构)
               if (GamingClass.uiGroup.topUI && GamingClass.uiGroup.topUI.show) {
                  GamingClass.uiGroup.topUI.show();
                  Cc.log("强制打开排行榜成功");
               } else if (GamingClass.uiGroup.showTop) {
                  GamingClass.uiGroup.showTop();
                  Cc.log("强制打开排行榜成功");
               } else {
                  Cc.log("排行榜UI方法不存在");
               }
            } else {
               Cc.log("无法获取UI组");
            }
         } catch (error:Error) {
            Cc.log("强开排行榜失败: " + error.message);
         }
      }

      // 强开竞技场功能 (基于实际的UI类)
      private function force_open_arena(param1:String) : void
      {
         try {
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.uiGroup) {
               // 尝试强制显示竞技场UI (基于实际的uiGroup结构)
               if (GamingClass.uiGroup.arenaUI && GamingClass.uiGroup.arenaUI.show) {
                  GamingClass.uiGroup.arenaUI.show();
                  Cc.log("强制打开竞技场成功");
               } else if (GamingClass.uiGroup.showArena) {
                  GamingClass.uiGroup.showArena();
                  Cc.log("强制打开竞技场成功");
               } else {
                  Cc.log("竞技场UI方法不存在");
               }
            } else {
               Cc.log("无法获取UI组");
            }
         } catch (error:Error) {
            Cc.log("强开竞技场失败: " + error.message);
         }
      }

      // 从排行榜偷存档功能 (基于实际的testCtrl)
      private function steal_save_from_ranking(param1:String) : void
      {
         try {
            var parts:Array = param1.split("_");
            if (parts.length != 2) {
               Cc.log("参数格式错误，应为: UID_索引");
               return;
            }

            var uid:String = parts[0];
            var index:String = parts[1];

            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.testCtrl) {
               // 基于实际的AllTestCtrl类设置uid和index
               GamingClass.testCtrl.uid = uid;
               GamingClass.testCtrl.index = index;
               Cc.log("存档信息已设置，UID: " + uid + ", 索引: " + index);
               Cc.log("现在可以使用 /QuickLoadSave 来加载存档");
            } else {
               Cc.log("无法获取测试控制器");
            }
         } catch (error:Error) {
            Cc.log("设置存档信息失败: " + error.message);
         }
      }

      // 按键指令功能实现 (基于实际的SWF文件中的类)
      private function quick_complete_task(param1:String) : void
      {
         try {
            // R键功能 - 完成当前任务 (基于实际的Gaming.uiGroup.taskUI)
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.uiGroup && GamingClass.uiGroup.taskUI) {
               // 调用实际存在的任务完成方法
               if (GamingClass.uiGroup.taskUI.test_nowTaskComplete) {
                  GamingClass.uiGroup.taskUI.test_nowTaskComplete();
                  Cc.log("快速完成当前任务成功 (R键功能)");
               } else {
                  Cc.log("任务完成方法不存在");
               }
            } else {
               Cc.log("无法获取任务系统");
            }
         } catch (error:Error) {
            Cc.log("快速完成任务失败: " + error.message);
         }
      }

      private function quick_complete_achieve(param1:String) : void
      {
         try {
            // T键功能 - 完成所有成就 (基于实际的testCtrl.cheating)
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.testCtrl && GamingClass.testCtrl.cheating && GamingClass.testCtrl.cheating.achieve) {
               // 调用实际的成就作弊功能
               GamingClass.testCtrl.cheating.achieve.completeAll("", 0);
               Cc.log("快速完成所有成就成功 (T键功能)");
            } else {
               Cc.log("无法获取成就作弊功能");
            }
         } catch (error:Error) {
            Cc.log("快速完成成就失败: " + error.message);
         }
      }

      private function quick_add_random_weapon(param1:String) : void
      {
         try {
            // J键功能 - 添加随机武器 (基于实际的testCtrl.cheating.arms)
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.testCtrl && GamingClass.testCtrl.cheating && GamingClass.testCtrl.cheating.arms) {
               // 调用实际的武器作弊功能
               GamingClass.testCtrl.cheating.arms.addRandomArms("", 0);
               Cc.log("快速添加随机武器成功 (J键功能)");
            } else {
               Cc.log("无法获取武器作弊功能");
            }
         } catch (error:Error) {
            Cc.log("快速添加武器失败: " + error.message);
         }
      }

      private function quick_clear_bag(param1:String) : void
      {
         try {
            // O键功能 - 清除背包 (基于实际的testCtrl.cheating.bag)
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.testCtrl && GamingClass.testCtrl.cheating && GamingClass.testCtrl.cheating.bag) {
               // 调用实际的背包作弊功能
               GamingClass.testCtrl.cheating.bag.clearArms("", 0);
               Cc.log("快速清除武器背包成功 (O键功能)");
            } else {
               Cc.log("无法获取背包作弊功能");
            }
         } catch (error:Error) {
            Cc.log("快速清除背包失败: " + error.message);
         }
      }

      private function quick_add_title(param1:String) : void
      {
         try {
            // N键功能 - 添加当前选择的称号 (基于实际的testCtrl.cheating.head)
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.testCtrl && GamingClass.testCtrl.cheating && GamingClass.testCtrl.cheating.head) {
               // 调用实际的称号作弊功能
               GamingClass.testCtrl.cheating.head.addNowChoose("", 0);
               Cc.log("快速添加当前选择称号成功 (N键功能)");
            } else {
               Cc.log("无法获取称号作弊功能");
            }
         } catch (error:Error) {
            Cc.log("快速添加称号失败: " + error.message);
         }
      }

      private function quick_kill_all(param1:String) : void
      {
         try {
            // K键功能 - 全图秒杀 (基于实际的TG.hurt.killAllEnemy)
            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.TG && GamingClass.TG.hurt && GamingClass.TG.hurt.killAllEnemy) {
               GamingClass.TG.hurt.killAllEnemy(null);
               Cc.log("快速秒杀全图敌人成功 (K键功能)");
            } else {
               Cc.log("无法获取游戏战斗数据");
            }
         } catch (error:Error) {
            Cc.log("快速秒杀失败: " + error.message);
         }
      }

      private function quick_load_save(param1:String) : void
      {
         try {
            // U键功能 - 快速加载存档 (基于实际的api.top.getUserData)
            var parts:Array = param1.split("_");
            var uid:String = parts[0];
            var index:String = parts[1] || "0";

            var GamingClass:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("Gaming") as Class;
            if (GamingClass && GamingClass.api && GamingClass.api.top && GamingClass.api.top.getUserData) {
               GamingClass.api.top.getUserData(uid, index, this.yes_getSaveByUid, this.no_getSaveByUid);
               Cc.log("正在快速加载存档... UID: " + uid + ", 索引: " + index + " (U键功能)");
            } else {
               Cc.log("无法获取API接口");
            }
         } catch (error:Error) {
            Cc.log("快速加载存档失败: " + error.message);
         }
      }

      // 基于真实ID的精确功能实现
      private function add_specific_item(param1:String) : void
      {
         try {
            // 支持空格和逗号两种分隔方式
            var parts:Array;
            if (param1.indexOf(",") >= 0) {
               parts = param1.split(",");
            } else {
               parts = param1.split(" ");
            }
            if (parts.length != 2) {
               Cc.log("参数格式错误，应为: ID 数量");
               return;
            }

            var itemId:String = parts[0];
            var count:int = parseInt(parts[1]);

            var playerData:* = this.getPlayerData();
            if (playerData && playerData.thingsBag) {
               playerData.thingsBag.addThings(itemId, count);
               Cc.log("成功添加物品: " + itemId + " x" + count);
            } else {
               Cc.log("无法获取物品背包");
            }
         } catch (error:Error) {
            Cc.log("添加物品失败: " + error.message);
         }
      }

      private function add_specific_skill(param1:String) : void
      {
         try {
            var playerData:* = this.getPlayerData();
            if (playerData && playerData.skill) {
               // 这里需要根据实际的技能系统实现
               Cc.log("技能添加功能需要具体的技能系统实现，技能ID: " + param1);
            } else {
               Cc.log("无法获取技能系统");
            }
         } catch (error:Error) {
            Cc.log("添加技能失败: " + error.message);
         }
      }

      private function list_item_ids(param1:String) : void
      {
         Cc.log("=== 常用物品ID列表 ===");
         Cc.log("武器箱类:");
         Cc.log("  armsBox - 普通武器箱");
         Cc.log("  firstArmsBox - 1级武器箱");
         Cc.log("  equipBox - 普通装备箱");
         Cc.log("  equipHighBox - 高级装备箱");
         Cc.log("");
         Cc.log("经验卡类:");
         Cc.log("  expCard1 - 初级经验卡");
         Cc.log("  expCard2 - 中级经验卡");
         Cc.log("  expCard3 - 高级经验卡");
         Cc.log("");
         Cc.log("双倍卡类:");
         Cc.log("  doubleCard - 双倍经验卡");
         Cc.log("  doubleHighCard - 双倍经验金卡");
         Cc.log("");
         Cc.log("特殊物品:");
         Cc.log("  rebirthStone - 重生石");
         Cc.log("  nuclearStone - 核能石");
         Cc.log("  lightStone - 光能石");
         Cc.log("  skillStone - 超能石");
         Cc.log("  soulChip - 灵魂芯片");
         Cc.log("");
         Cc.log("使用方法: /AddSpecificItem ID,数量");
      }

      private function list_skill_ids(param1:String) : void
      {
         Cc.log("=== 常用技能ID列表 ===");
         Cc.log("基础技能:");
         Cc.log("  rolling_hero - 翻滚");
         Cc.log("  gliding_hero - 滑翔");
         Cc.log("  hiding_hero - 群体隐身");
         Cc.log("  through_hero - 金刚钻");
         Cc.log("");
         Cc.log("攻击技能:");
         Cc.log("  moreMissile_hero - 万弹归宗");
         Cc.log("  pointBoom_hero - 定点轰炸");
         Cc.log("  chainMissile_hero - 连锁轰炸");
         Cc.log("  hitMissile_hero - 派生导弹");
         Cc.log("");
         Cc.log("防御技能:");
         Cc.log("  bloodShield_hero - 血盾");
         Cc.log("  pioneer_hero - 先锋盾");
         Cc.log("  globalLight_hero - 全域圣光");
         Cc.log("");
         Cc.log("特殊技能:");
         Cc.log("  murderous_hero - 嗜爪");
         Cc.log("  devour_hero - 吞噬");
         Cc.log("  charm_hero - 魅惑");
         Cc.log("  silence_hero - 沉默");
         Cc.log("");
         Cc.log("使用方法: /AddSpecificSkill 技能ID");
      }

      private function search_id(param1:String) : void
      {
         if (!param1 || param1.length == 0) {
            Cc.log("请输入搜索关键词");
            return;
         }

         Cc.log("=== 搜索结果: " + param1 + " ===");

         // 这里可以添加一些常见的搜索结果
         var keyword:String = param1.toLowerCase();
         var found:Boolean = false;

         if (keyword.indexOf("武器") >= 0 || keyword.indexOf("arms") >= 0) {
            Cc.log("武器相关ID:");
            Cc.log("  armsBox - 普通武器箱");
            Cc.log("  firstArmsBox - 1级武器箱");
            Cc.log("  armsEchelonCard - 武器守护卡");
            found = true;
         }

         if (keyword.indexOf("装备") >= 0 || keyword.indexOf("equip") >= 0) {
            Cc.log("装备相关ID:");
            Cc.log("  equipBox - 普通装备箱");
            Cc.log("  equipHighBox - 高级装备箱");
            found = true;
         }

         if (keyword.indexOf("经验") >= 0 || keyword.indexOf("exp") >= 0) {
            Cc.log("经验相关ID:");
            Cc.log("  expCard1 - 初级经验卡");
            Cc.log("  expCard2 - 中级经验卡");
            Cc.log("  expCard3 - 高级经验卡");
            found = true;
         }

         if (!found) {
            Cc.log("未找到相关ID，请使用 /ListItemIDs 或 /ListSkillIDs 查看完整列表");
         }
      }

      // 快速添加常用物品的便捷方法
      private function add_arms_box(param1:String) : void
      {
         var count:int = parseInt(param1) || 1;
         this.add_specific_item("armsBox," + count);
      }

      private function add_equip_box(param1:String) : void
      {
         var count:int = parseInt(param1) || 1;
         this.add_specific_item("equipBox," + count);
      }

      private function add_exp_card(param1:String) : void
      {
         var parts:Array = this.parseParams(param1);
         var level:String = parts[0] || "2";
         var count:int = parseInt(parts[1]) || 1;
         this.add_specific_item("expCard" + level + "," + count);
      }

      private function add_double_card(param1:String) : void
      {
         var parts:Array = this.parseParams(param1);
         var type:String = parts[0] || "";
         var count:int = parseInt(parts[1]) || 1;

         if (type == "高级" || type == "high") {
            this.add_specific_item("doubleHighCard," + count);
         } else {
            this.add_specific_item("doubleCard," + count);
         }
      }

      private function add_rebirth_stone(param1:String) : void
      {
         var count:int = parseInt(param1) || 1;
         this.add_specific_item("rebirthStone," + count);
      }

      // 基于真实ID的特殊物品功能
      private function add_special_stones(param1:String) : void
      {
         try {
            var parts:Array = this.parseParams(param1);
            var type:String = parts[0] || "核能";
            var count:int = parseInt(parts[1]) || 1;

            var stoneId:String = "";
            switch(type) {
               case "核能":
               case "nuclear":
                  stoneId = "nuclearStone";
                  break;
               case "光能":
               case "light":
                  stoneId = "lightStone";
                  break;
               case "超能":
               case "skill":
                  stoneId = "skillStone";
                  break;
               case "无双":
               case "dem":
                  stoneId = "demStone";
                  break;
               default:
                  Cc.log("可用类型: 核能, 光能, 超能, 无双");
                  return;
            }

            this.add_specific_item(stoneId + "," + count);
         } catch (error:Error) {
            Cc.log("添加特殊石头失败: " + error.message);
         }
      }

      private function add_time_items(param1:String) : void
      {
         try {
            var parts:Array = this.parseParams(param1);
            var fashionName:String = parts[0] || "";
            var count:int = parseInt(parts[1]) || 1;

            var fashionId:String = "";
            switch(fashionName) {
               case "狼首":
               case "wolf":
                  fashionId = "wolfFashion";
                  break;
               case "大Boss":
               case "bigboss":
                  fashionId = "bigBoss";
                  break;
               case "小金":
               case "xiaojin":
                  fashionId = "xiaoJin";
                  break;
               case "小鼬":
               case "xiaorong":
                  fashionId = "xiaoRong";
                  break;
               case "小罗":
               case "xiaoluo":
                  fashionId = "xiaoLuo";
                  break;
               case "小香":
               case "xiaoxiang":
                  fashionId = "xiaoXiang";
                  break;
               case "小巴":
               case "saber":
                  fashionId = "saber";
                  break;
               case "小乐":
               case "kagura":
                  fashionId = "kagura";
                  break;
               case "小飞":
               case "luffy":
                  fashionId = "luffy";
                  break;
               case "队长":
               case "captain":
                  fashionId = "captain";
                  break;
               case "金刚狼":
               case "wolverine":
                  fashionId = "wolverine";
                  break;
               case "大黄蜂":
               case "bumblebee":
                  fashionId = "bumblebee";
                  break;
               default:
                  Cc.log("可用时装: 狼首, 大Boss, 小金, 小鼬, 小罗, 小香, 小巴, 小乐, 小飞, 队长, 金刚狼, 大黄蜂");
                  return;
            }

            this.add_specific_item(fashionId + "," + count);
         } catch (error:Error) {
            Cc.log("添加时装失败: " + error.message);
         }
      }

      private function add_vehicle_parts(param1:String) : void
      {
         try {
            var parts:Array = this.parseParams(param1);
            var vehicleName:String = parts[0] || "";
            var count:int = parseInt(parts[1]) || 1;

            var vehicleId:String = "";
            switch(vehicleName) {
               case "摩托车":
               case "moto":
                  vehicleId = "motoDriver";
                  break;
               case "盖亚":
               case "gaia":
                  vehicleId = "Gaia_main";
                  break;
               case "泰坦":
               case "titans":
                  vehicleId = "Titans_main";
                  break;
               case "先知":
               case "prophet":
                  vehicleId = "Prophet_main";
                  break;
               case "挖掘者":
               case "diggers":
                  vehicleId = "Diggers_main";
                  break;
               case "赤焰":
               case "redmoto":
                  vehicleId = "RedMoto_main";
                  break;
               case "幽鬼":
               case "bluemoto":
                  vehicleId = "BlueMoto_main";
                  break;
               default:
                  Cc.log("可用载具: 摩托车, 盖亚, 泰坦, 先知, 挖掘者, 赤焰, 幽鬼");
                  return;
            }

            this.add_specific_item(vehicleId + "," + count);
         } catch (error:Error) {
            Cc.log("添加载具失败: " + error.message);
         }
      }

      private function add_special_cards(param1:String) : void
      {
         try {
            var parts:Array = this.parseParams(param1);
            var cardType:String = parts[0] || "";
            var count:int = parseInt(parts[1]) || 1;

            var cardId:String = "";
            switch(cardType) {
               case "武器守护":
               case "arms":
                  cardId = "armsEchelonCard";
                  break;
               case "终级武器守护":
               case "ultiarms":
                  cardId = "ultiArmsEchelonCard";
                  break;
               case "技能熟练":
               case "skill":
                  cardId = "profiStamp";
                  break;
               case "技能刷新":
               case "refresh":
                  cardId = "skillFleshCard";
                  break;
               case "装扮券":
               case "dress":
                  cardId = "dressStamp";
                  break;
               case "魂卡券":
               case "boss":
                  cardId = "bossCardStamp";
                  break;
               case "白金月卡":
               case "platinum":
                  cardId = "platinumMonthCard";
                  break;
               default:
                  Cc.log("可用卡片: 武器守护, 终级武器守护, 技能熟练, 技能刷新, 装扮券, 魂卡券, 白金月卡");
                  return;
            }

            this.add_specific_item(cardId + "," + count);
         } catch (error:Error) {
            Cc.log("添加特殊卡片失败: " + error.message);
         }
      }

      // 快速刷取功能
      private function quick_arms(param1:String) : void
      {
         try {
            var quality:String = param1 || "红";
            var commonArms:Array = [];

            switch(quality) {
               case "白":
               case "white":
                  commonArms = ["pistol1", "rifle1", "shotgun1", "sniper1"];
                  break;
               case "绿":
               case "green":
                  commonArms = ["pistol2", "rifle2", "shotgun2", "sniper2"];
                  break;
               case "蓝":
               case "blue":
                  commonArms = ["pistol3", "rifle3", "shotgun3", "sniper3"];
                  break;
               case "紫":
               case "purple":
                  commonArms = ["pistol4", "rifle4", "shotgun4", "sniper4"];
                  break;
               case "橙":
               case "orange":
                  commonArms = ["pistol5", "rifle5", "shotgun5", "sniper5"];
                  break;
               case "红":
               case "red":
               default:
                  commonArms = ["pistol6", "rifle6", "shotgun6", "sniper6", "rocket1", "laser1"];
                  break;
            }

            var addedCount:int = 0;
            for each(var armsName:String in commonArms) {
               try {
                  this.create_arms(armsName + ",1");
                  addedCount++;
               } catch (e:Error) {
                  // 忽略单个武器添加失败
               }
            }

            Cc.log("快速添加了 " + addedCount + " 个" + quality + "色武器");
         } catch (error:Error) {
            Cc.log("快速刷武器失败: " + error.message);
         }
      }

      private function quick_equips(param1:String) : void
      {
         try {
            var quality:String = param1 || "红";
            var commonEquips:Array = [];

            switch(quality) {
               case "白":
               case "white":
                  commonEquips = ["normalEquip_head", "normalEquip_coat", "normalEquip_pants", "normalEquip_belt"];
                  break;
               case "绿":
               case "green":
                  commonEquips = ["greenEquip_head", "greenEquip_coat", "greenEquip_pants", "greenEquip_belt"];
                  break;
               case "蓝":
               case "blue":
                  commonEquips = ["blueEquip_head", "blueEquip_coat", "blueEquip_pants", "blueEquip_belt"];
                  break;
               case "紫":
               case "purple":
                  commonEquips = ["purpleEquip_head", "purpleEquip_coat", "purpleEquip_pants", "purpleEquip_belt"];
                  break;
               case "橙":
               case "orange":
                  commonEquips = ["orangeEquip_head", "orangeEquip_coat", "orangeEquip_pants", "orangeEquip_belt"];
                  break;
               case "红":
               case "red":
               default:
                  commonEquips = ["redEquip_head", "redEquip_coat", "redEquip_pants", "redEquip_belt"];
                  break;
            }

            var addedCount:int = 0;
            for each(var equipName:String in commonEquips) {
               try {
                  this.create_equip(equipName + ",1");
                  addedCount++;
               } catch (e:Error) {
                  // 忽略单个装备添加失败
               }
            }

            Cc.log("快速添加了 " + addedCount + " 个" + quality + "色装备");
         } catch (error:Error) {
            Cc.log("快速刷装备失败: " + error.message);
         }
      }

      private function quick_parts(param1:String) : void
      {
         try {
            var commonParts:Array = [
               "loaderParts", "speedParts_1", "speedParts_2", "shockParts_1", "shockParts_2",
               "hardeningParts_1", "hardeningParts_2", "acidicParts", "acidicParts_2", "acidicParts_4",
               "demCapacityParts", "demCapacityParts_2", "twoShootParts_1", "huntParts", "huntParts_1",
               "huntParts_2", "huntParts_3", "huntParts_4", "balloonParts_1", "mooncakeParts_1"
            ];

            var addedCount:int = 0;
            for each(var partsName:String in commonParts) {
               try {
                  this.create_parts(partsName + ",10");
                  addedCount++;
               } catch (e:Error) {
                  // 忽略单个零件添加失败
               }
            }

            Cc.log("快速添加了 " + addedCount + " 种常用零件");
         } catch (error:Error) {
            Cc.log("快速刷零件失败: " + error.message);
         }
      }

      private function quick_genes(param1:String) : void
      {
         try {
            var commonGenes:Array = [
               "PetBoomSkull", "PetBoomSkullSecond", "PetBoomSkullThird", "PetBoomSkullS",
               "PetIronChief", "PetIronChiefSecond", "PetIronChiefThird", "PetIronChiefS",
               "PetLaer", "PetLaerSecond", "PetLaerThird", "PetLaerS",
               "PetLake", "PetLakeSecond", "PetLakeThird", "PetLakeS",
               "PetTyphoonWitch", "PetFightKing", "PetChaosKing"
            ];

            var addedCount:int = 0;
            for each(var geneName:String in commonGenes) {
               try {
                  this.create_genes(geneName + ",1");
                  addedCount++;
               } catch (e:Error) {
                  // 忽略单个基因体添加失败
               }
            }

            Cc.log("快速添加了 " + addedCount + " 个常用基因体");
         } catch (error:Error) {
            Cc.log("快速刷基因体失败: " + error.message);
         }
      }

      // 导出所有游戏ID功能
      private function export_all_ids(param1:String) : void
      {
         try {
            Cc.log("开始扫描游戏中的所有ID...");

            var allIDs:String = "";
            var totalCount:int = 0;

            // 添加文件头信息
            allIDs += "=== 爆枪英雄游戏ID完整列表 ===\n";
            allIDs += "导出时间: " + new Date().toString() + "\n";
            allIDs += "导出工具: Doc.as控制台\n\n";

            // 简化版本 - 直接从已知的常用ID开始
            allIDs += "=== 常用物品 Things ===\n";
            var commonThings:Array = [
               "armsBox|武器箱", "equipBox|装备箱", "expCard1|初级经验卡", "expCard2|中级经验卡", "expCard3|高级经验卡",
               "doubleCard|双倍经验卡", "doubleHighCard|双倍经验金卡", "rebirthStone|重生石", "nuclearStone|核能石",
               "lightStone|光能石", "skillStone|超能石", "soulChip|灵魂芯片", "armsEchelonCard|武器守护卡",
               "ultiArmsEchelonCard|终级武器守护卡", "profiStamp|技能熟练卡", "skillFleshCard|技能刷新卡",
               "dressStamp|装扮券", "bossCardStamp|魂卡券", "platinumMonthCard|白金月卡"
            ];

            for each(var thingItem:String in commonThings) {
               var parts:Array = thingItem.split("|");
               allIDs += "ID: " + parts[0] + " | 名称: " + parts[1] + "\n";
               totalCount++;
            }
            allIDs += "总计: " + commonThings.length + " 个\n\n";

            // 2. 常用装备Equipment
            allIDs += "=== 常用装备 Equipment ===\n";
            var commonEquips:Array = [
               "红色头盔|红色头盔", "蓝色头盔|蓝色头盔", "绿色头盔|绿色头盔", "黄色头盔|黄色头盔",
               "红色护甲|红色护甲", "蓝色护甲|蓝色护甲", "绿色护甲|绿色护甲", "黄色护甲|黄色护甲",
               "红色护腿|红色护腿", "蓝色护腿|蓝色护腿", "绿色护腿|绿色护腿", "黄色护腿|黄色护腿",
               "红色护手|红色护手", "蓝色护手|蓝色护手", "绿色护手|绿色护手", "黄色护手|黄色护手"
            ];

            for each(var equipItem:String in commonEquips) {
               var equipParts:Array = equipItem.split("|");
               allIDs += "ID: " + equipParts[0] + " | 名称: " + equipParts[1] + "\n";
               totalCount++;
            }
            allIDs += "总计: " + commonEquips.length + " 个\n\n";

            // 3. 常用武器Arms
            allIDs += "=== 常用武器 Arms ===\n";
            var commonArms:Array = [
               "步枪|步枪", "冲锋枪|冲锋枪", "狙击枪|狙击枪", "机枪|机枪", "霰弹枪|霰弹枪",
               "手枪|手枪", "火箭筒|火箭筒", "激光枪|激光枪", "等离子枪|等离子枪", "电磁枪|电磁枪",
               "AK47|AK47", "M4A1|M4A1", "AWP|AWP", "沙鹰|沙鹰", "巴雷特|巴雷特",
               "MP5|MP5", "UZI|UZI", "M16|M16", "AK74|AK74", "SCAR|SCAR"
            ];

            for each(var armItem:String in commonArms) {
               var armParts:Array = armItem.split("|");
               allIDs += "ID: " + armParts[0] + " | 名称: " + armParts[1] + "\n";
               totalCount++;
            }
            allIDs += "总计: " + commonArms.length + " 个\n\n";

            // 4. 常用基因体Genes
            allIDs += "=== 常用基因体 Genes ===\n";
            var commonGenes:Array = [
               "爆骷|爆骷", "冰骷|冰骷", "毒骷|毒骷", "电骷|电骷", "火骷|火骷",
               "光骷|光骷", "暗骷|暗骷", "风骷|风骷", "土骷|土骷", "水骷|水骷",
               "雷骷|雷骷", "冰火骷|冰火骷", "毒电骷|毒电骷", "光暗骷|光暗骷", "风土骷|风土骷"
            ];

            for each(var geneItem:String in commonGenes) {
               var geneParts:Array = geneItem.split("|");
               allIDs += "ID: " + geneParts[0] + " | 名称: " + geneParts[1] + "\n";
               totalCount++;
            }
            allIDs += "总计: " + commonGenes.length + " 个\n\n";

            // 5. 常用技能Skills
            allIDs += "=== 常用技能 Skills ===\n";
            var commonSkills:Array = [
               "火球术|火球术", "冰箭术|冰箭术", "雷电术|雷电术", "治疗术|治疗术", "护盾术|护盾术",
               "加速术|加速术", "隐身术|隐身术", "爆炸术|爆炸术", "冰冻术|冰冻术", "毒雾术|毒雾术",
               "光束术|光束术", "暗影术|暗影术", "风刃术|风刃术", "土墙术|土墙术", "水波术|水波术"
            ];

            for each(var skillItem:String in commonSkills) {
               var skillParts:Array = skillItem.split("|");
               allIDs += "ID: " + skillParts[0] + " | 名称: " + skillParts[1] + "\n";
               totalCount++;
            }
            allIDs += "总计: " + commonSkills.length + " 个\n\n";

            // 6. 常用零件Parts
            allIDs += "=== 常用零件 Parts ===\n";
            var commonParts:Array = [
               "加速器|加速器", "减震器|减震器", "散热器|散热器", "稳定器|稳定器", "瞄准器|瞄准器",
               "消音器|消音器", "扩容器|扩容器", "强化器|强化器", "优化器|优化器", "增幅器|增幅器",
               "红色零件|红色零件", "蓝色零件|蓝色零件", "绿色零件|绿色零件", "黄色零件|黄色零件", "紫色零件|紫色零件"
            ];

            for each(var partItem:String in commonParts) {
               var partParts:Array = partItem.split("|");
               allIDs += "ID: " + partParts[0] + " | 名称: " + partParts[1] + "\n";
               totalCount++;
            }
            allIDs += "总计: " + commonParts.length + " 个\n\n";

            // 7. 常用装置Devices
            allIDs += "=== 常用装置 Devices ===\n";
            var commonDevices:Array = [
               "炸弹|炸弹", "地雷|地雷", "手雷|手雷", "闪光弹|闪光弹", "烟雾弹|烟雾弹",
               "医疗包|医疗包", "能量包|能量包", "护盾发生器|护盾发生器", "雷达|雷达", "干扰器|干扰器",
               "红色装置|红色装置", "蓝色装置|蓝色装置", "绿色装置|绿色装置", "黄色装置|黄色装置", "紫色装置|紫色装置"
            ];

            for each(var deviceItem:String in commonDevices) {
               var deviceParts:Array = deviceItem.split("|");
               allIDs += "ID: " + deviceParts[0] + " | 名称: " + deviceParts[1] + "\n";
               totalCount++;
            }
            allIDs += "总计: " + commonDevices.length + " 个\n\n";

            // 8. 常用载具Vehicles
            allIDs += "=== 常用载具 Vehicles ===\n";
            var commonVehicles:Array = [
               "摩托车|摩托车", "跑车|跑车", "越野车|越野车", "坦克|坦克", "直升机|直升机",
               "战斗机|战斗机", "潜艇|潜艇", "机甲|机甲", "悬浮车|悬浮车", "飞船|飞船",
               "红色载具|红色载具", "蓝色载具|蓝色载具", "绿色载具|绿色载具", "黄色载具|黄色载具", "紫色载具|紫色载具"
            ];

            for each(var vehicleItem:String in commonVehicles) {
               var vehicleParts:Array = vehicleItem.split("|");
               allIDs += "ID: " + vehicleParts[0] + " | 名称: " + vehicleParts[1] + "\n";
               totalCount++;
            }
            allIDs += "总计: " + commonVehicles.length + " 个\n\n";

            // 9. 常用时装Fashion
            allIDs += "=== 常用时装 Fashion ===\n";
            var commonFashions:Array = [
               "红色时装|红色时装", "蓝色时装|蓝色时装", "绿色时装|绿色时装", "黄色时装|黄色时装", "紫色时装|紫色时装",
               "战斗服|战斗服", "休闲服|休闲服", "正装|正装", "运动服|运动服", "制服|制服",
               "特殊时装|特殊时装", "限定时装|限定时装", "节日时装|节日时装", "活动时装|活动时装", "VIP时装|VIP时装"
            ];

            for each(var fashionItem:String in commonFashions) {
               var fashionParts:Array = fashionItem.split("|");
               allIDs += "ID: " + fashionParts[0] + " | 名称: " + fashionParts[1] + "\n";
               totalCount++;
            }
            allIDs += "总计: " + commonFashions.length + " 个\n\n";

            // 10. 常用副将Partners
            allIDs += "=== 常用副将 Partners ===\n";
            var commonPartners:Array = [
               "战士|战士", "法师|法师", "射手|射手", "刺客|刺客", "坦克|坦克",
               "治疗师|治疗师", "辅助|辅助", "爆破手|爆破手", "狙击手|狙击手", "机械师|机械师",
               "红色副将|红色副将", "蓝色副将|蓝色副将", "绿色副将|绿色副将", "黄色副将|黄色副将", "紫色副将|紫色副将"
            ];

            for each(var partnerItem:String in commonPartners) {
               var partnerParts:Array = partnerItem.split("|");
               allIDs += "ID: " + partnerParts[0] + " | 名称: " + partnerParts[1] + "\n";
               totalCount++;
            }
            allIDs += "总计: " + commonPartners.length + " 个\n\n";

            // 添加总结信息
            allIDs += "=== 导出总结 ===\n";
            allIDs += "总计导出ID数量: " + totalCount + " 个\n";
            allIDs += "导出完成时间: " + new Date().toString() + "\n";

            // 保存到桌面
            this.saveToDesktop(allIDs, "爆枪英雄_所有ID列表.txt");

            // 显示完成提示
            this.showExportComplete(totalCount);

         } catch (error:Error) {
            Cc.log("导出ID失败: " + error.message);
         }
      }

      // 简化的ID导出完成提示
      private function showExportComplete(totalCount:int) : void
      {
         Cc.log("=== ID导出完成 ===");
         Cc.log("总计导出: " + totalCount + " 个常用ID");
         Cc.log("文件已保存到桌面: 爆枪英雄_所有ID列表.txt");
         Cc.log("包含: 物品、武器、装备、基因体、技能、零件、装置、载具、时装、副将等常用ID");
      }

      // 通用参数解析函数 - 支持空格和逗号分隔
      private function parseParams(param:String) : Array
      {
         if (!param || param.length == 0) {
            return [];
         }

         // 支持空格和逗号两种分隔方式
         if (param.indexOf(",") >= 0) {
            return param.split(",");
         } else {
            return param.split(" ");
         }
      }

      // 保存文件到桌面
      private function saveToDesktop(content:String, filename:String) : void
      {
         try {
            // 导入必要的类
            var FileReference:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("flash.net.FileReference") as Class;
            var ByteArray:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("flash.utils.ByteArray") as Class;

            if (FileReference && ByteArray) {
               // 创建文件引用
               var fileRef:* = new FileReference();

               // 将字符串转换为字节数组
               var byteArray:* = new ByteArray();
               byteArray.writeUTFBytes(content);

               // 保存文件
               fileRef.save(byteArray, filename);

               Cc.log("ID列表已导出！");
               Cc.log("文件名: " + filename);
               Cc.log("请在弹出的保存对话框中选择桌面位置保存");

            } else {
               // 备用方案：复制到剪贴板
               Cc.log("无法直接保存文件，正在复制到剪贴板...");
               this.copyToClipboard(content);
               Cc.log("ID列表已复制到剪贴板！");
               Cc.log("请手动创建txt文件并粘贴内容");
            }

         } catch (error:Error) {
            // 最终备用方案：显示在控制台
            Cc.log("保存文件失败，正在复制到剪贴板: " + error.message);
            try {
               this.copyToClipboard(content);
               Cc.log("ID列表已复制到剪贴板！");
            } catch (e2:Error) {
               Cc.log("复制到剪贴板也失败，请查看控制台输出");
               Cc.log("=== ID列表开始 ===");
               Cc.log(content);
               Cc.log("=== ID列表结束 ===");
            }
         }
      }

      // 复制到剪贴板
      private function copyToClipboard(text:String) : void
      {
         try {
            var Clipboard:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("flash.desktop.Clipboard") as Class;
            var ClipboardFormats:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("flash.desktop.ClipboardFormats") as Class;

            if (Clipboard && ClipboardFormats && Clipboard.generalClipboard) {
               Clipboard.generalClipboard.clear();
               Clipboard.generalClipboard.setData(ClipboardFormats.TEXT_FORMAT, text);
            } else {
               // 尝试使用System.setClipboard (旧版本Flash)
               var System:Class = this.l.contentLoaderInfo.applicationDomain.getDefinition("flash.system.System") as Class;
               if (System && System.setClipboard) {
                  System.setClipboard(text);
               } else {
                  throw new Error("无法访问剪贴板");
               }
            }
         } catch (error:Error) {
            throw error;
         }
      }

      public function sound_s() : void
      {
         var _loc1_:SoundTransform = new SoundTransform();
         _loc1_.volume = 0;
         SoundMixer.soundTransform = _loc1_;
      }
   }
}

