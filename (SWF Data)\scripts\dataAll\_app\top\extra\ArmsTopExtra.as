package dataAll._app.top.extra
{
   import com.sounto.utils.ClassProperty;
   
   public class ArmsTopExtra extends TopExtra
   {
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var player:String = "";
      
      public var lv:int = 0;
      
      public var color:String = "";
      
      public var sGap:String = "";
      
      public var hurt:Number = 0;
      
      public var ca:Number = 0;
      
      public var dps:Number = 0;
      
      public var img:String = "";
      
      public function ArmsTopExtra()
      {
         super();
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      override public function setToSimulated() : void
      {
         this.name = "火神";
         this.player = "迈居";
         this.color = "紫金";
         this.lv = int(Math.random() * 50);
         this.sGap = "1.3每秒";
         this.hurt = int(Math.random() * 500);
         this.dps = this.hurt * 2;
         this.img = "";
      }
   }
}

