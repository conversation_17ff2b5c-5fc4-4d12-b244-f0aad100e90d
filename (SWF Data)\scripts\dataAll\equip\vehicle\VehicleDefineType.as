package dataAll.equip.vehicle
{
   public class VehicleDefineType
   {
      public static const NORMAL:String = "normal";
      
      public static const SHOOT:String = "shoot";
      
      public static const mustYaArr:Array = ["BlueMoto","Titans"];
      
      public function VehicleDefineType()
      {
         super();
      }
      
      public static function getClass(type0:String) : Class
      {
         if(type0 == SHOOT)
         {
            return ShootVehicleDefine;
         }
         return VehicleDefine;
      }
      
      public static function getDpsNameArr(type0:String) : Array
      {
         return VehicleAttackLabel[type0 + "Arr"];
      }
      
      public static function mustYaB(baseLabel0:String) : Boolean
      {
         return mustYaArr.indexOf(baseLabel0) >= 0;
      }
      
      public static function getZodiac(baseLabel0:String) : String
      {
         if(baseLabel0 == "DesertTank")
         {
            return "yearMonkey";
         }
         if(baseLabel0 == "Daybreak")
         {
            return "yearPig";
         }
         if(baseLabel0 == "RedReaper")
         {
            return "yearRabbit";
         }
         if(baseLabel0 == "Prophet")
         {
            return "yearMouse";
         }
         if(baseLabel0 == "Diggers")
         {
            return "yearHourse";
         }
         if(baseLabel0 == "Titans")
         {
            return "yearCattle";
         }
         if(baseLabel0 == "SeaShark")
         {
            return "yearDog";
         }
         if(baseLabel0 == "BlueWhale")
         {
            return "yearSnake";
         }
         if(baseLabel0 == "RedMoto")
         {
            return "yearCattle";
         }
         if(baseLabel0 == "BlueMoto")
         {
            return "yearPig";
         }
         if(baseLabel0 == "Thunder")
         {
            return "yearRabbit";
         }
         return "zodiacCash";
      }
   }
}

