package dataAll._app.edit.tor
{
   import com.sounto.pool.OnePool;
   import dataAll._base.NormalDefine;
   
   public class TorEditDefine extends NormalDefine implements IO_TorEditDefine
   {
      private static const pool:OnePool = new OnePool(1000);
      
      public var index:int = 0;
      
      private var parent:IO_TorEditDefine;
      
      public function TorEditDefine()
      {
         super();
      }
      
      public static function getNewClass() : TorEditDefine
      {
         var b0:TorEditDefine = pool.getObject() as TorEditDefine;
         if(!b0)
         {
            return new TorEditDefine();
         }
         return b0;
      }
      
      public static function recoverPool(b0:TorEditDefine) : void
      {
         b0.clearData();
         pool.addObject(b0);
      }
      
      public function clearData() : void
      {
         name = "";
         cnName = "";
         father = "";
         this.parent = null;
      }
      
      public function getParent() : IO_TorEditDefine
      {
         return this.parent;
      }
      
      public function setParent(p0:IO_TorEditDefine) : void
      {
         this.parent = p0;
      }
      
      public function getTorTip() : String
      {
         return "";
      }
   }
}

