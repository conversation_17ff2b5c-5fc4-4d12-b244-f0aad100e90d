package dataAll._app.parts
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.parts.define.PartsType;
   import dataAll._app.task.define.TaskType;
   import dataAll.arms.ArmsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.save.ItemsSaveGroup;
   import dataAll.things.ThingsData;
   import dataAll.things.ThingsDataGroup;
   import dataAll.things.define.ThingsDefine;
   import dataAll.things.save.ThingsSave;
   import dataAll.things.save.ThingsSaveGroup;
   import dataAll.ui.tip.CheckData;
   
   public class PartsDataGroup extends ThingsDataGroup
   {
      public var nowArmsData:ArmsData = null;
      
      private var temp_partAddData:PartsAddData = new PartsAddData();
      
      public function PartsDataGroup()
      {
         super();
         dataType = ItemsDataGroup.TYPE_PARTS;
         placeType = ItemsDataGroup.PLACE_BAG;
      }
      
      public static function partsSwapTo(dg1:PartsDataGroup, dg2:PartsDataGroup, site1:int, site2:int, wearCompareLevel:int = 9999) : CheckData
      {
         var mustThingsType0:String = null;
         var check0:CheckData = new CheckData();
         var da1:ThingsData = dg1.getDataBySite(site1) as ThingsData;
         var da2:ThingsData = dg2.getDataBySite(site2) as ThingsData;
         var swapToB:Boolean = false;
         if(dg1.placeType == ItemsDataGroup.PLACE_BAG && dg2.placeType == ItemsDataGroup.PLACE_BAG)
         {
            swapToB = true;
         }
         else if(dg1.placeType == ItemsDataGroup.PLACE_WEAR && dg2.placeType == ItemsDataGroup.PLACE_WEAR)
         {
            if(PartsType.getWearType(site1) == PartsType.getWearType(site2))
            {
               swapToB = true;
            }
         }
         else
         {
            mustThingsType0 = "";
            if(dg1.placeType == ItemsDataGroup.PLACE_WEAR)
            {
               if(Boolean(da2))
               {
                  mustThingsType0 = PartsType.getThingsTypeBySite(site1);
                  if(mustThingsType0 == da2.save.getPartsType())
                  {
                     swapToB = true;
                  }
               }
               else
               {
                  swapToB = true;
               }
            }
            else if(Boolean(da1))
            {
               mustThingsType0 = PartsType.getThingsTypeBySite(site2);
               if(mustThingsType0 == da1.save.getPartsType())
               {
                  swapToB = true;
               }
            }
            else
            {
               swapToB = true;
            }
         }
         check0.bb = swapToB;
         if(swapToB)
         {
            return ItemsDataGroup.swapTo(dg1,dg2,site1,site2,wearCompareLevel,true);
         }
         return check0;
      }
      
      public function initBagSave() : void
      {
         var parts_arr0:Array = null;
         var n:* = undefined;
         if(dataArr.length == 0)
         {
            parts_arr0 = Gaming.defineGroup.things.normalPartsNameArr;
            for(n in parts_arr0)
            {
               addDataByName(parts_arr0[n] + "_3",3);
            }
         }
      }
      
      public function getPartsUIIconLabel() : String
      {
         var da0:ThingsData = null;
         var d0:ThingsDefine = null;
         var n0:int = 0;
         var v0:int = -1;
         for each(da0 in dataArr)
         {
            d0 = da0.save.getDefine();
            n0 = 0;
            if(d0.isPartsRareB())
            {
               n0 = 2;
            }
            else if(d0.isPartsSpecialB())
            {
               n0 = 1;
            }
            if(v0 < n0)
            {
               v0 = n0;
            }
         }
         if(v0 == 0)
         {
            return "parts";
         }
         if(v0 == 1)
         {
            return "special";
         }
         if(v0 == 2)
         {
            return "rare";
         }
         return "";
      }
      
      public function getPartsUIIconUrlArr() : Array
      {
         var da0:ThingsData = null;
         var d0:ThingsDefine = null;
         var arr0:Array = [];
         for each(da0 in dataArr)
         {
            d0 = da0.save.getDefine();
            if(d0.isPartsRareB())
            {
               arr0.push(d0.iconUrl);
            }
         }
         return arr0;
      }
      
      public function bagSpacePanByDataArr(arr0:Array) : String
      {
         var da0:ThingsData = null;
         var saveArr0:Array = [];
         for each(da0 in arr0)
         {
            saveArr0.push(da0.save);
         }
         return this.bagSpacePanBySaveArr(saveArr0);
      }
      
      public function bagSpacePanBySaveArr(arr0:Array) : String
      {
         var must0:int = ThingsSaveGroup.getMustSiteBySaveArr(arr0);
         var spaceNum0:int = getSpaceSiteNum();
         if(spaceNum0 < must0)
         {
            return "零件背包空间不足，当前还差 " + ComMethod.color(must0 - spaceNum0 + "","#00FF00") + " 个空位。";
         }
         return "";
      }
      
      public function addByDataArr(arr0:Array) : void
      {
         var da0:ThingsData = null;
         var saveArr0:Array = [];
         for each(da0 in arr0)
         {
            saveArr0.push(da0.save);
         }
         this.addBySaveArr(saveArr0);
      }
      
      public function addBySaveArr(arr0:Array) : void
      {
         var s0:ThingsSave = null;
         var d0:ThingsDefine = null;
         var da2:ThingsData = null;
         arr0 = ThingsSaveGroup.setMergeSaveArrBySame(arr0);
         var new_arr0:Array = [];
         for each(s0 in arr0)
         {
            d0 = s0.getDefine();
            if(!d0.noOverlayB)
            {
               da2 = getDataBySaveName(s0.name) as ThingsData;
               if(da2 is ThingsData)
               {
                  da2.save.overNum(s0.nowNum);
                  da2.newB = true;
               }
               else
               {
                  new_arr0.push(s0);
               }
            }
         }
         addNewSaveArr(new_arr0);
      }
      
      public function getPartsAddData() : PartsAddData
      {
         var da0:PartsAddData = null;
         if(TaskType.canPartsB() == false)
         {
            return this.temp_partAddData;
         }
         if(!this.nowArmsData)
         {
            return this.temp_partAddData;
         }
         if(dataArr.length > 0)
         {
            return PartsCreator.getPartsAddDataByDataArr(getSiteDataArray(),this.nowArmsData);
         }
         return this.temp_partAddData;
      }
      
      public function clone() : PartsDataGroup
      {
         var dg0:PartsDataGroup = new PartsDataGroup();
         dg0.setPlayerData(normalPlayerData);
         dg0.inData_bySaveGroup(saveGroup.clone());
         return dg0;
      }
      
      public function getBestByGripType(gripType0:String, armsLv0:int) : ThingsData
      {
         var maxDa0:ThingsData = null;
         var da0:ThingsData = null;
         var thingsType0:String = null;
         var lv0:int = 0;
         var maxLv0:int = 0;
         for each(da0 in dataArr)
         {
            thingsType0 = PartsType.getThingsType(gripType0);
            if(da0.save.getPartsType() == thingsType0)
            {
               lv0 = da0.save.getTrueLevel();
               if(lv0 <= armsLv0 && lv0 >= maxLv0)
               {
                  maxDa0 = da0;
                  maxLv0 = lv0;
               }
            }
         }
         return maxDa0;
      }
      
      public function getWearSiteByThingsType(thingsType0:String) : int
      {
         var unlockB0:Boolean = false;
         var surpportB0:Boolean = false;
         var gripThingsType0:String = null;
         var gripType0:String = null;
         var da0:ThingsData = null;
         var len0:int = PartsType.getWearLen();
         var firstSite0:int = -1;
         for(var i:int = 0; i < len0; i++)
         {
            unlockB0 = saveGroup.getUnlockBySite(i);
            if(unlockB0)
            {
               surpportB0 = true;
               gripThingsType0 = PartsType.getWearThingsType(i);
               gripType0 = PartsType.getWearType(i);
               if(gripThingsType0 != thingsType0)
               {
                  surpportB0 = false;
               }
               else if(Boolean(this.nowArmsData))
               {
                  if(PartsType.supportType(this.nowArmsData,gripType0) == false)
                  {
                     surpportB0 = false;
                  }
               }
               if(surpportB0)
               {
                  if(firstSite0 == -1)
                  {
                     firstSite0 = i;
                  }
                  da0 = getDataBySite(i) as ThingsData;
                  if(!da0)
                  {
                     return i;
                  }
               }
            }
         }
         return firstSite0;
      }
      
      override public function getBatchSellDataArr(colorArr0:Array) : Array
      {
         var n:* = undefined;
         var da0:ThingsData = null;
         var lv0:int = 0;
         var maxLv0:int = PartsMethod.getSellMaxLv();
         var del_arr0:Array = [];
         for(n in dataArr)
         {
            da0 = dataArr[n];
            lv0 = da0.save.getTrueLevel();
            if(lv0 < maxLv0)
            {
               if(da0.save.isPartsNormalB())
               {
                  del_arr0.push(da0);
               }
            }
         }
         return del_arr0;
      }
      
      override public function getBatchSellTextTip() : String
      {
         var maxLv0:int = PartsMethod.getSellMaxLv();
         return "卖出低于" + ComMethod.color(maxLv0 + "级","#00FF00") + "的零件";
      }
      
      public function getCanUnloadArr() : Array
      {
         var da0:ThingsData = null;
         var arr0:Array = [];
         for each(da0 in dataArr)
         {
            if(da0.save.isPartsNormalB() || da0.save.isPartsSpecialB())
            {
               arr0.push(da0);
            }
         }
         return arr0;
      }
      
      override public function sort(dg0:ItemsDataGroup) : void
      {
         sortByTempSortId("parts");
         _siteDataArr = null;
      }
      
      override public function getSaveGroup() : ItemsSaveGroup
      {
         return saveGroup;
      }
      
      public function getSkillArrNull() : Array
      {
         var da0:ThingsData = null;
         var d0:ThingsDefine = null;
         var arr0:Array = null;
         if(TaskType.canPartsB() == false)
         {
            return arr0;
         }
         for each(da0 in dataArr)
         {
            d0 = da0.save.getDefine();
            if(d0.skillArr.length > 0)
            {
               if(!arr0)
               {
                  arr0 = [];
               }
               arr0 = arr0.concat(d0.skillArr);
            }
         }
         return arr0;
      }
   }
}

