package dataAll._app.vip
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   
   public class VipSave
   {
      public static var pro_arr:Array = [];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var obj:Object = {};
      
      public var dayObj:Object = {};
      
      public var upLevelObj:Object = {};
      
      public function VipSave()
      {
         super();
         this.level = 0;
      }
      
      public function get level() : Number
      {
         return this.CF.getAttribute("level");
      }
      
      public function set level(v0:Number) : void
      {
         this.CF.setAttribute("level",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.obj = ClassProperty.copyObj(obj0["obj"]);
         this.dayObj = ClassProperty.copyObj(obj0["dayObj"]);
         this.upLevelObj = ClassProperty.copyObj(obj0["upLevelObj"]);
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.dayObj = {};
      }
      
      public function getGiftEvent(must0:int) : void
      {
         this.obj["m" + must0] = true;
      }
      
      public function getGiftB(must0:int) : Boolean
      {
         var name0:String = "m" + must0;
         if(this.obj.hasOwnProperty(name0))
         {
            return this.obj[name0];
         }
         return false;
      }
      
      public function getDayGiftEvent(must0:int) : void
      {
         this.dayObj["m" + must0] = true;
      }
      
      public function getDayGiftB(must0:int) : Boolean
      {
         var name0:String = "m" + must0;
         if(this.dayObj.hasOwnProperty(name0))
         {
            return this.dayObj[name0];
         }
         return false;
      }
      
      public function upLevel(must0:int) : void
      {
         var name0:String = "m" + must0;
         this.upLevelObj[name0] = true;
      }
      
      public function haveUpLevelB(must0:int) : Boolean
      {
         var name0:String = "m" + must0;
         if(this.upLevelObj.hasOwnProperty(name0))
         {
            return this.upLevelObj[name0];
         }
         return false;
      }
   }
}

