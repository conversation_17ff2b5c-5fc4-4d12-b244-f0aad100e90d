package dataAll._app.union.building
{
   import dataAll._app.union.building.define.UnionBuildingDefine;
   import dataAll.must.define.MustDefine;
   
   public class UnionBuildingData
   {
      public var name:String = "";
      
      public var def:UnionBuildingDefine;
      
      public var save:UnionBuildingSave;
      
      public var buildDataGroup:UnionBuildingDataGroup;
      
      public function UnionBuildingData()
      {
         super();
      }
      
      public function inData_bySave(s0:UnionBuildingSave, buildDataGroup0:UnionBuildingDataGroup) : void
      {
         this.name = s0.name;
         this.save = s0;
         this.def = Gaming.defineGroup.union.building.getDefine(this.name);
         this.buildDataGroup = buildDataGroup0;
      }
      
      public function getNowMaxLv() : Number
      {
         var lv0:int = this.def.maxLv;
         if(lv0 == 0)
         {
            lv0 = UnionBuildingCtrl.getNowMaxLv();
         }
         return lv0;
      }
      
      public function getExpPer() : Number
      {
         return this.save.lv / UnionBuildingCtrl.getMaxLv();
      }
      
      public function getExpString() : String
      {
         return "";
      }
      
      public function getBuildingLv() : int
      {
         var unionLv0:int = this.buildDataGroup.playerData.union.nowUnion.level;
         var lv0:int = Gaming.defineGroup.union.building.property.getMaxInOnePro(unionLv0,"levelRelevance");
         var maxLv0:int = this.getNowMaxLv();
         if(lv0 >= maxLv0)
         {
            lv0 = maxLv0;
         }
         return lv0;
      }
      
      public function canUplevelB() : Boolean
      {
         return this.save.lv < this.getBuildingLv() && this.save.lv < this.getNowMaxLv();
      }
      
      public function getNoUplevelStr() : String
      {
         if(this.save.lv >= this.getBuildingLv())
         {
            return this.def.lvName + "不能超过当前建筑等级";
         }
         if(this.save.lv >= this.getNowMaxLv())
         {
            return this.def.lvName + "已升至最高级" + this.getNowMaxLv() + "级";
         }
         return "";
      }
      
      public function getUplevelMust() : MustDefine
      {
         return UnionBuildingCtrl.getUplevelMust(this);
      }
      
      public function uplevel() : void
      {
         var max0:int = this.getNowMaxLv();
         var lv0:int = this.save.lv;
         lv0++;
         if(lv0 > max0)
         {
            lv0 = max0;
         }
         this.save.lv = lv0;
      }
   }
}

