package dataAll._app.union
{
   import com.common.text.TextWay;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.union.building.UnionBuildingSaveGroup;
   import dataAll._app.union.define.DonationDefine;
   import dataAll._app.union.task.UnionTaskDefine;
   import dataAll._app.union.task.UnionTaskSave;
   
   public class UnionSave
   {
      public static var pro_arr:Array = [];
      
      private static var SCF:NiuBiCF = new NiuBiCF();
      
      public static var startTime:String = TextWay.toCode32("2018-11-26");
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var todayGiftB:Boolean = false;
      
      public var donationNumObj:Object = {};
      
      public var firstTime:String = "";
      
      public var quitT:String = "";
      
      public var taskObj:Object = {};
      
      public var building:UnionBuildingSaveGroup = new UnionBuildingSaveGroup();
      
      public var conObj:Object = {};
      
      public var bGiftB:Boolean = false;
      
      public var mp:String = "";
      
      public function UnionSave()
      {
         super();
         this.removeNum = 0;
         this.unionId = 0;
         this.conDay = 0;
         this.beCon = -1;
      }
      
      public static function get BATTLE_MAX() : Number
      {
         return SCF.getAttribute("BATTLE_MAX");
      }
      
      public static function set BATTLE_MAX(v0:Number) : void
      {
         SCF.setAttribute("BATTLE_MAX",v0);
      }
      
      public static function init() : void
      {
         BATTLE_MAX = 1;
      }
      
      public static function getPhaseByTime(nowTime0:StringDate) : int
      {
         var start0:StringDate = new StringDate(TextWay.getText32(startTime));
         var now0:StringDate = nowTime0;
         var c0:int = now0.reductionOne(start0);
         return int(c0 / 7) + 1;
      }
      
      public function get unionId() : Number
      {
         return this.CF.getAttribute("unionId");
      }
      
      public function set unionId(v0:Number) : void
      {
         this.CF.setAttribute("unionId",v0);
      }
      
      public function get maxCon() : Number
      {
         return this.CF.getAttribute("maxCon");
      }
      
      public function set maxCon(v0:Number) : void
      {
         this.CF.setAttribute("maxCon",v0);
      }
      
      public function get con() : Number
      {
         return this.CF.getAttribute("con");
      }
      
      public function set con(v0:Number) : void
      {
         this.CF.setAttribute("con",v0);
      }
      
      public function get beCon() : Number
      {
         return this.CF.getAttribute("beCon");
      }
      
      public function set beCon(v0:Number) : void
      {
         this.CF.setAttribute("beCon",v0);
      }
      
      public function get afCon() : Number
      {
         return this.CF.getAttribute("afCon");
      }
      
      public function set afCon(v0:Number) : void
      {
         this.CF.setAttribute("afCon",v0);
      }
      
      public function get conDay() : Number
      {
         return this.CF.getAttribute("conDay");
      }
      
      public function set conDay(v0:Number) : void
      {
         this.CF.setAttribute("conDay",v0);
      }
      
      public function get removeNum() : Number
      {
         return this.CF.getAttribute("removeNum");
      }
      
      public function set removeNum(v0:Number) : void
      {
         this.CF.setAttribute("removeNum",v0);
      }
      
      public function get bNum() : Number
      {
         return this.CF.getAttribute("bNum");
      }
      
      public function set bNum(v0:Number) : void
      {
         this.CF.setAttribute("bNum",v0);
      }
      
      public function get bgNum() : Number
      {
         return this.CF.getAttribute("bgNum");
      }
      
      public function set bgNum(v0:Number) : void
      {
         this.CF.setAttribute("bgNum",v0);
      }
      
      public function get lt() : Number
      {
         return this.CF.getAttribute("lt");
      }
      
      public function set lt(v0:Number) : void
      {
         this.CF.setAttribute("lt",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.taskObj = ClassProperty.copySaveObj(obj0["taskObj"],UnionTaskSave);
         this.donationNumObj = ClassProperty.copyObj(obj0["donationNumObj"]);
         this.conObj = ClassProperty.copyObj(obj0["conObj"]);
         this.suppleTaskObj();
         this.building.initSave();
      }
      
      public function initSave() : void
      {
         this.suppleTaskObj();
         this.building.initSave();
      }
      
      private function suppleTaskObj() : void
      {
         var d0:UnionTaskDefine = null;
         var s0:UnionTaskSave = null;
         var arr0:Array = Gaming.defineGroup.union.task.arr;
         for each(d0 in arr0)
         {
            if(!this.taskObj.hasOwnProperty(d0.name))
            {
               s0 = new UnionTaskSave();
               s0.inData_byDefine(d0);
               this.taskObj[d0.name] = s0;
            }
         }
      }
      
      public function newDayCtrl(time0:StringDate) : void
      {
         this.conDay = 0;
         this.todayGiftB = false;
         this.donationNumObj = {};
         this.removeNum = 0;
         this.clearMoreDonationCount(time0);
      }
      
      public function newWeek() : void
      {
      }
      
      public function newWeek6() : void
      {
         this.bGiftB = false;
      }
      
      public function inUnionId(id0:int) : void
      {
         var now0:int = this.unionId;
         if(now0 != id0)
         {
            this.unionId = id0;
         }
      }
      
      public function inNowCon(v0:Number) : void
      {
         this.con = v0;
         if(v0 > this.maxCon)
         {
            this.maxCon = v0;
         }
         if(this.beCon < 0)
         {
            this.beCon = v0;
         }
      }
      
      public function getDonationString(name0:String) : String
      {
         var d0:DonationDefine = Gaming.defineGroup.union.getDonationDefine(name0);
         var str0:String = "";
         str0 += "捐献所需：" + d0.must.getText(true,1);
         str0 += "\n获得贡献：" + ComMethod.color(d0.contribution + "点","#FFFF00");
         return str0 + ("\n剩余次数：" + ComMethod.colorEnoughNum(this.getSurplusDonationNum(name0)) + ComMethod.color("次","#FFFF00"));
      }
      
      public function getDonationBtnActived(name0:String) : Boolean
      {
         var d0:DonationDefine = Gaming.defineGroup.union.getDonationDefine(name0);
         var num0:int = this.getSurplusDonationNum(name0);
         return num0 > 0 && d0.must.panCondition();
      }
      
      public function getSurplusDonationNum(name0:String) : Number
      {
         var num0:int = this.getDonationNum(name0);
         var d0:DonationDefine = Gaming.defineGroup.union.getDonationDefine(name0);
         return d0.dayNum - num0;
      }
      
      public function addDonationNum(name0:String, num0:int = 1) : void
      {
         var nowNum0:int = this.getDonationNum(name0);
         nowNum0 += num0;
         this.setDonationNum(name0,nowNum0);
         var d0:DonationDefine = Gaming.defineGroup.union.getDonationDefine(name0);
         var conNum0:Number = d0.contribution * num0;
         this.addCountDonation(conNum0,Gaming.api.save.getNowServerDate());
      }
      
      public function getDonationNum(name0:String) : Number
      {
         var nowNum0:int = 0;
         if(this.donationNumObj.hasOwnProperty(name0))
         {
            nowNum0 = Number(TextWay.getText32(this.donationNumObj[name0]));
         }
         return nowNum0;
      }
      
      private function setDonationNum(name0:String, v0:int) : void
      {
         this.donationNumObj[name0] = TextWay.toCode32(v0 + "");
      }
      
      private function clearMoreDonationCount(nowTime0:StringDate) : void
      {
         var n:* = undefined;
         var now0:StringDate = nowTime0;
         var nowPhase0:int = getPhaseByTime(nowTime0);
         var newObj0:Object = {};
         for(n in this.conObj)
         {
            if(n >= nowPhase0 - 2)
            {
               newObj0[n] = this.conObj[n];
            }
         }
         this.conObj = newObj0;
      }
      
      public function addCountDonation(num0:Number, nowTime0:StringDate) : void
      {
         var nowPhase0:int = getPhaseByTime(nowTime0);
         if(!this.conObj.hasOwnProperty(nowPhase0))
         {
            this.conObj[nowPhase0] = 0;
         }
         this.conObj[nowPhase0] += num0;
         this.conDay += num0;
         this.beCon += num0;
      }
   }
}

