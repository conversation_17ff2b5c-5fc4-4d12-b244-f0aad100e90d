package dataAll.things.creator
{
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.equip.define.EquipDefine;
   import dataAll.must.define.MustDefine;
   import dataAll.things.define.ThingsDefine;
   
   public class BlackChipConverCtrl
   {
      public function BlackChipConverCtrl()
      {
         super();
      }
      
      public static function getAfterThingsDefineArr(d0:ThingsDefine) : Array
      {
         var d2:ThingsDefine = null;
         var equipD2:EquipDefine = null;
         var armsD2:ArmsRangeDefine = null;
         var equipD0:EquipDefine = Gaming.defineGroup.equip.getDefine(d0.name);
         var armsD0:ArmsRangeDefine = Gaming.defineGroup.bullet.getArmsRangeDefine(d0.name);
         var arr0:Array = Gaming.defineGroup.things.getConverChipDefineArr();
         var newArr0:Array = [];
         for each(d2 in arr0)
         {
            if(Math.ceil(d0.itemsLevel / 5) == Math.ceil(d2.itemsLevel / 5))
            {
               equipD2 = Gaming.defineGroup.equip.getDefine(d2.name);
               armsD2 = Gaming.defineGroup.bullet.getArmsRangeDefine(d0.name);
               if(Boolean(equipD0) && Boolean(equipD2))
               {
                  if(equipD0.type == equipD2.type && equipD0 != equipD2)
                  {
                     newArr0.push(d2);
                  }
               }
               else if(Boolean(armsD0) && Boolean(armsD2))
               {
                  if(armsD0 != armsD2)
                  {
                     newArr0.push(d2);
                  }
               }
            }
         }
         return newArr0;
      }
      
      public static function getMustDefine(d0:ThingsDefine, num0:int, moneyB0:Boolean) : MustDefine
      {
         var goodsName0:String = null;
         var goodsDefine0:GoodsDefine = null;
         var m0:MustDefine = new MustDefine();
         var thingsNum0:int = getMustNum(d0,num0,moneyB0);
         m0.coin = (moneyB0 ? 0 : 50000) * num0;
         m0.inThingsDataByArr([d0.name + ";" + thingsNum0]);
         if(moneyB0)
         {
            goodsName0 = "blackChipConver";
            if(Math.ceil(d0.itemsLevel / 5) == 18)
            {
               goodsName0 = "blackChipConver90";
            }
            goodsDefine0 = Gaming.defineGroup.goods.getDefine(goodsName0);
            m0.propId = goodsDefine0.propId;
            m0.money = goodsDefine0.price * num0;
            m0.propNum = num0;
         }
         return m0;
      }
      
      public static function getMustNum(d0:ThingsDefine, num0:int, moneyB0:Boolean) : int
      {
         return int((moneyB0 ? 1 : 2) * num0);
      }
   }
}

