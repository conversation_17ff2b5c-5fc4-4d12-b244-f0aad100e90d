package dataAll.pet.gene.creator
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll._player.PlayerData;
   import dataAll.equip.creator.EquipPropertyDataCreator;
   import dataAll.equip.define.EquipColor;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.pet.PetCount;
   import dataAll.pet.gene.GeneData;
   import dataAll.pet.gene.define.GeneDefine;
   import dataAll.pet.gene.define.GeneDefineGroup;
   import dataAll.pet.gene.define.GeneDropDefine;
   import dataAll.pet.gene.save.GeneSave;
   import dataAll.pro.PropertyArrayDefine;
   import dataAll.pro.PropertyArrayDefineGroup;
   import dataAll.skill.define.SkillDefine;
   
   public class GeneDataCreator
   {
      public function GeneDataCreator()
      {
         super();
      }
      
      public static function getTempDataByGift(d0:GiftAddDefine) : GeneData
      {
         return getTempData(Gaming.defineGroup.geneCreator.getSaveByGift(d0,Gaming.PG.da.level));
      }
      
      public static function getTempData(s0:GeneSave) : GeneData
      {
         var da0:GeneData = new GeneData();
         da0.inData_bySave(s0,Gaming.PG.da);
         return da0;
      }
      
      public function getSaveByBodyNull(enemyName0:String, bodyLv0:int, diff0:int, mapMode0:String) : GeneSave
      {
         var geneDefine0:GeneDefine = Gaming.defineGroup.gene.getDefineByTargetName(enemyName0);
         var dropDefine0:GeneDropDefine = Gaming.defineGroup.gene.drop;
         var rareDropAdd0:Number = Gaming.PG.da.moreWay.get_rareGeneDropPro();
         var color0:String = dropDefine0.getDropColorByGeneType(geneDefine0.type,rareDropAdd0);
         if(mapMode0 == MapMode.DEMON && color0 != EquipColor.RED)
         {
            return null;
         }
         return this.getSave(color0,bodyLv0,geneDefine0.name);
      }
      
      public function getSaveByGift(d0:GiftAddDefine, heroLv0:int) : GeneSave
      {
         if(d0.name == "box")
         {
            return this.getBoxSave(heroLv0);
         }
         if(d0.childType == "super")
         {
            return this.getSuperSave(heroLv0,d0.name);
         }
         return this.getSave("red",heroLv0,d0.name);
      }
      
      private function getBoxSave(heroLv0:int) : GeneSave
      {
         var nameArr0:Array = Gaming.defineGroup.gene.getNormalNameArr();
         var name0:String = nameArr0[int(Math.random() * nameArr0.length)];
         var colorArr0:Array = ["purple","orange","red"];
         var proArr0:Array = [0.7,0.22,0.08];
         var color0:String = colorArr0[ComMethod.getPro_byArrSum(proArr0)];
         return this.getSave(color0,heroLv0,name0);
      }
      
      public function getSave(color0:String, lv0:int, petName0:String, haveObjB0:Boolean = true) : GeneSave
      {
         var s0:GeneSave = new GeneSave();
         var d0:GeneDefine = Gaming.defineGroup.gene.getDefine(petName0);
         s0.inDataByDefine(d0);
         if(haveObjB0)
         {
            s0.obj = this.getObjBy(color0,lv0);
         }
         var bodyLv0:int = lv0 - PetCount.cLevel;
         if(bodyLv0 < 1)
         {
            bodyLv0 = 1;
         }
         s0.itemsLevel = bodyLv0;
         s0.color = color0;
         this.setSkillArr(s0,d0);
         return s0;
      }
      
      private function setSkillArr(s0:GeneSave, d0:GeneDefine) : void
      {
         var dropDefine0:GeneDropDefine = Gaming.defineGroup.gene.drop;
         var index0:int = int(Gaming.defineGroup.gene.drop.name.indexOf(s0.color));
         var talentNum0:int = int(dropDefine0.talentSkillNum[index0]);
         var laterNum0:int = int(dropDefine0.laterSkillNum[index0]);
         s0.talentSkillArr = this.getTalentSkillArrBy(talentNum0,d0);
         s0.laterSkillArr = this.getLaterSkillArrBy(laterNum0,s0.talentSkillArr);
      }
      
      private function getTalentSkillArrBy(num0:int, d0:GeneDefine) : Array
      {
         var allArr0:Array = d0.talentSkillArr;
         return ComMethod.getRandomArray(allArr0,num0);
      }
      
      public function getLaterSkillArrBy(num0:int, talentSkillArr0:Array) : Array
      {
         var allArr0:Array = Gaming.defineGroup.skill.petSkillNameArr;
         allArr0 = ComMethod.deductArr(allArr0,talentSkillArr0);
         return ComMethod.getRandomArray(allArr0,num0);
      }
      
      private function getObjBy(color0:String, lv0:int) : Object
      {
         var dg0:GeneDefineGroup = Gaming.defineGroup.gene;
         var index0:int = int(dg0.drop.name.indexOf(color0));
         var proNum0:int = int(dg0.drop.proNum[index0]);
         var allProArr0:Array = dg0.allProNameArr;
         var nowArr0:Array = ComMethod.getRandomArray(allProArr0,proNum0);
         return this.getObj(color0,lv0,nowArr0,false);
      }
      
      private function getObj(color0:String, lv0:int, proNameArr0:Array, superB0:Boolean) : Object
      {
         var name0:* = null;
         var value0:Number = NaN;
         var obj0:Object = {};
         var dropDefine0:GeneDropDefine = Gaming.defineGroup.gene.drop;
         var proDefine0:PropertyArrayDefineGroup = Gaming.defineGroup.gene.pro;
         var index0:int = int(dropDefine0.name.indexOf(color0));
         var vR0:Array = dropDefine0.valueRange[index0];
         for each(name0 in proNameArr0)
         {
            value0 = this.getOneProValue(name0,lv0,vR0,superB0);
            obj0[name0] = value0;
         }
         return obj0;
      }
      
      private function getOneProValue(name0:String, lv0:int, vR0:Array, superB0:Boolean) : Number
      {
         var proDefine0:PropertyArrayDefineGroup = Gaming.defineGroup.gene.pro;
         var d0:PropertyArrayDefine = proDefine0.getDefine(name0);
         var max0:Number = proDefine0.getPropertyValue(name0,lv0);
         var min0:Number = proDefine0.getPropertyValue(name0,lv0 - 10);
         var max2:Number = (max0 - min0) * vR0[1] + min0;
         var min2:Number = (max0 - min0) * vR0[0] + min0;
         var v0:Number = min2 + Math.random() * (max2 - min2);
         if(superB0)
         {
            v0 = max0;
         }
         return d0.fixedNumber(v0);
      }
      
      public function getTempSuperData(petName0:String) : GeneData
      {
         var da0:GeneData = new GeneData();
         var pd0:PlayerData = Gaming.PG.da;
         var s0:GeneSave = this.getSuperSave(pd0.level,petName0);
         da0.inData_bySave(s0,pd0);
         return da0;
      }
      
      public function getSuperSave(lv0:int, petName0:String) : GeneSave
      {
         var d0:GeneDefine = Gaming.defineGroup.gene.getDefine(petName0);
         var s0:GeneSave = this.getSave("red",lv0,petName0,false);
         s0.obj = this.getSuperObj(s0.color,lv0,d0);
         s0.laterSkillArr = d0.laterSkillArr.concat([]);
         return s0;
      }
      
      private function getSuperObj(color0:String, lv0:int, d0:GeneDefine) : Object
      {
         return this.getObj(color0,lv0,d0.talentProArr,true);
      }
      
      public function getGather(s0:GeneSave) : String
      {
         var str0:String = "";
         str0 += "<i1>|<blue <b>资质：</b>/>";
         str0 += "\n" + this.getGather_byObj(s0.obj);
         str0 += "\n<i1>|<blue <b>天赋技能：</b>/>";
         str0 += "\n" + this.getGather_bySkillArr(s0.talentSkillArr);
         str0 += "\n\n<i1>|<blue <b>后天技能：</b>/>";
         return str0 + ("\n" + this.getGather_bySkillArr(s0.laterSkillArr));
      }
      
      private function getGather_bySkillArr(skillArr0:Array) : String
      {
         var name0:* = null;
         var d0:SkillDefine = null;
         var str0:String = "";
         for each(name0 in skillArr0)
         {
            d0 = Gaming.defineGroup.skill.getDefine(name0);
            if(str0 != "")
            {
               str0 += "\n" + d0.cnName;
            }
            else
            {
               str0 += d0.cnName;
            }
         }
         return str0;
      }
      
      public function getGather_byObj(obj0:Object, compareObj0:Object = null) : String
      {
         var proDefineArr0:Array = Gaming.defineGroup.gene.pro.propertyArr;
         var str0:String = "";
         return str0 + EquipPropertyDataCreator.getTextRange(obj0,proDefineArr0,"gray",compareObj0);
      }
      
      public function getPetLakeSave(bodyLv0:int) : GeneSave
      {
         var color0:String = null;
         var rareDropAdd0:Number = Gaming.PG.da.moreWay.get_rareGeneDropPro();
         var colorArr0:Array = ["purple","orange"];
         var proArr0:Array = [0.1,0.016666666666666666 * (1 + rareDropAdd0)];
         var index0:int = ComMethod.getPro_byArrNo(proArr0);
         if(index0 >= 0)
         {
            color0 = colorArr0[index0];
            return this.getSave(color0,bodyLv0,"PetLake");
         }
         return null;
      }
   }
}

