package dataAll.gift.dailySign
{
   import com.sounto.oldUtils.Sounto64;
   import com.sounto.utils.ClassProperty;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class DailyGiftDefine
   {
      public static var pro_arr:Array = [];
      
      public var cnName:String = "";
      
      private var _mustNum:String = "";
      
      public var gift:GiftAddDefineGroup = new GiftAddDefineGroup();
      
      public var vipGift:GiftAddDefineGroup = new GiftAddDefineGroup();
      
      public function DailyGiftDefine()
      {
         super();
         this.mustNum = 999;
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         this.gift.inData_byXML(xml0.gift);
         this.vipGift.inData_byXML(xml0.vipGift);
         if(this.cnName == "")
         {
            if(this.mustNum == 0)
            {
               this.cnName = "每日";
            }
            else
            {
               this.cnName = this.mustNum + "次";
            }
         }
      }
      
      public function getName() : String
      {
         return String(this.mustNum);
      }
      
      public function getMustNum(monthDay0:int) : int
      {
         var mustNum0:int = this.mustNum;
         if(mustNum0 == 30)
         {
            return monthDay0;
         }
         return mustNum0;
      }
      
      public function set mustNum(v0:Number) : void
      {
         this._mustNum = Sounto64.encode(String(v0));
      }
      
      public function get mustNum() : Number
      {
         return Number(Sounto64.decode(this._mustNum));
      }
   }
}

