package dataAll.gift.define
{
   import com.common.data.Base64;
   import com.common.text.TextWay;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.Sounto64;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.NumberMethod;
   import dataAll._app.edit.card.BCardPKCreator;
   import dataAll._app.edit.card.BossCardData;
   import dataAll._app.food.FoodRawDefine;
   import dataAll._app.head.define.HeadDefine;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.arms.skin.ArmsSkinDefine;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.device.DeviceDefine;
   import dataAll.equip.jewelry.JewelryDefine;
   import dataAll.equip.shield.ShieldDefine;
   import dataAll.equip.vehicle.VehicleDefine;
   import dataAll.equip.weapon.WeaponDefine;
   import dataAll.items.save.ItemsSave;
   import dataAll.pet.gene.define.GeneDefine;
   import dataAll.things.define.ThingsDefine;
   import dataAll.ui.GatherColor;
   
   public class GiftAddDefine
   {
      public static var pro_arr:Array = [];
      
      public static var pro_arr2:Array = ["type","name","num","color","lv","childType","numExtra","tipB","dropName","pro","sp"];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var type:String = "";
      
      public var name:String = "";
      
      private var _num:String = "";
      
      public var color:String = "";
      
      private var _lv:String = "";
      
      public var childType:String = "";
      
      public var numExtra:String = "";
      
      public var dropName:String = "diff_0";
      
      public var sp:Number = 0;
      
      public var showLv:int = 0;
      
      public var itemsSave:ItemsSave;
      
      public var tipB:Boolean = false;
      
      public function GiftAddDefine()
      {
         super();
         this.num = 1;
         this.lv = "1";
         this.pro = 0;
      }
      
      public function get pro() : Number
      {
         return this.CF.getAttribute("pro");
      }
      
      public function set pro(v0:Number) : void
      {
         this.CF.setAttribute("pro",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr2);
      }
      
      public function samePan(d0:GiftAddDefine) : Boolean
      {
         if(this.canOverlayB())
         {
            if(this.type == "things" || this.type == GiftType.foodRaw)
            {
               return this.type == d0.type && this.name == d0.name;
            }
            return this.type == d0.type && this.name == d0.name && this.color == d0.color && this.lv == d0.lv && this.childType == d0.childType;
         }
         return false;
      }
      
      public function isThingsB() : Boolean
      {
         return this.type == GiftType.things;
      }
      
      public function isThingsOrBaseB() : Boolean
      {
         return this.type == GiftType.things || this.type == GiftType.base;
      }
      
      public function isNoBagLimitB() : Boolean
      {
         return GiftType.noBagLimitArr.indexOf(this.getTrueType()) >= 0;
      }
      
      public function getTrueType() : String
      {
         var d0:ThingsDefine = null;
         if(this.type == GiftType.things)
         {
            d0 = Gaming.defineGroup.things.getDefine(this.name);
            if(Boolean(d0))
            {
               if(d0.isPartsB())
               {
                  return GiftType.parts;
               }
            }
         }
         return this.type;
      }
      
      public function getDropType() : String
      {
         if(this.type == GiftType.parts)
         {
            return GiftType.things;
         }
         return this.type;
      }
      
      public function canOverlayB() : Boolean
      {
         var t_d0:ThingsDefine = null;
         if(this.type == "base" || this.type == GiftType.foodRaw)
         {
            return true;
         }
         if(this.type == "things" || this.type == "parts")
         {
            t_d0 = Gaming.defineGroup.things.getDefine(this.name);
            if(t_d0 is ThingsDefine)
            {
               return !t_d0.noOverlayB;
            }
         }
         else if(this.type == "arms" || this.type == "equip" || this.type == "more" || this.type == "gene")
         {
            if(this.type == "equip" && Boolean(Gaming.defineGroup.getNumEquipDefine(this.name)))
            {
               return true;
            }
         }
         return false;
      }
      
      public function clone() : GiftAddDefine
      {
         var d0:GiftAddDefine = new GiftAddDefine();
         d0.inData_byObj(this);
         d0.itemsSave = Boolean(this.itemsSave) ? this.itemsSave.copyOne() : null;
         return d0;
      }
      
      public function inData_byXML(x0:XML) : void
      {
         this.inData_byStr(String(x0));
         ClassProperty.inData_byXMLAt(this,x0,pro_arr);
      }
      
      public function inData_byStr(str0:String) : void
      {
         var n:* = undefined;
         var value0:String = null;
         var arr0:Array = str0.split(";");
         var proArr:Array = pro_arr2;
         for(n in arr0)
         {
            value0 = TextWay.toHan2(arr0[n]);
            if(!arr0[n])
            {
               value0 = "";
            }
            ClassProperty.setProByString(this,proArr[n],value0);
         }
      }
      
      public function inData_byCnStr(str0:String) : String
      {
         var thingsD0:ThingsDefine = null;
         var deviceD0:DeviceDefine = null;
         var vehicleD0:VehicleDefine = null;
         var weaponD0:WeaponDefine = null;
         var fashionD0:EquipDefine = null;
         var jewelryD0:JewelryDefine = null;
         var shieldD0:ShieldDefine = null;
         var geneD0:GeneDefine = null;
         var armsD0:ArmsRangeDefine = null;
         var headD0:HeadDefine = null;
         var foodRawD0:FoodRawDefine = null;
         var cardDa0:BossCardData = null;
         var trueStr0:String = "";
         var strArr0:Array = str0.split("*");
         var cn0:String = strArr0[0];
         var num0:Number = Number(strArr0[1]);
         var baseName0:String = GiftBase.getNameByCn(cn0);
         if(baseName0 != "")
         {
            trueStr0 = "base;" + baseName0 + ";" + num0;
         }
         else
         {
            thingsD0 = Gaming.defineGroup.things.getDefineByCnName(cn0);
            deviceD0 = Gaming.defineGroup.device.getDeviceDefineByCn(cn0);
            vehicleD0 = Gaming.defineGroup.vehicle.getDefineByCn(cn0);
            weaponD0 = Gaming.defineGroup.weapon.getWeaponDefineByCn(cn0);
            fashionD0 = Gaming.defineGroup.equip.getFashionDefineByCn(cn0);
            jewelryD0 = Gaming.defineGroup.jewelry.getJewelryDefineByCn(cn0);
            shieldD0 = Gaming.defineGroup.shield.getShieldDefineByCn(cn0);
            geneD0 = Gaming.defineGroup.gene.getDefineByCn(cn0);
            armsD0 = Gaming.defineGroup.bullet.getArmsRangeDefineByCn(cn0);
            headD0 = Gaming.defineGroup.head.getDefineByCn(cn0);
            foodRawD0 = Gaming.defineGroup.food.raw.getNormalDefineByCn(cn0) as FoodRawDefine;
            cardDa0 = BCardPKCreator.getData(cn0);
            if(Boolean(thingsD0))
            {
               if(thingsD0.isPartsB())
               {
                  trueStr0 = "parts;" + thingsD0.name + ";" + num0;
               }
               else
               {
                  trueStr0 = "things;" + thingsD0.name + ";" + num0;
               }
            }
            else if(Boolean(deviceD0))
            {
               trueStr0 = "equip;" + deviceD0.name + ";" + num0;
            }
            else if(Boolean(vehicleD0))
            {
               trueStr0 = "equip;" + vehicleD0.name + ";" + num0;
            }
            else if(Boolean(weaponD0))
            {
               trueStr0 = "equip;" + weaponD0.name + ";" + num0;
            }
            else if(Boolean(jewelryD0))
            {
               trueStr0 = "equip;" + jewelryD0.name + ";" + num0;
            }
            else if(Boolean(shieldD0))
            {
               trueStr0 = "equip;" + shieldD0.name + ";" + num0;
            }
            else if(Boolean(fashionD0))
            {
               trueStr0 = "equip;" + fashionD0.name + ";" + num0;
            }
            else if(Boolean(geneD0))
            {
               trueStr0 = "gene;" + geneD0.name + ";" + num0;
            }
            else if(Boolean(armsD0))
            {
               trueStr0 = "arms;" + armsD0.def.name + ";" + num0;
            }
            else if(Boolean(headD0))
            {
               trueStr0 = "head;" + headD0.name + ";" + num0;
            }
            else if(Boolean(foodRawD0))
            {
               trueStr0 = "foodRaw;" + foodRawD0.name + ";" + num0;
            }
            else
            {
               if(!Boolean(cardDa0))
               {
                  return "找不到物品“" + cn0 + "”";
               }
               trueStr0 = "bossCard;" + cn0 + ";" + num0;
            }
         }
         this.inData_byStr(trueStr0);
         return "";
      }
      
      public function set num(v0:Number) : void
      {
         this._num = TextWay.toCode(Sounto64.encode(String(v0)));
      }
      
      public function get num() : Number
      {
         return Number(Sounto64.decode(TextWay.getText(this._num)));
      }
      
      public function set lv(v0:String) : void
      {
         this._lv = Base64.encodeString(String(v0));
      }
      
      public function get lv() : String
      {
         return Base64.decodeString(this._lv);
      }
      
      public function converToTrue(lv0:int) : void
      {
         var extra0:Number = NaN;
         if(this.numExtra != "")
         {
            extra0 = Gaming.defineGroup.normal.getByName(this.numExtra,lv0);
            this.num = Math.ceil(this.num * extra0);
         }
         if(this.lv == "taskLv")
         {
            this.lv = lv0 + "";
         }
      }
      
      public function getBaseCnName() : String
      {
         return GiftBase.getCn(this.name);
      }
      
      public function getBaseIconUrl() : String
      {
         return GiftBase.getIconUrl(this.name);
      }
      
      public function getBaseGatherTip() : String
      {
         return GiftBase.getGatherTip(this.name,this.num);
      }
      
      public function getGatherTip() : String
      {
         var d0:IO_GiftDefine = null;
         if(this.type == GiftType.base)
         {
            return this.getBaseGatherTip();
         }
         d0 = this.getIODefine();
         if(Boolean(d0))
         {
            return d0.getGiftTip();
         }
         return this.getCnName();
      }
      
      public function getXMLString() : String
      {
         return "<gift>" + this.type + ";" + this.name + ";" + this.num + "</gift>";
      }
      
      public function getDescription(colorB0:Boolean = true, allPro0:Number = 0) : String
      {
         var pro0:Number = NaN;
         var proStr0:String = null;
         var cn0:String = this.getCnName();
         var s0:String = "";
         if(colorB0)
         {
            s0 = ComMethod.color(cn0 + "x","#00FF00") + ComMethod.color(this.num + "","#FFFF00");
         }
         else
         {
            s0 = cn0 + "x" + this.num;
         }
         if(allPro0 > 0)
         {
            pro0 = this.pro / allPro0;
            if(this.sp > 0)
            {
               pro0 = this.sp;
            }
            proStr0 = " (" + NumberMethod.toPer(pro0) + ")";
            if(colorB0)
            {
               proStr0 = ComMethod.color(proStr0,GatherColor.gray2Color);
            }
            s0 += proStr0;
         }
         return s0;
      }
      
      public function getCnName() : String
      {
         var d0:IO_GiftDefine = null;
         if(this.type == GiftType.base)
         {
            return this.getBaseCnName();
         }
         d0 = this.getIODefine();
         if(Boolean(d0))
         {
            return d0.getGiftCn();
         }
         return this.name;
      }
      
      public function getIconUrl() : String
      {
         var d0:IO_GiftDefine = null;
         if(this.type == GiftType.base)
         {
            return this.getBaseIconUrl();
         }
         d0 = this.getIODefine();
         if(Boolean(d0))
         {
            return d0.getGiftIconUrl();
         }
         return "";
      }
      
      public function getIODefine() : IO_GiftDefine
      {
         var headD0:HeadDefine = null;
         var equipD0:EquipDefine = null;
         var armsD0:ArmsDefine = null;
         var d0:ThingsDefine = null;
         var bossCardDa0:BossCardData = null;
         var geneD0:GeneDefine = null;
         var foodRawD0:FoodRawDefine = null;
         var armsSkinD0:ArmsSkinDefine = null;
         if(this.type == GiftType.head)
         {
            return Gaming.defineGroup.head.getDefine(this.name);
         }
         if(this.type == GiftType.equip)
         {
            return Gaming.defineGroup.getAllEquipDefine(this.name);
         }
         if(this.type == GiftType.arms)
         {
            return Gaming.defineGroup.bullet.getArmsDefine(this.name);
         }
         if(this.type == GiftType.things || Boolean(GiftType.parts))
         {
            return Gaming.defineGroup.things.getDefine(this.name);
         }
         if(this.type == GiftType.bossCard)
         {
            return BCardPKCreator.getData(this.name);
         }
         if(this.type == GiftType.gene)
         {
            return Gaming.defineGroup.gene.getDefine(this.name);
         }
         if(this.type == GiftType.foodRaw)
         {
            return Gaming.defineGroup.food.raw.getNormalDefine(this.name) as FoodRawDefine;
         }
         if(this.type == GiftType.armsSkin)
         {
            return Gaming.defineGroup.armsCharger.getSkin(this.name);
         }
         return null;
      }
   }
}

