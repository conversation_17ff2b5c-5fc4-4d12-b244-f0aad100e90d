package dataAll._base
{
   import com.sounto.utils.ClassProperty;
   
   public class NormalDefine implements IO_NormalDefine
   {
      public var father:String = "";
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var ele:String = "";
      
      public function NormalDefine()
      {
         super();
      }
      
      public function getFather() : String
      {
         return this.father;
      }
      
      public function getName() : String
      {
         return this.name;
      }
      
      public function getCnName() : String
      {
         return this.cnName;
      }
      
      public function inData_byXML(xml0:XML, father0:String = "") : void
      {
         if(String(xml0.@father) == "")
         {
            this.father = father0;
         }
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,this.getBaseClassProArr());
         if(this.name.indexOf(" ") >= 0)
         {
            INIT.showError(this.name + "不能包含空格！");
         }
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,this.getFinalProArr());
      }
      
      protected function getBaseClassProArr() : Array
      {
         INIT.showError("该函数必须被覆盖！");
         return null;
      }
      
      protected function getFinalProArr() : Array
      {
         return this.getBaseClassProArr();
      }
   }
}

