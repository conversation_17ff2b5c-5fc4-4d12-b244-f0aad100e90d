package dataAll.equip.suit
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll.equip.add.IO_EquipAddGetter;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipFatherDefine;
   import dataAll.equip.define.EquipSuitPropertyMul;
   import dataAll.pro.PropertyArrayDefine;
   
   public class SuitEquipDataObj implements IO_EquipAddGetter
   {
      public var obj:Object = {};
      
      public var mustPartNum:int = 0;
      
      public var color:String = "";
      
      public var lv:int = 0;
      
      public var fatherDefine:EquipFatherDefine;
      
      public var name:String = "";
      
      public var tempIndex:int = 0;
      
      public var suitPartNum:int = 0;
      
      public function SuitEquipDataObj()
      {
         super();
      }
      
      public function getProAddObj() : Object
      {
         return this.obj;
      }
      
      public function getProAddMax(pro0:String) : Number
      {
         return 0;
      }
      
      public function inData(f0:EquipFatherDefine, minLv0:int, color0:String, partNum0:int) : void
      {
         var n:* = undefined;
         var mulD0:EquipSuitPropertyMul = null;
         var proName0:String = null;
         var proDefine0:PropertyArrayDefine = null;
         var v0:Number = NaN;
         var obj0:Object = {};
         var proArr0:Array = f0.suitArray;
         var proNum0:int = SuitCtreator.getProNumBy(color0,partNum0);
         this.mustPartNum = SuitCtreator.getMustPartNumBy(partNum0);
         for(n in proArr0)
         {
            if(n < proNum0)
            {
               mulD0 = proArr0[n];
               proName0 = mulD0.name;
               proDefine0 = Gaming.defineGroup.suitProperty.getDefine(proName0);
               v0 = Gaming.defineGroup.suitProperty.getPropertyValue(proName0,minLv0);
               if(mulD0.mul > 0)
               {
                  v0 *= mulD0.mul;
               }
               else
               {
                  v0 = mulD0.value;
               }
               v0 = proDefine0.fixedNumber(v0);
               obj0[proName0] = v0;
            }
         }
         this.obj = obj0;
         this.color = color0;
         this.lv = minLv0;
         this.fatherDefine = f0;
         this.name = f0.name;
      }
      
      public function getObjByProArr(proArr0:Array) : Object
      {
         var name0:* = null;
         var newObj0:Object = {};
         for each(name0 in proArr0)
         {
            newObj0[name0] = this.obj[name0];
         }
         return newObj0;
      }
      
      public function getTitleText() : String
      {
         var str0:String = "";
         str0 += ComMethod.color(this.fatherDefine.cnName,EquipColor.htmlColor(this.color));
         return str0 + (" " + this.lv + "级");
      }
      
      public function getGatherTip(showInfoB0:Boolean = false) : String
      {
         var str0:String = SuitCtreator.getGatherTipByObj(this.obj,this.fatherDefine,this.lv,this.color,this.mustPartNum);
         if(showInfoB0)
         {
            str0 += "\n\n<i1>|<blue <b>说明：</b>/>";
            str0 += "\n1、当你穿上套装时，套装等级、品质不超过4种装备中所拥有的最低等级、最差品质。";
            str0 += "\n2、所有套装只要集齐2件就可开启一条套装属性，集齐3件可开启剩余套装属性。";
            str0 += "\n3、当身上有两种套装时，只激活拥有战衣的那个套装。";
         }
         return str0;
      }
      
      public function getColorCnName() : String
      {
         return ComMethod.color(this.fatherDefine.getTitleText(),EquipColor.htmlColor(this.color));
      }
   }
}

