package dataAll._app.task
{
   import UI.base.button.NormalBtn;
   import UI.task.TaskCondtionText;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.StringMethod;
   import dataAll._app.task.define.TaskConditionDefine;
   import dataAll._app.task.define.TaskConditionType;
   import dataAll._app.task.define.TaskDefine;
   import dataAll._app.task.define.TaskDefineGroup;
   import dataAll._app.task.define.TaskFatherDefine;
   import dataAll._app.task.define.TaskType;
   import dataAll._app.task.save.TaskSave;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._app.worldMap.save.WorldMapSave;
   import dataAll._player.PlayerData;
   import dataAll.body.define.BodyCamp;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.level.TempLevel;
   import dataAll.level.define.LevelDefine;
   import dataAll.level.define.event.LevelEventConditionDefine;
   import dataAll.things.define.ThingsDefine;
   import dataAll.ui.GatherColor;
   
   public class TaskData
   {
      public var def:TaskDefine = null;
      
      public var save:TaskSave = null;
      
      public var playerData:PlayerData;
      
      private var tempDef:TaskDefine;
      
      public var uiFleshB:Boolean = false;
      
      public var die:int = 0;
      
      public function TaskData()
      {
         super();
      }
      
      public function inData_byDefine(def0:TaskDefine, playerData0:PlayerData) : void
      {
         this.playerData = playerData0;
         this.def = def0;
         this.save = new TaskSave();
         this.save.name = this.def.name;
         this.setDiff(0);
         this.fleshTempDefine();
      }
      
      public function inData_bySave(s0:TaskSave, playerData0:PlayerData) : void
      {
         var fatherD0:TaskFatherDefine = null;
         this.playerData = playerData0;
         this.save = s0;
         this.def = Gaming.defineGroup.task.getOneDefine(this.save.name);
         if(this.def.father == TaskType.WEEK)
         {
            if(this.save.map == "PrisonDeep")
            {
               this.save.map = "WoTu";
            }
         }
         if(Boolean(this.playerData) && this.playerData.level > 1)
         {
            fatherD0 = Gaming.defineGroup.task.getFatherDefine(this.def.father);
            if(fatherD0.maxLvLimitB && this.def.maxLvLimitB)
            {
               if(s0.lv > this.playerData.level)
               {
                  s0.lv = this.playerData.level;
               }
            }
         }
         this.fleshTempDefine();
      }
      
      public function get state() : String
      {
         return this.save.state;
      }
      
      public function dayInit() : void
      {
         this.save.dayInit();
      }
      
      private function fleshTempDefine() : void
      {
         if(this.def.haveGrowthB)
         {
            this.tempDef = this.def.growthArr[this.save.diff];
            if(!(this.tempDef is TaskDefine))
            {
               INIT.showErrorMust("找不到等级" + this.save.diff + "的任务定义：" + this.def.cnName);
            }
         }
         else
         {
            this.tempDef = this.def;
         }
      }
      
      public function fixedLevelByMust() : void
      {
         if(this.save.lev != "" && this.def.fixedLevelUrl != "")
         {
            TempLevel.fixedLevel(this.def.fixedLevelUrl,this.save.lev,this.getLv());
         }
         if(this.def.name == "doubleBoss")
         {
            TempLevel.addDoubleBoss();
         }
      }
      
      public function isCompleteOrOver() : Boolean
      {
         return this.state == TaskState.complete || this.state == TaskState.over;
      }
      
      public function isIng() : Boolean
      {
         return this.state == TaskState.ing;
      }
      
      public function isIngOrComplete() : Boolean
      {
         return this.state == TaskState.ing || this.state == TaskState.complete;
      }
      
      public function isOver() : Boolean
      {
         return this.state == TaskState.over;
      }
      
      public function isUiShowTipB() : Boolean
      {
         if(this.state == TaskState.no || this.state == TaskState.complete)
         {
            if(!this.def.openD.timeLimitB())
            {
               return true;
            }
            if(!Boolean(this.playerData))
            {
               return false;
            }
            if(this.def.openD.timePanB(this.playerData.time.getReadTimeDate()))
            {
               return true;
            }
         }
         return false;
      }
      
      public function dealOne() : Boolean
      {
         return TaskDeal.dealOne(this);
      }
      
      public function dealConditionCumulative() : void
      {
         if(this.tempDef.condition.cumulativeType == "no" && this.state != TaskState.complete)
         {
            this.save.c = 0;
            this.uiFleshB = false;
         }
      }
      
      public function getBtnOverTip(btn0:NormalBtn) : String
      {
         if(btn0.actived == false)
         {
            return this.def.getUnlockText();
         }
         if(this.isOver())
         {
            if(this.def.isDayClearB())
            {
               return "明天可重新接取该任务。";
            }
            if(this.def.isWeekClearB())
            {
               return "下周一可重新接取该任务。";
            }
         }
         return "";
      }
      
      public function checkFleshConditon(fun0:Function) : void
      {
         var c0:LevelEventConditionDefine = null;
         var bb0:Boolean = false;
         var arr_len0:int = int(this.def.uiFleshConditionArr.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            c0 = this.def.uiFleshConditionArr[i];
            bb0 = fun0(c0);
            if(bb0)
            {
               this.uiFleshB = false;
               return;
            }
         }
      }
      
      public function checkCondition() : void
      {
         var con0:TaskConditionDefine = this.getCondition();
         var bb0:Boolean = this.checkConditionDefine(con0);
         if(bb0)
         {
            this.complete(false);
         }
      }
      
      public function checkConditionDefine(con0:TaskConditionDefine) : Boolean
      {
         if(con0.type == TaskConditionType.collect)
         {
            if(this.save.c >= con0.value)
            {
               return true;
            }
         }
         else if(con0.type == TaskConditionType.levelWin)
         {
            if(Gaming.LG.isWinB())
            {
               return true;
            }
         }
         return false;
      }
      
      private function setDiff(diff0:int) : void
      {
         if(this.def.haveGrowthB)
         {
            this.save.diff = diff0;
            this.fleshTempDefine();
         }
      }
      
      public function setToNextDiff() : void
      {
         if(this.def.haveGrowthB)
         {
            this.setDiff((this.save.diff + 1) % this.def.getGrowthNum());
         }
      }
      
      public function getDiff() : int
      {
         if(this.def.haveGrowthB)
         {
            return this.save.diff;
         }
         return -1;
      }
      
      public function getMaxDiff() : int
      {
         if(this.def.haveGrowthB)
         {
            return this.def.getGrowthNum();
         }
         return 1;
      }
      
      public function getSaveDiff() : int
      {
         if(Boolean(this.save))
         {
            return this.save.diff;
         }
         return 0;
      }
      
      public function getDropName() : String
      {
         return this.tempDef.drop.dropName;
      }
      
      public function getLastGiftThingsDefine() : ThingsDefine
      {
         return this.tempDef.getLastGiftThingsDefine();
      }
      
      public function dealLevelDiff(diff0:Number) : Number
      {
         if(this.tempDef.diff > 0)
         {
            return this.tempDef.diff;
         }
         if(this.tempDef.diffMul > 0)
         {
            return diff0 * this.tempDef.diffMul;
         }
         return diff0;
      }
      
      public function getLifeMul(camp0:String) : Number
      {
         var v0:Number = 1;
         if(camp0 == BodyCamp.ENEMY)
         {
            v0 = this.tempDef.enemyLifeMul;
            if(v0 <= 0)
            {
               v0 = 1;
            }
         }
         return v0;
      }
      
      public function getDpsMul(camp0:String) : Number
      {
         var v0:Number = 1;
         if(camp0 == BodyCamp.ENEMY)
         {
            v0 = this.tempDef.enemyDpsMul;
            if(v0 <= 0)
            {
               v0 = 1;
            }
         }
         return v0;
      }
      
      public function downNowBossUpLevel() : void
      {
      }
      
      public function haveBossUpLevelB() : Boolean
      {
         return this.def.father == TaskType.KING;
      }
      
      public function haveDownBossUpLevelB() : Boolean
      {
         return this.getNowBossUpLevel() < 3;
      }
      
      public function getNowBossUpLevel() : int
      {
         return 3;
      }
      
      public function getSumStar() : Number
      {
         return this.save.sN;
      }
      
      public function getSumStarMust() : Number
      {
         if(this.def.father == TaskType.DAY)
         {
            return (this.getSaveDiff() + 1) * 4;
         }
         return (this.getSaveDiff() + 1) * 8;
      }
      
      public function getDirectGiftBtnTip() : String
      {
         var s0:String = "所需累计星级：" + ComMethod.mustColor(this.getSumStar(),this.getSumStarMust());
         return s0 + "\n\n<orange 每完成一次任务，就能累计难度星级。累计星级到达一定值，就可免做任务，直接领取任务奖励。/>";
      }
      
      public function getDirectGiftBtnActived() : Boolean
      {
         return this.getSumStar() >= this.getSumStarMust();
      }
      
      public function canRegetB() : Boolean
      {
         if(this.state == TaskState.over)
         {
            if(TaskType.regetArr.indexOf(this.def.father) >= 0)
            {
               return true;
            }
            if(this.def.name == "newYearBlessing")
            {
               return true;
            }
         }
         return false;
      }
      
      public function isOpenB(nowTimeDa0:StringDate) : Boolean
      {
         var timeB0:Boolean = this.def.openD.timePanB(nowTimeDa0);
         var taskB0:Boolean = this.def.openD.taskPanB(this.playerData.task);
         return taskB0 && timeB0 && (!this.def.openD.mustB || this.save.openB);
      }
      
      public function getCondition() : TaskConditionDefine
      {
         return this.tempDef.condition;
      }
      
      public function getGrowthUnlockLv() : int
      {
         return this.tempDef.unlockLv;
      }
      
      public function getGift() : GiftAddDefineGroup
      {
         var readTime0:StringDate = this.playerData.time.getReadTimeDate();
         var g0:GiftAddDefineGroup = this.tempDef.gift.getConverToTrue(this.getLv());
         g0.converByShowLv(this.getLv(),readTime0);
         if(this.def.isMainB())
         {
            if(TaskDefineGroup.mainAddGiftArr.indexOf(this.def.name) >= 0)
            {
               g0.addGiftByStr("things;allBlackCash;20");
               g0.addGiftByStr("things;allBlackEquipCash;20");
               g0.addGiftByStr("things;armsRadium;20");
               g0.addGiftByStr("things;armsTitanium;20");
            }
         }
         return g0;
      }
      
      public function getWorldMapId() : String
      {
         if(this.save.map != "")
         {
            return this.save.map;
         }
         return this.def.worldMapId;
      }
      
      public function getWorldMapDefine() : WorldMapDefine
      {
         return Gaming.defineGroup.worldMap.getDefine(this.getWorldMapId());
      }
      
      public function getLevelDefine() : LevelDefine
      {
         var d0:LevelDefine = null;
         var s0:WorldMapSave = null;
         if(this.mustFixedLevelB())
         {
            return TempLevel.getLevelDefine(this.def.fixedLevelUrl);
         }
         d0 = null;
         if(this.def.levelId == "")
         {
            s0 = this.playerData.worldMap.saveGroup.getSave(this.getWorldMapId());
            d0 = Gaming.defineGroup.level.getDefine(s0.getLevelName());
         }
         else
         {
            d0 = Gaming.defineGroup.level.getDefineUrl(this.def.levelId);
            if(!d0)
            {
               d0 = TempLevel.getLevelDefine(this.def.levelId);
            }
         }
         return d0;
      }
      
      public function mustFixedLevelB() : Boolean
      {
         return this.def.fixedLevelUrl != "";
      }
      
      public function getLv() : int
      {
         var d0:TaskDefine = null;
         var lv0:int = this.save.lv;
         if(lv0 > 0)
         {
            return lv0;
         }
         d0 = this.def;
         if(d0.tempLvB)
         {
            d0 = this.tempDef;
         }
         if(d0.lv > 0)
         {
            return d0.lv;
         }
         return d0.unlockLv;
      }
      
      public function getTaskBarTitleText() : String
      {
         var s0:String = ComMethod.color(this.getLv() + "","#00FF00") + "·";
         if(this.state == TaskState.over)
         {
            s0 += ComMethod.color(this.def.cnName,"#FF6600") + ComMethod.color("（已完成）","#FFFF00");
         }
         else
         {
            s0 += this.def.cnName;
            if(TaskType.diffTipArr.indexOf(this.def.father) >= 0)
            {
               if(this.def.haveGrowthB)
               {
                  s0 += ComMethod.color(" [可选" + this.def.getGrowthNum() + "星]",GatherColor.gray2Color);
               }
            }
         }
         return s0;
      }
      
      public function getCanCompleteNum() : int
      {
         if(this.def.completeLimitNum == -1)
         {
            return -1;
         }
         if(this.def.completeLimitNum == 1)
         {
            return this.state == TaskState.no ? 1 : 0;
         }
         return this.def.completeLimitNum - this.save.cN;
      }
      
      public function getTaskBoxText() : String
      {
         var str0:String = this.def.getTaskBoxText(this.state);
         var worldMap_d0:WorldMapDefine = this.getWorldMapDefine();
         if(worldMap_d0 is WorldMapDefine)
         {
            str0 = str0.replace("[map]",ComMethod.color(worldMap_d0.cnName,"#00FF00"));
         }
         if(this.def.conditionText != "")
         {
            str0 += "\n" + TaskCondtionText.getText(this,false);
         }
         return str0;
      }
      
      public function getTask() : void
      {
         this.save.state = TaskState.ing;
         this.save.clearNum();
         this.uiFleshB = false;
         Gaming.soundGroup.playSound("uiSound","getTask");
         Gaming.TG.task.dataChange();
         Gaming.TG.task.getTask(this);
         this.ingDataChange();
      }
      
      public function complete(fleshIngArrB0:Boolean = true) : void
      {
         var xx0:int = 0;
         if(this.save.state == TaskState.ing)
         {
            this.save.state = TaskState.complete;
            this.uiFleshB = false;
            Gaming.soundGroup.playSound("uiSound","success");
            Gaming.TG.task.complete(this);
            if(fleshIngArrB0)
            {
               this.ingDataChange();
            }
         }
         else
         {
            xx0 = 0;
         }
      }
      
      public function completeDiff(diff0:int) : void
      {
         this.setDiff(diff0);
         this.complete();
      }
      
      public function fail() : void
      {
         this.save.state = TaskState.fail;
         this.uiFleshB = false;
         Gaming.soundGroup.playSound("uiSound","fail");
         Gaming.TG.task.fail(this);
         this.ingDataChange();
      }
      
      public function getTaskGift() : TaskData
      {
         var gift0:GiftAddDefineGroup = null;
         if(this.def.name == "weekMadboss")
         {
            gift0 = this.getGift();
            ++this.playerData.task.saveGroup.madN;
            this.playerData.task.saveGroup.madG += gift0.getAllNum();
         }
         this.playerData.task.addTodayNum(this.def.father,1);
         var canCompleteB0:Boolean = false;
         if(this.def.completeLimitNum > 1)
         {
            ++this.save.cN;
            canCompleteB0 = this.def.completeLimitNum - this.save.cN > 0;
         }
         else if(this.def.completeLimitNum == 1)
         {
            canCompleteB0 = false;
         }
         else if(this.def.completeLimitNum == -1)
         {
            canCompleteB0 = true;
         }
         if(this.def.isAutoUnlockNextB() == false)
         {
            ++this.save.aN;
            this.save.sN += this.getSaveDiff() + 1;
         }
         if(canCompleteB0)
         {
            this.rebirth(false);
         }
         else
         {
            this.save.state = TaskState.over;
         }
         if(this.def.openD.closeAffterCompleteB)
         {
            this.save.openB = false;
         }
         Gaming.TG.task.dataChange();
         this.ingDataChange();
         if(TaskType.autoUnlockArr.indexOf(this.def.father) >= 0)
         {
            return this.playerData.task.unlockOneByType(this.def.father);
         }
         return null;
      }
      
      public function giveup() : void
      {
         this.save.state = TaskState.no;
         this.save.clearNum();
         Gaming.soundGroup.playSound("uiSound","giveupTask");
         Gaming.TG.task.dataChange();
         this.ingDataChange();
         Gaming.TG.task.giveUp(this);
      }
      
      public function rebirth(excludeMeMapB:Boolean) : Boolean
      {
         var mapId0:String = this.save.map;
         this.save.state = TaskState.no;
         this.save.clearNum();
         if(this.mustFixedLevelB() && this.def.worldMapType != "")
         {
            TempLevel.delLevelDefineByName(this.def.fixedLevelUrl);
         }
         if(mapId0 != "" && excludeMeMapB)
         {
            TaskDeal.addExcludeMap(mapId0);
         }
         var bb0:Boolean = this.dealOne();
         if(bb0)
         {
            this.fixedLevelByMust();
         }
         return bb0;
      }
      
      public function clearOverState() : void
      {
         if(this.state == TaskState.over)
         {
            this.save.state = TaskState.no;
            this.save.clearNum();
         }
      }
      
      private function ingDataChange() : void
      {
         this.playerData.task.fleshIngArr();
      }
      
      public function rolePan() : Boolean
      {
         if(Boolean(this.playerData))
         {
            return this.def.rolePan(this.playerData.getRoleName());
         }
         return false;
      }
      
      public function getMustUnlockDefArr() : Array
      {
         var name0:* = null;
         var d0:TaskDefine = null;
         var darr0:Array = [];
         for each(name0 in this.def.openD.tk)
         {
            d0 = Gaming.defineGroup.task.getOneDefine(name0);
            if(Boolean(d0))
            {
               darr0.push(d0);
            }
         }
         return darr0;
      }
      
      public function getMustUnlockCnStr() : String
      {
         var cnArr0:Array = null;
         var darr0:Array = null;
         var d0:TaskDefine = null;
         if(this.def.openD.tk.length == 0)
         {
            return "";
         }
         cnArr0 = [];
         darr0 = this.getMustUnlockDefArr();
         for each(d0 in darr0)
         {
            if(d0.rolePan(this.playerData.getRoleName()))
            {
               cnArr0.push(d0.getAlertTitleText());
            }
         }
         return StringMethod.concatStringArr(cnArr0,4," 或 ");
      }
   }
}

