package dataAll.items.save
{
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   
   public class ItemsSaveGroup
   {
      public static var pro_arr:Array = [];
      
      public var arr:Array = [];
      
      public var lockLen:int = 0;
      
      public var lockObj:Object = {};
      
      public var lastId:Number = 0;
      
      private var _gripMaxNum:int = 0;
      
      public function ItemsSaveGroup()
      {
         super();
      }
      
      public function inData_byObjAndClass(obj0:Object, childClassOrFun0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.arr = ClassProperty.copySaveArray(obj0["arr"],childClassOrFun0);
         this.lockObj = ClassProperty.copyObj(obj0["lockObj"]);
      }
      
      public function replaceSave(m0:ItemsSave, new0:ItemsSave) : Boolean
      {
         var i0:int = int(this.arr.indexOf(m0));
         if(i0 >= 0)
         {
            this.arr[i0] = new0;
            return true;
         }
         return false;
      }
      
      public function addSaveByOther(s0:ItemsSave) : void
      {
         s0.site = this.arr.length;
         this.arr.push(s0);
      }
      
      public function get gripMaxNum() : int
      {
         return this._gripMaxNum;
      }
      
      public function set gripMaxNum(value0:int) : void
      {
         this._gripMaxNum = value0;
      }
      
      public function getCanFillLen() : int
      {
         var n:* = undefined;
         var num0:int = 0;
         for(n in this.lockObj)
         {
            if(this.lockObj[n] == 1 && int(n) >= this.lockLen)
            {
               num0++;
            }
         }
         return this.lockLen + num0;
      }
      
      public function getArrBySite() : Array
      {
         var arr0:Array = this.arr.concat();
         ArrayMethod.sortArrByProName(arr0,"site");
         return arr0;
      }
      
      public function getUnlockSurplus() : int
      {
         return this.gripMaxNum - this.lockLen;
      }
      
      public function unlockAll() : void
      {
         this.unlockTo(this.gripMaxNum);
      }
      
      public function unlockTo(end0:int) : void
      {
         if(end0 > this.gripMaxNum - 1)
         {
            end0 = this.gripMaxNum - 1;
         }
         this.lockLen = end0 + 1;
      }
      
      public function unlockNewOne() : void
      {
         this.unlockTo(this.lockLen);
      }
      
      public function getUnlockBySite(site0:int) : Boolean
      {
         if(site0 <= this.lockLen - 1)
         {
            return true;
         }
         return this.lockObj[site0] == 1;
      }
      
      public function unlockSite(site0:int) : void
      {
         if(site0 <= this.gripMaxNum - 1)
         {
            this.lockObj[site0] = 1;
         }
      }
      
      public function unlockNextSite() : int
      {
         var max0:int = this.gripMaxNum;
         for(var i:int = 0; i < max0; i++)
         {
            if(!this.getUnlockBySite(i))
            {
               this.unlockSite(i);
               return i;
            }
         }
         return -1;
      }
      
      public function unlockNextSiteNum(num0:int) : void
      {
         this.unlockTo(this.getCanFillLen() + num0 - 1);
      }
      
      public function sort() : void
      {
         var n:* = undefined;
         var s0:ItemsSave = null;
         var arr0:Array = this.arr.concat([]);
         arr0.sort(this.sortFun);
         for(n in arr0)
         {
            s0 = arr0[n];
            s0.site = n;
         }
      }
      
      private function sortFun(a0:ItemsSave, b0:ItemsSave) : int
      {
         if(a0.id < b0.id)
         {
            return -1;
         }
         if(a0.id > b0.id)
         {
            return 1;
         }
         return 0;
      }
      
      public function replaceItemsName(before0:String, after0:String) : void
      {
         var s0:ItemsSave = null;
         for each(s0 in this.arr)
         {
            if(s0.name == before0)
            {
               s0.name = after0;
            }
         }
      }
      
      public function zuobiPan() : String
      {
         return "";
      }
   }
}

