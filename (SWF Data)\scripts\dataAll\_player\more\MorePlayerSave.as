package dataAll._player.more
{
   public class MorePlayerSave extends NormalPlayerSave
   {
      public static var pro_arr:Array = [];
      
      public function MorePlayerSave()
      {
         super();
         inputNoDataNameArr = pro_arr.concat([]);
      }
      
      override public function initSave() : void
      {
         super.initSave();
      }
      
      override protected function initGripMaxNum() : void
      {
         super.initGripMaxNum();
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         var arr0:Array = pro_arr;
         _inData_byObj(obj0,arr0);
      }
   }
}

