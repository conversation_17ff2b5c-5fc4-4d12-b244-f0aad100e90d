package dataAll._app.union.info
{
   import com.adobe.serialization.json.JSON2;
   
   public class OwnUnionInfo
   {
      public var unionInfo:UnionInfo = new UnionInfo();
      
      public var member:MemberInfo = new MemberInfo();
      
      public function OwnUnionInfo()
      {
         super();
      }
      
      public static function getSimulated() : OwnUnionInfo
      {
         var i0:OwnUnionInfo = new OwnUnionInfo();
         i0.unionInfo = UnionInfo.getSimulated(1);
         i0.member = MemberInfo.getSimulated(1);
         return i0;
      }
      
      public static function getSimulatedJson() : String
      {
         var i0:OwnUnionInfo = getSimulated();
         return JSON2.encode(i0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         this.unionInfo.inData_byObj(obj0["unionInfo"]);
         this.member.inData_byObj(obj0["member"]);
      }
      
      public function inData_byJson(jsonStr0:String) : void
      {
         var obj0:Object = JSON2.decode(jsonStr0);
         this.inData_byObj(obj0);
      }
   }
}

