package dataAll.image
{
   import dataAll._app.edit.TorData;
   import dataAll._app.edit._def.EditProDefine;
   import dataAll._app.edit.arms.ArmsTorData;
   import dataAll._app.edit.list.EditListAgent;
   import dataAll._base.IO_Define;
   import dataAll._base.IO_NormalDefine;
   import dataAll._base.NormalDefineGroup;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.bullet.BulletDefine;
   import dataAll.skill.define.SkillDefine;
   import flash.utils.getTimer;
   
   public class ImageUrlGroup extends NormalDefineGroup
   {
      private var xmlArrObj:Object = {};
      
      private var sameNumObj:Object = {};
      
      private var editAgentObj:Object = {};
      
      public function ImageUrlGroup()
      {
         super();
         defineClass = ImageUrlDefine;
         fatherNameArr = [];
         fatherCnArr = [];
      }
      
      override protected function defineInXml(d0:IO_NormalDefine, xml0:XML, father0:String, gather0:String) : void
      {
         var d2:ImageUrlDefine = d0 as ImageUrlDefine;
         d2.inDefineXml(xml0,father0);
      }
      
      public function getDefine(name0:String) : ImageUrlDefine
      {
         return obj[name0];
      }
      
      private function getFirstCodeObj() : Object
      {
         var d0:ImageUrlDefine = null;
         var obj0:Object = {};
         for each(d0 in arr)
         {
            obj0[d0.getUnicode()] = d0;
         }
         return obj0;
      }
      
      public function dealBulletAndSkill() : void
      {
         var range0:ArmsRangeDefine = null;
         var bulletArr0:Array = null;
         var bu0:BulletDefine = null;
         var skillArr0:Array = null;
         var skill0:SkillDefine = null;
         var tt0:Number = getTimer();
         var codeObj0:Object = this.getFirstCodeObj();
         var rangeArr0:Array = Gaming.defineGroup.bullet.getRangeArr();
         for each(range0 in rangeArr0)
         {
            this.idBullet(range0.def,codeObj0,true);
         }
         bulletArr0 = Gaming.defineGroup.bullet.getBulletArr();
         for each(bu0 in bulletArr0)
         {
            this.idBullet(bu0,codeObj0);
         }
         skillArr0 = Gaming.defineGroup.skill.getFromXmlArr();
         for each(skill0 in skillArr0)
         {
            this.idSkill(skill0,codeObj0);
         }
         trace("dealBulletAndSkill耗时：" + (getTimer() - tt0));
      }
      
      private function idBullet(d0:BulletDefine, codeObj0:Object, noErrorB0:Boolean = false) : void
      {
         var n:* = undefined;
         var posName0:String = null;
         var posId0:String = null;
         var posCn0:String = null;
         var img0:ImageUrlDefine = null;
         var imgNameArr0:Array = BulletDefine.imgNameArr;
         for(n in imgNameArr0)
         {
            posName0 = imgNameArr0[n];
            posId0 = ImageUrlFather.bulletArr[n];
            posCn0 = ImageUrlFather.bulletCnArr[n];
            img0 = d0[posName0];
            this.idImgUrlDefine(img0,codeObj0,d0,posId0,posCn0,noErrorB0);
         }
      }
      
      private function idSkill(d0:SkillDefine, codeObj0:Object, noErrorB0:Boolean = false) : void
      {
         var n:* = undefined;
         var posName0:String = null;
         var posId0:String = null;
         var posCn0:String = null;
         var img0:ImageUrlDefine = null;
         var imgNameArr0:Array = SkillDefine.imgNameArr;
         for(n in imgNameArr0)
         {
            posName0 = imgNameArr0[n];
            posId0 = ImageUrlFather.skillArr[n];
            posCn0 = ImageUrlFather.skillCnArr[n];
            img0 = d0[posName0];
            this.idImgUrlDefine(img0,codeObj0,d0,posId0,posCn0,noErrorB0);
         }
      }
      
      private function idImgUrlDefine(d0:ImageUrlDefine, codeObj0:Object, fatherD0:IO_Define, posId0:String, posCn0:String, noErrorB0:Boolean = false) : void
      {
         var code0:String = null;
         var idD0:ImageUrlDefine = null;
         var df:int = 0;
         Gaming.defineGroup.sound.idImgUrlDefine(d0,fatherD0,posId0,posCn0);
         if(d0.name == "")
         {
            code0 = d0.getUnicode();
            if(codeObj0.hasOwnProperty(code0))
            {
               idD0 = codeObj0[code0];
               d0.setIdName(idD0.name);
            }
            else
            {
               if(noErrorB0)
               {
                  INIT.showError("不存在ImageUrlDefine：" + fatherD0 + "-" + posCn0 + "  " + code0);
               }
               df = 0;
            }
         }
      }
      
      private function traceFatherNum() : void
      {
         var f0:* = null;
         var arr0:Array = null;
         var num0:int = 0;
         var farr0:Array = ImageUrlFather.bulletArr.concat(ImageUrlFather.skillArr);
         for each(f0 in farr0)
         {
            arr0 = getArrByFather(f0);
            num0 = 0;
            if(Boolean(arr0))
            {
               num0 = int(arr0.length);
            }
            INIT.TRACE(f0 + ":" + num0);
         }
      }
      
      private function traceSameNameNum() : void
      {
         var n:* = undefined;
         var num0:int = 0;
         var d0:ImageUrlDefine = null;
         INIT.TRACE("-------------------------同名统计");
         for(n in this.sameNumObj)
         {
            num0 = int(this.sameNumObj[n]);
            if(num0 >= 10)
            {
               d0 = this.getDefine(n);
               INIT.TRACE(d0.url + "    " + d0.cnName + "   " + num0);
            }
         }
      }
      
      private function outXml() : void
      {
         var father0:* = undefined;
         var arr0:Array = null;
         var fatherCn0:String = null;
         var fatherXml0:XML = null;
         var index0:int = 0;
         var xml0:XML = null;
         var s0:String = "";
         for(father0 in this.xmlArrObj)
         {
            arr0 = this.xmlArrObj[father0];
            fatherCn0 = ImageUrlFather.getCn(father0);
            fatherXml0 = <father/>;
            fatherXml0.@name = father0;
            fatherXml0.@cnName = fatherCn0;
            fatherXml0.@num = arr0.length;
            arr0.sort(this.sortOutXmlFun);
            index0 = 0;
            for each(xml0 in arr0)
            {
               fatherXml0.appendChild(xml0);
               index0++;
            }
            s0 += "\n" + fatherXml0.toXMLString();
            s0 += "\n";
         }
         INIT.TRACE(s0);
         INIT.TRACE("\n\n");
      }
      
      private function sortOutXmlFun(a0:XML, b0:XML) : int
      {
         var a2:String = String(a0);
         var b2:String = String(b0);
         if(a2 > b2)
         {
            return 1;
         }
         if(a2 < b2)
         {
            return -1;
         }
         return 0;
      }
      
      public function getEditAgent(proD0:EditProDefine, nowD0:ImageUrlDefine, da0:TorData) : EditListAgent
      {
         var childFather0:String = null;
         var armsTor0:ArmsTorData = da0 as ArmsTorData;
         var type0:String = "skill";
         var firstTitle0:String = "";
         if(Boolean(armsTor0))
         {
            if(armsTor0.getArmsDef().isLongLine() && proD0.name == "bulletImg")
            {
               type0 = "line";
               firstTitle0 = ImageUrlFather.bulletLine;
            }
            else
            {
               childFather0 = proD0.name.replace("Img","");
               firstTitle0 = ImageUrlFather.getFather(nowD0,childFather0);
               type0 = "arms";
            }
         }
         return this.getEditListAgent(nowD0,type0,firstTitle0);
      }
      
      private function getEditListAgent(nowD0:ImageUrlDefine, type0:String, firstTitle0:String) : EditListAgent
      {
         var fatherArr0:Array = null;
         var cnArr0:Array = null;
         var father0:* = null;
         var arr0:Array = null;
         var d0:ImageUrlDefine = null;
         var a0:EditListAgent = this.editAgentObj[type0];
         if(Boolean(a0))
         {
            a0.clearFun();
         }
         else
         {
            a0 = new EditListAgent();
            fatherArr0 = [];
            if(type0 == "arms")
            {
               fatherArr0 = ImageUrlFather.BULLET_EDIT;
            }
            else if(type0 == "line")
            {
               fatherArr0 = ImageUrlFather.BULLET_LINE_EDIT;
            }
            else if(type0 == "skill")
            {
            }
            cnArr0 = ImageUrlFather.getCnArr(fatherArr0);
            a0.inTitle(fatherArr0,cnArr0);
            for each(father0 in fatherArr0)
            {
               arr0 = getArrByFather(father0);
               for each(d0 in arr0)
               {
                  a0.addDataLast(d0,father0);
               }
            }
            a0.cutPan();
            this.editAgentObj[type0] = a0;
         }
         a0.setFirstTitle(firstTitle0);
         a0.setNoLinkArr([nowD0.name]);
         a0.createAllText();
         return a0;
      }
   }
}

