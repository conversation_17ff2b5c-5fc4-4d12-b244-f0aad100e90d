package dataAll._player.count.props
{
   public class DpsCountData
   {
      private static const gap:int = 5;
      
      public var allColor:String = "FF6600";
      
      private var arr:Array = [];
      
      private var averageArr:Array = [];
      
      private var cumuValue:Number = 0;
      
      private var allValue:Number = 0;
      
      private var allT:Number = 0;
      
      public function DpsCountData()
      {
         super();
      }
      
      public static function getAveInArr(arr0:Array, index0:int) : Number
      {
         var first0:int = index0 - gap;
         if(first0 < 0)
         {
            first0 = 0;
         }
         var num0:int = 0;
         var all0:Number = 0;
         for(var i:int = first0; i <= index0; i++)
         {
            all0 += arr0[i];
            num0++;
         }
         return all0 / num0;
      }
      
      public function haveDataB() : Boolean
      {
         return this.arr.length > 0;
      }
      
      private function addData(v0:Number) : void
      {
         this.arr.push(v0);
         this.averageArr.push(this.getAverageValue());
      }
      
      public function addValue(v0:Number) : void
      {
         if(v0 < 0)
         {
            this.cumuValue = v0;
         }
         else
         {
            if(this.cumuValue < 0)
            {
               this.cumuValue = 0;
            }
            this.cumuValue += v0;
            this.allValue += v0;
         }
      }
      
      public function getAverage() : Number
      {
         if(this.averageArr.length > 0)
         {
            return getAveInArr(this.averageArr,this.averageArr.length - 1);
         }
         return 0;
      }
      
      private function getAverageValue() : Number
      {
         var all0:Number = this.allValue;
         var num0:Number = this.allT;
         if(num0 == 0)
         {
            return 0;
         }
         return all0 / num0;
      }
      
      public function getNow() : Number
      {
         if(this.arr.length > 0)
         {
            return getAveInArr(this.arr,this.arr.length - 1);
         }
         return 0;
      }
      
      public function getImageDataArr(valueB0:Boolean, arrangeB0:Boolean) : Array
      {
         var da1:DpsImageData = null;
         var da2:DpsImageData = null;
         var arr0:Array = [];
         if(valueB0)
         {
            da1 = new DpsImageData();
            da1.value = this.getNow();
            da1.color = this.allColor;
            da1.alpha = 0.5;
            da1.valueArr = this.arr;
            arr0.push(da1);
         }
         if(arrangeB0)
         {
            da2 = new DpsImageData();
            da2.value = this.getAverage();
            da2.color = this.allColor;
            da2.valueArr = this.averageArr;
            arr0.push(da2);
         }
         return arr0;
      }
      
      public function FTimerSecond() : void
      {
         this.addData(this.cumuValue);
         if(this.arr.length > 0)
         {
            ++this.allT;
            this.cumuValue = 0;
         }
      }
   }
}

