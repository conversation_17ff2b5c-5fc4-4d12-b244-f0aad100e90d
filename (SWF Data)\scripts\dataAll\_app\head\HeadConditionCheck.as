package dataAll._app.head
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ObjectMethod;
   import dataAll._app.achieve.define.AchieveDefine;
   import dataAll._app.head.define.HeadConditionDefine;
   import dataAll._app.head.define.HeadDefine;
   import dataAll._app.task.define.TaskType;
   import dataAll._app.worldMap.save.WorldMapSave;
   import dataAll._player.PlayerData;
   import dataAll._player.count.HeadCountSave;
   import dataAll.arms.ArmsData;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsType;
   import dataAll.arms.skin.ArmsSkinDefine;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.vehicle.VehicleData;
   import dataAll.equip.vehicle.VehicleDefine;
   import dataAll.pet.PetData;
   import dataAll.pet.gene.define.GeneDefine;
   
   public class HeadConditionCheck
   {
      public function HeadConditionCheck()
      {
         super();
      }
      
      private static function get PD() : PlayerData
      {
         return Gaming.PG.da;
      }
      
      public static function check(d0:HeadDefine, heroLv0:int, dealProgressB0:Boolean = true) : HeadTempData
      {
         var c0:HeadConditionDefine = null;
         var fun0:Function = null;
         if(d0.unlockLv <= heroLv0)
         {
            c0 = d0.condition;
            fun0 = HeadConditionCheck[c0.fun];
            if(!(fun0 is Function))
            {
               fun0 = HeadConditionCheckEquip[c0.fun];
            }
            if(fun0 is Function)
            {
               return fun0(d0,dealProgressB0);
            }
         }
         return new HeadTempData();
      }
      
      private static function num(d0:HeadDefine, dealProgressB0:Boolean) : HeadTempData
      {
         var r0:HeadTempData = new HeadTempData();
         var c0:HeadConditionDefine = d0.condition;
         var count0:HeadCountSave = Gaming.PG.save.headCount;
         var fun0:Function = HeadConditionCheck[c0.pro];
         var value0:Number = 0;
         if(fun0 is Function)
         {
            value0 = fun0();
         }
         else
         {
            value0 = count0.getValue(c0.pro);
         }
         r0.inNumCompare(value0,d0);
         return r0;
      }
      
      private static function getUnionContribution() : Number
      {
         return Gaming.PG.da.union.getContribution();
      }
      
      private static function getHeroSkillNum() : int
      {
         var pd0:PlayerData = Gaming.PG.da;
         return pd0.skill.dataArr.length + pd0.skillBag.dataArr.length;
      }
      
      private static function getWilderDiff4Num() : int
      {
         var pd0:PlayerData = Gaming.PG.da;
         return pd0.wilder.getDiffWinNum(4);
      }
      
      private static function getActive20_5() : int
      {
         return 0;
      }
      
      private static function getActive90Day() : int
      {
         return 0;
      }
      
      private static function getActive20_3() : int
      {
         return 0;
      }
      
      private static function getAchieveNum() : int
      {
         return PD.achieve.getCompleteNum();
      }
      
      private static function getZodiacNum() : int
      {
         var da0:ArmsData = null;
         var d0:ArmsDefine = null;
         var nameObj0:Object = {};
         var num0:int = 0;
         var arr0:Array = PD.getArmsDataArr(true,true,true,true);
         for each(da0 in arr0)
         {
            d0 = da0.def;
            if(d0.isZodiacB())
            {
               if(!nameObj0.hasOwnProperty(d0.name))
               {
                  nameObj0[d0.name] = 1;
                  num0++;
               }
            }
         }
         return num0;
      }
      
      private static function getMainTaskCompleteNum() : int
      {
         return PD.task.getOverTaskNum(TaskType.MAIN);
      }
      
      private static function getMaxDps_rocket() : Number
      {
         return PD.arms.getMaxDpsByType(ArmsType.rocket);
      }
      
      private static function getMaxDps_pistol() : Number
      {
         return PD.arms.getMaxDpsByType(ArmsType.pistol);
      }
      
      private static function getMaxDps_shotgun() : Number
      {
         return PD.arms.getMaxDpsByType(ArmsType.shotgun);
      }
      
      private static function getMaxDps_sniper() : Number
      {
         return PD.arms.getMaxDpsByType(ArmsType.sniper);
      }
      
      private static function getMaxDps_rifle() : Number
      {
         return PD.arms.getMaxDpsByType(ArmsType.rifle);
      }
      
      private static function get10YiArmsTypeNum() : Number
      {
         var da0:ArmsData = null;
         var type0:String = null;
         var dps0:Number = NaN;
         var arr0:Array = PD.armsBag.dataArr;
         var typeObj0:Object = {};
         for each(da0 in arr0)
         {
            type0 = da0.armsType;
            dps0 = da0.getUIShowDps();
            if(dps0 >= 500000000)
            {
               if(typeObj0.hasOwnProperty(type0) == false)
               {
                  typeObj0[type0] = dps0;
               }
            }
         }
         return ObjectMethod.getObjElementNum(typeObj0);
      }
      
      private static function getArenaNum() : Number
      {
         return PD.arena.save.allNum + PD.arena.save.challengeNum;
      }
      
      private static function getDps() : Number
      {
         return PD.getDps();
      }
      
      private static function getEvoVehicleNum() : int
      {
         var da0:EquipData = null;
         var v0:VehicleData = null;
         var beforeD0:VehicleDefine = null;
         var num0:int = 0;
         var dataArr:Array = Gaming.PG.da.getEquipDataArr(true,true,true);
         for each(da0 in dataArr)
         {
            if(da0.save.partType == EquipType.VEHICLE)
            {
               v0 = da0 as VehicleData;
               beforeD0 = Gaming.defineGroup.vehicle.getBeforeDefine(v0.save.imgName);
               if(Boolean(beforeD0))
               {
                  num0++;
               }
            }
         }
         return num0;
      }
      
      private static function getEvoPetNum() : int
      {
         var da0:PetData = null;
         var d0:GeneDefine = null;
         var num0:int = 0;
         var dataArr:Array = Gaming.PG.da.pet.arr;
         for each(da0 in dataArr)
         {
            d0 = da0.getGeneDefine();
            if(d0.lv >= 2)
            {
               num0++;
            }
         }
         return num0;
      }
      
      private static function getGoddiffNum() : int
      {
         var s0:WorldMapSave = null;
         var obj0:Object = Gaming.PG.save.worldMap.obj;
         var num0:int = 0;
         for each(s0 in obj0)
         {
            if(s0.diffUnlock >= 4)
            {
               num0++;
            }
         }
         return num0;
      }
      
      private static function armsSkinCreator(d0:HeadDefine, dealProgressB0:Boolean) : HeadTempData
      {
         var r0:HeadTempData = new HeadTempData();
         r0.completeB = ArmsSkinDefine.UID_ARR.indexOf(Gaming.getUid()) >= 0;
         return r0;
      }
      
      private static function death(d0:HeadDefine, dealProgressB0:Boolean) : HeadTempData
      {
         var name0:* = null;
         var ad0:AchieveDefine = null;
         var bb0:Boolean = false;
         var r0:HeadTempData = new HeadTempData();
         var nameArr0:Array = ["dieNum_7","dieBelow_3"];
         var completeB0:Boolean = true;
         var n0:int = 0;
         for each(name0 in nameArr0)
         {
            ad0 = Gaming.defineGroup.achieve.getDefine(name0);
            bb0 = Gaming.PG.da.achieve.isCompleteB(name0);
            if(!bb0)
            {
               completeB0 = false;
            }
            if(dealProgressB0)
            {
               r0.compareStr += (n0 > 0 ? "、" : "") + (bb0 ? ComMethod.color(ad0.cnName + "√","#00FF00") : ComMethod.color(ad0.cnName + "×","#FF0000"));
            }
            n0++;
         }
         r0.completeB = completeB0;
         return r0;
      }
   }
}

