package dataAll._app.wilder
{
   import com.sounto.oldUtils.StringDate;
   import dataAll._app.wilder.define.WilderDefine;
   import dataAll._app.wilder.define.WilderFatherDefine;
   import dataAll._player.PlayerData;
   import gameAll.drop.BodyDropCtrl;
   
   public class WilderDataGroup
   {
      public var playerData:PlayerData;
      
      public var saveGroup:WilderSaveGroup;
      
      public var obj:Object = {};
      
      public function WilderDataGroup()
      {
         super();
      }
      
      public function inData_bySaveGroup(sg0:WilderSaveGroup) : void
      {
         var s0:WilderSave = null;
         var da0:WilderData = null;
         this.saveGroup = sg0;
         this.obj = {};
         for each(s0 in sg0.obj)
         {
            da0 = new WilderData();
            da0.inData_bySave(s0,this.playerData);
            this.obj[da0.name] = da0;
         }
      }
      
      public function supple(timeStr0:String) : void
      {
         var d0:WilderDefine = null;
         var da0:WilderData = null;
         var s0:WilderSave = null;
         var obj0:Object = Gaming.defineGroup.wilder.obj;
         for each(d0 in obj0)
         {
            da0 = this.getData(d0.name);
            if(!this.obj.hasOwnProperty(d0.name))
            {
               s0 = new WilderSave();
               da0 = new WilderData();
               s0.inData_byDefine(d0);
               da0.inData_bySave(s0,this.playerData);
               this.obj[d0.name] = da0;
               da0.newDayCtrl(timeStr0,0,true);
            }
            else
            {
               da0.supple(timeStr0);
            }
         }
         this.fleshSaveGroup();
      }
      
      protected function fleshSaveGroup() : void
      {
         var da0:WilderData = null;
         var obj0:Object = {};
         for each(da0 in this.obj)
         {
            obj0[da0.name] = da0.save;
         }
         this.saveGroup.obj = obj0;
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         var da0:WilderData = null;
         var dayGap0:int = this.playerData.time.getDayGap();
         var now0:StringDate = new StringDate(timeStr0);
         var monday0:StringDate = now0.getWeekData(1);
         var mondayGap0:int = now0.reductionOne(monday0);
         var overWeekB0:Boolean = false;
         if(dayGap0 > mondayGap0 || mondayGap0 == 0)
         {
            dayGap0 = mondayGap0;
            overWeekB0 = true;
         }
         for each(da0 in this.obj)
         {
            da0.newDayCtrl(timeStr0,dayGap0,overWeekB0);
         }
         this.saveGroup.todayClickB = false;
         this.saveGroup.lootB = false;
      }
      
      public function openUI() : String
      {
         var add0:int = 0;
         var day0:int = 0;
         var sd0:StringDate = this.playerData.time.getReadTimeDate();
         this.supple(sd0.getStr());
         if(!this.saveGroup.lootB && this.saveGroup.keyNum <= 100)
         {
            add0 = 2;
            day0 = sd0.getDateClass().day;
            if(day0 == 6 || day0 == 0)
            {
               add0 = 6;
            }
            if(this.playerData.time.isNowActiveB())
            {
               add0 += BodyDropCtrl.getActiveAdd_wilderKey();
            }
            this.saveGroup.lootB = true;
            this.addKey(add0);
            return "今天成功获得" + add0 + "把秘境钥匙。";
         }
         return "";
      }
      
      public function unlockAllWider() : void
      {
         var da0:WilderData = null;
         Gaming.defineGroup.wilder.unlockAllWider();
         for each(da0 in this.obj)
         {
            da0.setTimeState(0);
         }
      }
      
      public function getData(name0:String) : WilderData
      {
         return this.obj[name0];
      }
      
      public function getDataArrByFather(father0:String, showHideB0:Boolean = true) : Array
      {
         var d0:WilderDefine = null;
         var da0:WilderData = null;
         var dataArr0:Array = [];
         var f0:WilderFatherDefine = Gaming.defineGroup.wilder.getFather(father0);
         for each(d0 in f0.wilderArr)
         {
            da0 = this.getData(d0.name);
            if(Boolean(da0) && (showHideB0 || !d0.hideB))
            {
               dataArr0.push(da0);
            }
         }
         return dataArr0;
      }
      
      public function getDayNum() : int
      {
         var da0:WilderData = null;
         var num0:int = 0;
         for each(da0 in this.obj)
         {
            num0 += da0.save.num;
         }
         return num0;
      }
      
      public function getDayExchangeNum() : int
      {
         var da0:WilderData = null;
         var num0:int = 0;
         for each(da0 in this.obj)
         {
            num0 += da0.save.exchangeNum;
         }
         return num0;
      }
      
      public function getWilderAllNum(name0:String) : Number
      {
         var da0:WilderData = this.getData(name0);
         if(Boolean(da0))
         {
            return da0.save.all;
         }
         return 0;
      }
      
      public function getSurplusLootNum() : int
      {
         if(this.saveGroup.lootB)
         {
            return 0;
         }
         return 1;
      }
      
      public function exchangeNum(da0:WilderData) : void
      {
         if(this.saveGroup.keyNum > 0)
         {
            --this.saveGroup.keyNum;
            da0.addExchangeNum(1);
         }
      }
      
      public function getLootTime(timeStr0:String) : StringDate
      {
         var da0:StringDate = new StringDate(timeStr0);
         da0.hours = 19;
         da0.minutes = 0;
         da0.seconds = 0;
         return da0;
      }
      
      public function getLootLong() : Number
      {
         return 600000;
      }
      
      public function addKeyNum() : void
      {
         this.addKey(1);
         this.saveGroup.lootB = true;
      }
      
      public function addKey(num0:int) : void
      {
         this.saveGroup.keyNum += num0;
      }
      
      public function getDiffWinNum(diff0:int) : int
      {
         var da0:WilderData = null;
         var num0:int = 0;
         for each(da0 in this.obj)
         {
            if(da0.save.d >= diff0)
            {
               num0++;
            }
         }
         return num0;
      }
      
      public function getWinAll() : Number
      {
         var da0:WilderData = null;
         var num0:Number = 0;
         for each(da0 in this.obj)
         {
            num0 += da0.save.all;
         }
         return num0;
      }
      
      public function getMostWiderStr() : String
      {
         var da0:WilderData = null;
         var num0:Number = NaN;
         var most0:Number = 0;
         var cn0:String = "";
         for each(da0 in this.obj)
         {
            num0 = da0.save.all;
            if(num0 > most0)
            {
               most0 = num0;
               cn0 = da0.def.cnName;
            }
         }
         if(most0 > 0)
         {
            return cn0 + " " + most0 + "次";
         }
         return "无";
      }
      
      public function zuobiPan() : String
      {
         var da0:WilderData = null;
         for each(da0 in this.obj)
         {
            if(da0.getCanChallangeNum() < -3)
            {
               return da0.def.cnName + "秘境可挑战次数：" + da0.getCanChallangeNum();
            }
         }
         return "";
      }
   }
}

