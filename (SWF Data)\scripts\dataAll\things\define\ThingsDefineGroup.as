package dataAll.things.define
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import dataAll._app.parts.define.PartsConst;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipFatherDefine;
   import dataAll.items.find.IO_CnNameFinder;
   
   public class ThingsDefineGroup implements IO_CnNameFinder
   {
      public var obj:Object = {};
      
      public var cnObj:Object = {};
      
      public var fatherObj:Object = {};
      
      public var fatherArrObj:Object = {};
      
      public var smeltObj:Object = {};
      
      private var smeltCnObj:Object = {};
      
      private var smeltGatherTip:String = "";
      
      public var composeArr:Array = [];
      
      public var composeObj:Object = {};
      
      private var findCnObj:Object = {};
      
      public var normalPartsNameArr:Array = [];
      
      private var randomArr:Array = [];
      
      private var nowIndex:int = 0;
      
      public function ThingsDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var i:* = undefined;
         var fx0:XML = null;
         var thingsXML0:* = undefined;
         var fatherName0:String = null;
         var n:* = undefined;
         var d0:ThingsDefine = null;
         var fatherXML0:XMLList = xml0.father;
         for(i in fatherXML0)
         {
            fx0 = fatherXML0[i];
            thingsXML0 = fatherXML0[i].things;
            fatherName0 = fatherXML0[i].@name;
            for(n in thingsXML0)
            {
               d0 = new ThingsDefine();
               d0.inFatherXml(fx0);
               d0.inData_byXML(thingsXML0[n],fatherName0);
               if(d0.index == 0)
               {
                  d0.index = this.nowIndex;
                  ++this.nowIndex;
               }
               this.addDefine(d0);
               if(fatherName0 == "parts")
               {
                  this.createPartsDefine(d0,thingsXML0[n]);
               }
            }
         }
      }
      
      public function init() : void
      {
         var d0:ThingsDefine = null;
         var smeltType0:String = null;
         var sarr0:Array = null;
         for each(d0 in this.randomArr)
         {
            d0.fleshRandomDescription();
         }
         for each(d0 in this.obj)
         {
            smeltType0 = d0.smeltD.type;
            if(!d0.smeltD.canSmeltB())
            {
               smeltType0 = "";
            }
            sarr0 = this.smeltObj[smeltType0];
            if(!sarr0)
            {
               sarr0 = [];
               this.smeltObj[smeltType0] = sarr0;
               this.smeltCnObj[smeltType0] = [];
            }
            sarr0.push(d0);
            this.smeltCnObj[smeltType0][d0.cnName] = d0;
         }
      }
      
      private function addDefine(d0:ThingsDefine, cnB0:Boolean = true) : void
      {
         var xx0:int = 0;
         if(d0.cnName == "青蜂")
         {
            xx0 = 0;
         }
         this.obj[d0.name] = d0;
         if(cnB0)
         {
            if(!(d0.isPartsB() && this.cnObj[d0.cnName] != null))
            {
               this.cnObj[d0.cnName] = d0;
            }
         }
         var fatherName0:String = d0.father;
         if(d0.getShowGiftB())
         {
            this.randomArr.push(d0);
         }
         if(!this.fatherObj[fatherName0])
         {
            this.fatherObj[fatherName0] = {};
         }
         this.fatherObj[fatherName0][d0.name] = d0;
         if(!this.fatherArrObj[fatherName0])
         {
            this.fatherArrObj[fatherName0] = [];
         }
         this.fatherArrObj[fatherName0].push(d0);
         if(!d0.isPartsB())
         {
            if(!d0.hideB && !d0.isShopAutoUseB())
            {
               this.findCnObj[d0.cnName] = d0;
            }
         }
      }
      
      public function inComposeData_byXML(xml0:XML) : void
      {
         var x0:XML = null;
         var d0:ThingsComposeDefine = null;
         for each(x0 in xml0.things)
         {
            d0 = new ThingsComposeDefine();
            d0.inData_byXML(x0);
            this.composeArr.push(d0);
            this.composeObj[d0.name] = d0;
         }
      }
      
      public function getComposeThingsDefineArr() : Array
      {
         var cd0:ThingsComposeDefine = null;
         var arr0:Array = [];
         for each(cd0 in this.composeArr)
         {
            arr0.push(cd0.getThingsDefine());
         }
         return arr0;
      }
      
      public function getComposeDefineArr() : Array
      {
         return this.composeArr;
      }
      
      public function getDefineArrByFather(father0:String) : Array
      {
         return this.fatherArrObj[father0];
      }
      
      public function getThingsComposeDefine(name0:String) : ThingsComposeDefine
      {
         return this.composeObj[name0];
      }
      
      public function getConverChipDefineArr() : Array
      {
         var d0:ThingsDefine = null;
         var newArr0:Array = [];
         var arr0:Array = this.getDefineArrByFather("blackChip");
         for each(d0 in arr0)
         {
            if(d0.canConverB())
            {
               newArr0.push(d0);
            }
         }
         return newArr0;
      }
      
      private function createPartsDefine(d0:ThingsDefine, xml0:XML) : void
      {
         var cLv0:int = 0;
         var minLv0:int = 0;
         var i:int = 0;
         var d2:ThingsDefine = null;
         var last_index0:int = 0;
         cLv0 = PartsConst.cLv;
         if(d0.isPartsNormalB())
         {
            this.normalPartsNameArr.push(d0.name);
            minLv0 = PartsConst.minLv;
            cLv0 = PartsConst.cLv;
            for(i = minLv0; i <= 100; i += cLv0)
            {
               d2 = new ThingsDefine();
               d2.inData_byXML(xml0,d0.father);
               d2.baseLabel = d0.name;
               d2.index = d0.index;
               d2.itemsLevel = i;
               d2.name = d0.name + "_" + i;
               last_index0 = int((i - minLv0) / minLv0 / 5) + 1;
               if(last_index0 > 6)
               {
                  last_index0 = 6;
               }
               d2.iconUrl = PartsConst.getIconUrl(d0.name,i);
               d2.addDropDefineB = true;
               d2.cnName = PartsConst.getCn(d0.cnName,i);
               this.obj[d2.name] = d2;
               this.cnObj[d2.cnName] = d2;
            }
         }
         else if(d0.maxLevel > 0)
         {
            if(d0.isPartsSpecialB())
            {
               this.createSpecialPartsDefine(d0,xml0);
            }
            else if(d0.isPartsRareB())
            {
               if(d0.baseLabel == d0.name)
               {
                  this.createSpecialPartsDefine(d0,xml0);
               }
            }
         }
      }
      
      private function createSpecialPartsDefine(d0:ThingsDefine, xml0:XML) : void
      {
         var name0:String = null;
         var d2:ThingsDefine = null;
         var skillName0:* = null;
         var max0:int = d0.maxLevel;
         for(var i:int = 1; i <= max0; i++)
         {
            name0 = d0.name + "_" + i;
            if(this.getDefine(name0) == null)
            {
               d2 = new ThingsDefine();
               d2.inData_byXML(xml0,d0.father);
               d2.baseLabel = d0.name;
               d2.index = d0.index;
               d2.itemsLevel = i;
               d2.name = d0.name + "_" + i;
               if(d0.iconUrl != "")
               {
                  d2.iconUrl = d0.iconUrl + i;
               }
               else
               {
                  d2.iconUrl = "ThingsIcon/" + d0.name + i;
               }
               if(i == 1)
               {
                  d2.addDropDefineB = true;
               }
               if(d0.skillArr.length > 0)
               {
                  d2.skillArr = [];
                  for each(skillName0 in d0.skillArr)
                  {
                     d2.skillArr.push(skillName0 + "_" + i);
                  }
               }
               this.addDefine(d2,i == 1);
            }
         }
      }
      
      public function inBlackEquipFatherArr(arr0:Array) : void
      {
         var f0:EquipFatherDefine = null;
         var equipD0:EquipDefine = null;
         var d0:ThingsDefine = null;
         var lv0:int = 0;
         var baseD0:ThingsDefine = this.getDefine("blackChip");
         for each(f0 in arr0)
         {
            for each(equipD0 in f0.partObj)
            {
               d0 = this.getDefine(equipD0.name);
               if(!d0)
               {
                  d0 = new ThingsDefine();
                  if(d0.index == 0)
                  {
                     d0.index = this.nowIndex;
                     ++this.nowIndex;
                  }
                  d0.cnName = equipD0.cnName + "碎片";
               }
               d0.father = baseD0.father;
               d0.secType = "equip";
               d0.name = equipD0.name;
               d0.iconUrl = "ThingsIcon/" + d0.name;
               d0.itemsLevel = equipD0.itemsLevel;
               d0.addDropDefineB = true;
               lv0 = d0.itemsLevel;
               if(lv0 < 86)
               {
                  d0.smeltD.price = 2;
                  d0.smeltD.grade = 1;
               }
               else if(lv0 < 91)
               {
                  d0.smeltD.price = 10;
                  d0.smeltD.grade = 2;
                  d0.smeltD.maxNum = 1;
                  d0.smeltD.addType = ThingsSmeltType.armsEquip;
               }
               else
               {
                  d0.smeltD.price = 1;
               }
               if(lv0 >= 91)
               {
                  d0.smeltD.grade = -1;
               }
               d0.smeltD.type = ThingsSmeltType.equipChip;
               d0.btnList = ["compose","composeNum"];
               if(d0.canConverB())
               {
                  d0.btnList.push("conver");
               }
               this.addDefine(d0);
            }
         }
      }
      
      public function inBlackArmsRangeArr(arr0:Array) : void
      {
         var armsD0:ArmsRangeDefine = null;
         var d0:ThingsDefine = null;
         var lv0:int = 0;
         var baseD0:ThingsDefine = this.getDefine("blackChip");
         for each(armsD0 in arr0)
         {
            if(armsD0.def.chipNum > 0)
            {
               d0 = this.getDefine(armsD0.def.name);
               if(!d0)
               {
                  d0 = new ThingsDefine();
                  if(d0.index == 0)
                  {
                     d0.index = this.nowIndex;
                     ++this.nowIndex;
                  }
                  d0.cnName = armsD0.def.cnName + "碎片";
               }
               d0.father = baseD0.father;
               d0.secType = "arms";
               d0.name = armsD0.def.name;
               if(d0.iconUrl == "")
               {
                  d0.iconUrl = "ThingsIcon/" + d0.name;
               }
               d0.itemsLevel = armsD0.def.composeLv;
               d0.addDropDefineB = true;
               lv0 = d0.itemsLevel;
               if(lv0 < 86)
               {
                  d0.smeltD.price = 2;
                  d0.smeltD.grade = 1;
               }
               else if(lv0 < 91)
               {
                  d0.smeltD.price = 10;
                  d0.smeltD.grade = 2;
                  d0.smeltD.maxNum = 1;
                  d0.smeltD.addType = ThingsSmeltType.armsEquip;
               }
               else
               {
                  d0.smeltD.price = 1;
               }
               if(lv0 >= 90 || EquipColor.moreDarkgoldB(armsD0.def.color))
               {
                  d0.smeltD.grade = -1;
               }
               d0.smeltD.type = ThingsSmeltType.armsChip;
               d0.btnList = ["compose"];
               this.addDefine(d0);
            }
         }
      }
      
      public function inRareArmsRangeArr(arr0:Array) : void
      {
         var armsD0:ArmsRangeDefine = null;
         var d0:ThingsDefine = null;
         var lv0:int = 0;
         var baseD0:ThingsDefine = this.getDefine("rareChip");
         for each(armsD0 in arr0)
         {
            if(armsD0.def.haveChipB())
            {
               d0 = new ThingsDefine();
               if(d0.index == 0)
               {
                  d0.index = this.nowIndex;
                  ++this.nowIndex;
               }
               d0.inData_byObj(baseD0);
               d0.secType = "arms";
               d0.name = armsD0.def.name;
               d0.cnName = armsD0.def.cnName + "稀有碎片";
               d0.iconUrl = "ThingsIcon/" + d0.name;
               d0.addDropDefineB = true;
               d0.description = "合成" + armsD0.def.cnName + "所需物品。";
               d0.description += armsD0.def.description;
               lv0 = d0.itemsLevel;
               d0.smeltD.price = 10;
               d0.smeltD.grade = 1;
               d0.smeltD.type = ThingsSmeltType.armsChip;
               d0.btnList = ["compose"];
               d0.fleshDescription();
               this.addDefine(d0);
            }
         }
      }
      
      public function getDefine(name0:String) : ThingsDefine
      {
         return this.obj[name0];
      }
      
      public function getCn(name0:String) : String
      {
         var d0:ThingsDefine = this.getDefine(name0);
         if(Boolean(d0))
         {
            return d0.cnName;
         }
         return "";
      }
      
      public function getDefineByCnName(name0:String) : ThingsDefine
      {
         return this.cnObj[name0];
      }
      
      public function getKeyByLevel(lv0:int) : ThingsDefine
      {
         var d0:ThingsDefine = null;
         for each(d0 in this.obj)
         {
            if(d0.dropLevelArr.indexOf(lv0 + "") >= 0)
            {
               if(d0.name.indexOf("Key") > 0)
               {
                  return d0;
               }
            }
         }
         return null;
      }
      
      public function getIconNum() : int
      {
         var d0:ThingsDefine = null;
         var obj0:Object = {};
         var num0:int = 0;
         for each(d0 in this.obj)
         {
            if(!obj0.hasOwnProperty(d0.iconUrl))
            {
               obj0[d0.iconUrl] = 0;
               num0++;
            }
         }
         return num0;
      }
      
      public function getCnArrByNameArr(nameArr0:Array) : Array
      {
         var name0:* = null;
         var d0:ThingsDefine = null;
         var cnArr0:Array = [];
         for each(name0 in nameArr0)
         {
            d0 = this.getDefine(name0);
            if(Boolean(d0))
            {
               cnArr0.push(d0.cnName);
            }
         }
         return cnArr0;
      }
      
      public function getCnArrByFind(str0:String) : Array
      {
         var objArr0:Array = null;
         var cn0:* = undefined;
         var f0:int = 0;
         var d0:ThingsDefine = null;
         var obj0:Object = null;
         var cnArr0:Array = [];
         var cud0:ThingsDefine = this.findCnObj[str0];
         if(Boolean(cud0))
         {
            cnArr0.push(cud0.cnName);
         }
         else
         {
            objArr0 = [];
            for(cn0 in this.findCnObj)
            {
               f0 = int((cn0 as String).indexOf(str0));
               d0 = this.findCnObj[cn0];
               if(f0 >= 0)
               {
                  obj0 = new Object();
                  obj0.index = f0;
                  obj0.def = d0;
                  objArr0.push(obj0);
               }
            }
            if(objArr0.length > 0)
            {
               objArr0.sort(this.sortFindCnObjFun);
               for each(obj0 in objArr0)
               {
                  cnArr0.push(obj0.def.cnName);
               }
            }
         }
         return cnArr0;
      }
      
      private function sortFindCnObjFun(a:Object, b:Object) : int
      {
         var ad0:ThingsDefine = null;
         var bd0:ThingsDefine = null;
         var s2:int = 0;
         var s1:int = ArrayMethod.sortNumberFun(a.index,b.index);
         if(s1 == 0)
         {
            ad0 = a.def;
            bd0 = b.def;
            s2 = ArrayMethod.sortNumberFun(ad0.cnName.length,bd0.cnName.length);
            if(s2 == 0)
            {
               return ArrayMethod.sortNumberFun(ad0.index,bd0.index);
            }
            return s2;
         }
         return s1;
      }
      
      public function getSmeltGatherTip() : String
      {
         var n:* = null;
         var arr0:Array = null;
         var lvObj0:Object = null;
         var d0:ThingsDefine = null;
         var i:int = 0;
         var grade0:int = 0;
         var lvArr0:Array = null;
         var one0:String = null;
         var s0:String = this.smeltGatherTip;
         if(s0 == "")
         {
            for each(n in ThingsSmeltType.arr)
            {
               arr0 = this.smeltCnObj[n];
               if(Boolean(arr0))
               {
                  lvObj0 = {};
                  for each(d0 in arr0)
                  {
                     if(d0.isSmeltMaterialB())
                     {
                        grade0 = d0.smeltD.grade;
                        if(!(grade0 == -1 && d0.smeltD.price < 10))
                        {
                           if(lvObj0.hasOwnProperty(grade0) == false)
                           {
                              lvObj0[grade0] = [];
                           }
                           lvArr0 = lvObj0[grade0];
                           lvArr0.push(d0);
                        }
                     }
                  }
                  s0 += ComMethod.color("--------------" + ThingsSmeltType.getCn(n) + "--------------","#666666") + "\n";
                  for(i = -1; i <= 5; i++)
                  {
                     lvArr0 = lvObj0[i];
                     if(Boolean(lvArr0))
                     {
                        lvArr0.sort(this.sortBySmeltPrice);
                        one0 = "";
                        for each(d0 in lvArr0)
                        {
                           if(one0 != "")
                           {
                              one0 += "、";
                           }
                           one0 += d0.cnName;
                        }
                        one0 = ComMethod.color(one0,ThingsSmeltType.getGradeColor(i));
                        one0 = "  【" + ThingsSmeltType.getGradeCn(i) + "】" + one0;
                        s0 += one0 + "\n";
                     }
                  }
               }
            }
         }
         return s0;
      }
      
      private function sortBySmeltPrice(d1:ThingsDefine, d2:ThingsDefine) : int
      {
         return ArrayMethod.sortNumberFun(d2.smeltD.price,d1.smeltD.price);
      }
      
      public function countLove0() : void
      {
         var d0:ThingsDefine = null;
         INIT.tempTrace("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$");
         for each(d0 in this.obj)
         {
            if(d0.loveD.num == 0)
            {
               INIT.tempTrace(d0.cnName);
            }
         }
         INIT.tempTrace("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$");
      }
      
      public function countSmelt0() : void
      {
         var d0:ThingsDefine = null;
         INIT.tempTrace("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$");
         for each(d0 in this.obj)
         {
            if(d0.isPartsB() == false)
            {
               if(d0.smeltD.havePriceB == false)
               {
                  INIT.tempTrace(d0.cnName);
               }
            }
         }
         INIT.tempTrace("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$");
      }
   }
}

