package dataAll._app.setting.key
{
   import com.sounto.utils.ClassProperty;
   
   public class SettingKeySaveGroup
   {
      public var obj:Object = {};
      
      public function SettingKeySaveGroup()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         this.obj = ClassProperty.copySaveObj(obj0["obj"],SettingKeySave);
         if(ClassProperty.countObjNum(this.obj) == 0)
         {
            this.initObj();
         }
         this.doubleDiviceInit();
      }
      
      public function initObj() : void
      {
         var type0:* = null;
         var keyObj0:Object = null;
         var s0:SettingKeySave = null;
         var obj0:Object = {};
         var type_arr0:Array = KeyActionDefine.type_arr;
         for each(type0 in type_arr0)
         {
            keyObj0 = Gaming.defineGroup.keyAction.getKeyObj(type0);
            s0 = new SettingKeySave();
            s0.inObj(keyObj0,type0);
            obj0[type0] = s0;
         }
         this.obj = obj0;
      }
      
      public function toDefault(label0:String) : void
      {
         var type0:* = null;
         var s0:SettingKeySave = null;
         var arr0:Array = KeyActionDefine.getTypeArr(label0);
         for each(type0 in arr0)
         {
            s0 = this.getKeySave(type0);
            s0.toDefault();
         }
      }
      
      private function doubleDiviceInit() : void
      {
         var defaultKeyDefine0:KeyActionDefine = null;
         var p1:SettingKeySave = this.getKeySave("p1");
         var p2:SettingKeySave = this.getKeySave("p2");
         if(p2.arrObj["nextSay"].length == 0)
         {
            defaultKeyDefine0 = Gaming.defineGroup.keyAction.getDefine("nextSay");
            p2.arrObj["nextSay"] = p1.arrObj["nextSay"].concat([]);
            p1.arrObj["nextSay"] = defaultKeyDefine0.p1.concat([]);
         }
      }
      
      public function getKeySave(playerType0:String) : SettingKeySave
      {
         return this.obj[playerType0];
      }
      
      public function setNewKeyCodeEvent(type0:String, name0:String, keyCode0:int, beforeCode0:int) : void
      {
         var rangeArr0:Array = null;
         var d0:KeyActionDefine = null;
         var newType0:* = null;
         var s0:SettingKeySave = null;
         if(keyCode0 != 0)
         {
            rangeArr0 = [];
            d0 = Gaming.defineGroup.keyAction.getDefine(name0);
            if(type0 == "single")
            {
               rangeArr0 = ["single"];
            }
            else
            {
               rangeArr0 = ["p1","p2"];
            }
            for each(newType0 in rangeArr0)
            {
               s0 = this.obj[newType0];
               s0.setNewKeyCodeEvent(type0,name0,keyCode0,beforeCode0);
            }
         }
      }
   }
}

