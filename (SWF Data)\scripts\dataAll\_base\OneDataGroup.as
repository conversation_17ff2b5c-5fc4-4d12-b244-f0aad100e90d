package dataAll._base
{
   import com.sounto.utils.ArrayMethod;
   
   public class OneDataGroup
   {
      protected var dataClass:Class = OneData;
      
      protected var saveG:OneSaveGroup;
      
      protected var arr:Array = [];
      
      public function OneDataGroup()
      {
         super();
      }
      
      public function inData_bySave(sg0:OneSaveGroup) : void
      {
         var s0:OneSave = null;
         var da0:OneData = null;
         this.saveG = sg0;
         this.arr.length = 0;
         for each(s0 in sg0.arr)
         {
            da0 = this.getNewData();
            da0.inData_bySave(s0);
            this.insideAddData(da0);
         }
      }
      
      protected function getNewData() : OneData
      {
         return new this.dataClass();
      }
      
      public function addSave(s0:OneSave) : OneData
      {
         var da0:OneData = this.getNewData();
         this.saveG.addSave(s0);
         da0.inData_bySave(s0);
         this.addData(da0);
         return da0;
      }
      
      protected function insideAddData(da0:OneData) : void
      {
         this.arr.push(da0);
      }
      
      public function addData(da0:OneData) : void
      {
         this.arr.push(da0);
      }
      
      public function removeData(da0:OneData) : void
      {
         ArrayMethod.remove(this.arr,da0);
         this.saveG.removeSave(da0.getOneSave());
         this.removeDataEvent(da0);
      }
      
      protected function removeDataEvent(da0:OneData) : void
      {
      }
      
      public function removeDataArr(arr0:Array) : void
      {
         var da0:OneData = null;
         for each(da0 in arr0)
         {
            this.removeDataEvent(da0);
         }
         this.arr = ArrayMethod.deductArr(this.arr,arr0);
         this.saveG.arr = this.getSaveArrFromData();
      }
      
      protected function getSaveArrFromData() : Array
      {
         var da0:OneData = null;
         var saveArr0:Array = [];
         for each(da0 in this.arr)
         {
            saveArr0.push(da0.getOneSave());
         }
         return saveArr0;
      }
      
      public function get hideHighB() : Boolean
      {
         return this.saveG.hideHB;
      }
      
      public function set hideHighB(v0:Boolean) : void
      {
         this.saveG.hideHB = v0;
      }
      
      public function getReverseDataArr() : Array
      {
         var arr0:Array = this.arr.concat();
         arr0.reverse();
         return arr0;
      }
      
      public function getDataArr() : Array
      {
         return this.arr;
      }
      
      public function findDataIndex(da0:OneData) : int
      {
         return this.arr.indexOf(da0);
      }
      
      public function removeAll() : void
      {
         var da0:OneData = null;
         for each(da0 in this.arr)
         {
            this.removeDataEvent(da0);
         }
         this.arr.length = 0;
         this.saveG.removeAll();
      }
   }
}

