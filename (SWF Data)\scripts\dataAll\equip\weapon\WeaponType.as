package dataAll.equip.weapon
{
   public class WeaponType
   {
      public static var BLADE:String = "blade";
      
      public static var DAGGER:String = "dagger";
      
      public static var STICK:String = "stick";
      
      public static const saberDartsBullet:String = "saberDartsBullet";
      
      public static const saberDartsFox:String = "saberDartsFox";
      
      public static const bulletArr:Array = [saberDartsBullet,saberDartsFox];
      
      public function WeaponType()
      {
         super();
      }
      
      public static function isBulletB(name0:String) : Boolean
      {
         return bulletArr.indexOf(name0) >= 0;
      }
   }
}

