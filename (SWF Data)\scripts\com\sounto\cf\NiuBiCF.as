package com.sounto.cf
{
   import com.sounto.utils.ObjectMethod;
   
   public class NiuBiCF
   {
      protected var varObj:Object = {};
      
      protected var sounto64:SountoLocal64 = SountoLocal64.getRandom();
      
      public function NiuBiCF()
      {
         super();
      }
      
      public function addAttribute(varName:String, varValue:Number) : *
      {
         var v0:Number = this.getAttribute(varName);
         v0 += varValue;
         this.setAttribute(varName,v0);
      }
      
      public function setAttribute(varName:String, varValue:Number) : *
      {
         var tmpObj:Object = {"value":this.encode(varValue)};
         this.varObj[varName] = tmpObj;
      }
      
      protected function encode(varValue:Number) : String
      {
         return this.sounto64.encode(String(varValue));
      }
      
      protected function decode(str0:String) : Number
      {
         return Number(this.sounto64.decode(str0));
      }
      
      public function getAttribute(varName:String) : Number
      {
         if(this.varObj[varName] == null)
         {
            return 0;
         }
         return this.decode(this.varObj[varName].value);
      }
      
      protected function setObj(obj0:Object) : void
      {
         var n:* = undefined;
         this.varObj = {};
         if(Boolean(obj0))
         {
            for(n in obj0)
            {
               this.varObj[n] = {"value":this.encode(obj0[n])};
            }
         }
      }
      
      protected function getObj() : Object
      {
         var n:* = undefined;
         var obj0:Object = {};
         for(n in this.varObj)
         {
            if(Boolean(this.varObj[n]))
            {
               obj0[n] = this.decode(this.varObj[n].value);
            }
         }
         return obj0;
      }
      
      public function haveDataB() : Boolean
      {
         return ObjectMethod.getObjElementNum(this.varObj) > 0;
      }
      
      public function getEncodeObj() : Object
      {
         return this.varObj;
      }
      
      public function keepVarObjByNameObj(obj0:Object) : void
      {
         this.varObj = ObjectMethod.keepName(this.varObj,obj0);
      }
      
      public function getVarNum() : int
      {
         var n:* = undefined;
         var num0:int = 0;
         for(n in this.varObj)
         {
            if(this.varObj[n] != null)
            {
               num0++;
            }
         }
         return num0;
      }
      
      public function getNameArr() : Array
      {
         return ObjectMethod.getNameArr(this.varObj);
      }
      
      public function clearName(name0:String) : void
      {
         this.varObj = ObjectMethod.remove(this.varObj,name0);
      }
      
      public function clearNameArr(nameArr0:Array) : void
      {
         this.varObj = ObjectMethod.removeArr(this.varObj,nameArr0);
      }
      
      public function clearData() : void
      {
         this.varObj = {};
      }
   }
}

