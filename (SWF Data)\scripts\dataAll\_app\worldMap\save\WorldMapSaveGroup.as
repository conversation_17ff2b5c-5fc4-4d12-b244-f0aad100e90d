package dataAll._app.worldMap.save
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._player.base.PlayerBaseData;
   import dataAll.level.LevelCountSave;
   
   public class WorldMapSaveGroup
   {
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var obj:Object = {};
      
      public var superNum:int = 0;
      
      public var dropRocketB:Boolean = false;
      
      public var winMaxDiffMapArr:Array = [];
      
      public function WorldMapSaveGroup()
      {
         super();
         this.todayEndlessNum = 0;
         this.sweepingNum = 0;
      }
      
      public function get todayEndlessNum() : Number
      {
         return this.CF.getAttribute("todayEndlessNum");
      }
      
      public function set todayEndlessNum(v0:Number) : void
      {
         this.CF.setAttribute("todayEndlessNum",v0);
      }
      
      public function get sweepingNum() : Number
      {
         return this.CF.getAttribute("sweepingNum");
      }
      
      public function set sweepingNum(v0:Number) : void
      {
         this.CF.setAttribute("sweepingNum",v0);
      }
      
      public function get ch() : Number
      {
         return this.CF.getAttribute("ch");
      }
      
      public function set ch(v0:Number) : void
      {
         this.CF.setAttribute("ch",v0);
      }
      
      public function get chA() : Number
      {
         return this.CF.getAttribute("chA");
      }
      
      public function set chA(v0:Number) : void
      {
         this.CF.setAttribute("chA",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.obj = ClassProperty.copySaveObj(obj0["obj"],WorldMapSave);
         if(this.obj.hasOwnProperty("CavesDeepGeology"))
         {
            this.obj = this.dealObj(this.obj);
         }
      }
      
      private function dealObj(obj0:Object) : Object
      {
         var n:* = undefined;
         var name0:String = null;
         var newName0:String = null;
         var new0:Object = {};
         for(n in obj0)
         {
            name0 = String(n);
            if(name0 == "CavesDeepGeology")
            {
               newName0 = "CavesDeep";
               new0[newName0] = obj0[n];
               new0[newName0].name = newName0;
            }
            else
            {
               new0[n] = obj0[n];
            }
         }
         return new0;
      }
      
      public function dealSaveObj(obj0:Object) : void
      {
         var n:* = undefined;
         var oldObj0:Object = null;
         var s0:WorldMapSave = null;
         var newArr0:Object = {};
         for(n in this.obj)
         {
            oldObj0 = obj0.obj[n];
            s0 = this.obj[n];
            newArr0[n] = s0.getSaveObj(oldObj0);
         }
         obj0.obj = newArr0;
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.todayEndlessNum = 0;
         this.sweepingNum = 0;
      }
      
      public function newWeek(timeStr0:String) : void
      {
         var s0:WorldMapSave = null;
         for each(s0 in this.obj)
         {
            s0.newWeek(timeStr0);
         }
         this.ch = 0;
      }
      
      public function delOne(name0:String) : void
      {
         var n:* = undefined;
         var obj2:Object = {};
         for(n in this.obj)
         {
            if(n != name0)
            {
               obj2[n] = this.obj[n];
            }
         }
         this.obj = obj2;
      }
      
      public function unlockOne(name0:String) : WorldMapSave
      {
         var s0:WorldMapSave = null;
         var d0:WorldMapDefine = Gaming.defineGroup.worldMap.getDefine(name0);
         if(Boolean(d0))
         {
            if(d0.isSpaceB())
            {
               return null;
            }
         }
         if(!this.obj.hasOwnProperty(name0))
         {
            s0 = new WorldMapSave();
            s0.name = name0;
            this.obj[name0] = s0;
         }
         return this.obj[name0];
      }
      
      public function unlockAll() : void
      {
         var n:* = undefined;
         var obj0:Object = Gaming.defineGroup.worldMap.obj;
         for(n in obj0)
         {
            this.unlockOne(n);
         }
      }
      
      public function unlockAllDiff() : void
      {
         var s0:WorldMapSave = null;
         for each(s0 in this.obj)
         {
            s0.diffUnlock = 9;
         }
      }
      
      public function setOneName(map0:String, name0:String) : Boolean
      {
         var s0:WorldMapSave = this.getSave(map0);
         if(s0 is WorldMapSave)
         {
            if(s0.levelName != name0)
            {
               s0.setLevelName(name0);
               return true;
            }
         }
         return false;
      }
      
      public function winOne(name0:String, diff0:int, mapModel0:String = "regular", count0:LevelCountSave = null) : void
      {
         var linkArr0:Array = null;
         var n:* = undefined;
         var s2:WorldMapSave = null;
         var name2:String = null;
         var s0:WorldMapSave = this.getSave(name0);
         if(Boolean(s0))
         {
            s0.win(diff0,mapModel0);
            if(Boolean(count0))
            {
               s0.countSave.addData(count0);
            }
            linkArr0 = s0.getDefine().linkArr;
            for(n in linkArr0)
            {
               name2 = linkArr0[n];
               this.unlockOne(name2);
            }
            for each(s2 in this.obj)
            {
               s2.winOtherOne(name0,diff0,this,mapModel0);
            }
         }
      }
      
      public function winAll() : void
      {
         var s0:WorldMapSave = null;
         for each(s0 in this.obj)
         {
            s0.winB = true;
         }
      }
      
      public function getWinB(name0:String) : Boolean
      {
         var s0:WorldMapSave = this.getSave(name0);
         if(Boolean(s0))
         {
            return s0.winB;
         }
         return false;
      }
      
      public function getSave(name0:String) : WorldMapSave
      {
         return this.obj[name0];
      }
      
      public function getWinArr() : Array
      {
         var s0:WorldMapSave = null;
         var arr0:Array = [];
         for each(s0 in this.obj)
         {
            if(s0.winB)
            {
               arr0.push(s0);
            }
         }
         return arr0;
      }
      
      public function getNoWinArr() : Array
      {
         var s0:WorldMapSave = null;
         var arr0:Array = [];
         for each(s0 in this.obj)
         {
            if(!s0.winB)
            {
               arr0.push(s0);
            }
         }
         return arr0;
      }
      
      public function haveChallengeLevelB() : Boolean
      {
         var s0:WorldMapSave = null;
         for each(s0 in this.obj)
         {
            if(s0.challengeLevel > 0)
            {
               return true;
            }
         }
         return false;
      }
      
      public function randomChallengeLevel(heroLv0:int) : void
      {
         var d0:WorldMapDefine = null;
         var s0:WorldMapSave = null;
         var normalLv0:int = 0;
         if(heroLv0 > PlayerBaseData.ENEMY_LEVEL)
         {
            heroLv0 = PlayerBaseData.ENEMY_LEVEL;
         }
         var num0:int = 0;
         var lv0:int = heroLv0;
         var wArr0:Array = Gaming.defineGroup.worldMap.getArrByPriority(0.6);
         for each(d0 in wArr0)
         {
            s0 = this.getSave(d0.name);
            if(s0 is WorldMapSave)
            {
               s0.challengeLevel = 0;
               normalLv0 = s0.getNormalLv();
               lv0 = heroLv0 - int(num0 / 4);
               if(s0.winB && normalLv0 > 0 && lv0 > normalLv0)
               {
                  s0.challengeLevel = lv0;
                  s0.setAllChallengeNum(1);
                  num0++;
               }
            }
         }
      }
      
      public function getDemStoneAll() : Number
      {
         var s0:WorldMapSave = null;
         var num0:Number = 0;
         for each(s0 in this.obj)
         {
            num0 += s0.dm;
         }
         return num0;
      }
      
      public function getAllCount() : String
      {
         var s0:WorldMapSave = null;
         var count0:LevelCountSave = new LevelCountSave();
         var save0:WorldMapSave = new WorldMapSave();
         for each(s0 in this.obj)
         {
            this.getOneCount(count0,s0.countSave,LevelCountSave.pro_arr);
            this.getOneCount(save0,s0,WorldMapSave.pro_arr);
         }
         return save0.getCountString() + "\n" + count0.toString();
      }
      
      private function getOneCount(obj0:Object, obj1:Object, pro_arr0:Array) : void
      {
         var pro0:* = null;
         for each(pro0 in pro_arr0)
         {
            if(obj0[pro0] is Number || obj0[pro0] is int)
            {
               obj0[pro0] += obj1[pro0];
            }
         }
      }
      
      public function getSaveArr() : Array
      {
         var s0:WorldMapSave = null;
         var arr0:Array = [];
         for each(s0 in this.obj)
         {
            arr0.push(s0);
         }
         return arr0;
      }
      
      public function getMaxMapLevel() : int
      {
         var s0:WorldMapSave = null;
         var d0:WorldMapDefine = null;
         var max0:int = 0;
         for each(s0 in this.obj)
         {
            d0 = s0.getDefine();
            if(d0.lv > max0)
            {
               max0 = d0.lv;
            }
         }
         return max0;
      }
      
      public function getMapCount() : WorldMapCount
      {
         var s0:WorldMapSave = null;
         var d0:WorldMapDefine = null;
         var c0:WorldMapCount = new WorldMapCount();
         for each(s0 in this.obj)
         {
            d0 = s0.getDefine();
            this.getOneCount(c0.count,s0.countSave,LevelCountSave.pro_arr);
            this.getOneCount(c0,s0,["winNum"]);
            if(s0.winNum > c0.mostNum)
            {
               c0.mostNum = s0.winNum;
               c0.mostMapCn = d0.cnName;
            }
         }
         return c0;
      }
   }
}

