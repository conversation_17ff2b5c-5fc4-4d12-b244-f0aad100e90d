package dataAll.skill
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._player.more.MorePlayerData;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.NormalItemsData;
   import dataAll.items.define.IO_ResolveItemsDefine;
   import dataAll.items.save.ItemsSave;
   import dataAll.skill.define.HeroSkillDefine;
   import dataAll.skill.define.SkillFather;
   import dataAll.skill.save.HeroSkillSave;
   import gameAll.skill.SkillTargetAlert;
   
   public class HeroSkillData extends NormalItemsData implements IO_ItemsData
   {
      public var save:HeroSkillSave = null;
      
      public var heroSkillDataGroup:HeroSkillDataGroup;
      
      public function HeroSkillData()
      {
         super();
      }
      
      public static function get maxDayProfi() : int
      {
         return 4000;
      }
      
      public static function get maxProfi() : int
      {
         return 81000;
      }
      
      public function inData_bySave(s0:HeroSkillSave, normalPlayerData0:NormalPlayerData, heroSkillDataGroup0:HeroSkillDataGroup) : void
      {
         this.save = s0;
         this.heroSkillDataGroup = heroSkillDataGroup0;
         setPlayerData(normalPlayerData0);
      }
      
      public function normalClone() : IO_ItemsData
      {
         return null;
      }
      
      public function canUseB() : Boolean
      {
         var def0:HeroSkillDefine = null;
         var cnArr0:Array = null;
         if(Boolean(normalPlayerData) && Boolean(this.save))
         {
            def0 = this.save.getDefine();
            if(def0.father == SkillFather.heroSkill)
            {
               cnArr0 = normalPlayerData.getHeroSkillCnArr();
               if(cnArr0.indexOf(def0.cnName) >= 0)
               {
                  return true;
               }
               return false;
            }
         }
         return true;
      }
      
      public function getSkillAddData() : SkillAddData
      {
         if(normalPlayerData is NormalPlayerData)
         {
            return normalPlayerData.equip.getSkillAddData(this.save.baseLabel);
         }
         return SkillAddData.ZERO;
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.save.dayProfi = 0;
      }
      
      public function addProfi(v0:Number) : void
      {
         var newV0:Number = NaN;
         var noFull0:HeroSkillData = null;
         v0 *= 4;
         if(Boolean(normalPlayerData))
         {
            if(normalPlayerData is MorePlayerData)
            {
               v0 *= 2;
            }
         }
         if(this.save.getProfiFullB())
         {
            if(Boolean(normalPlayerData))
            {
               newV0 = Math.round(v0 / 3);
               if(newV0 > 0)
               {
                  noFull0 = normalPlayerData.getProfiNoFull();
                  if(Boolean(noFull0))
                  {
                     noFull0.addProfiValue(newV0);
                     INIT.tempTrace("熟练度溢出，添加到技能：" + noFull0.getCnName() + "：" + newV0);
                  }
               }
            }
         }
         else
         {
            this.addProfiValue(v0);
         }
      }
      
      private function addProfiValue(v0:Number) : void
      {
         var profi0:Number = NaN;
         var new0:Number = NaN;
         if(v0 <= 0)
         {
            return;
         }
         v0 = Math.round(v0);
         var day0:Number = this.save.dayProfi;
         var newDay0:Number = day0 + v0;
         if(newDay0 > maxDayProfi)
         {
            newDay0 = maxDayProfi;
         }
         var add0:Number = newDay0 - day0;
         if(add0 > 0)
         {
            profi0 = this.save.profi;
            new0 = profi0 + add0;
            if(new0 > maxProfi)
            {
               new0 = maxProfi;
            }
            if(new0 > profi0)
            {
               this.save.dayProfi = newDay0;
               this.save.profi = new0;
            }
         }
      }
      
      public function addProfiNoLimit(v0:Number) : void
      {
         this.save.profi += v0;
      }
      
      public function useProfi(v0:Number) : void
      {
         var profi0:Number = this.save.profi;
         profi0 -= v0;
         if(profi0 < 0)
         {
            profi0 = 0;
         }
         this.save.profi = profi0;
      }
      
      override public function getSave() : ItemsSave
      {
         return this.save;
      }
      
      public function getCnType() : String
      {
         return "技能";
      }
      
      public function getIconImgUrl(maxWidth0:int = 0, maxHeight0:int = 0) : String
      {
         return this.save.getDefine().getIconImgUrl(maxWidth0,maxHeight0);
      }
      
      public function getCnName() : String
      {
         return this.save.getDefine().cnName;
      }
      
      public function getSellPrice() : Number
      {
         return 0;
      }
      
      public function getWearLevel() : int
      {
         return this.save.getTrueLevel();
      }
      
      public function getTypeId() : String
      {
         return "05";
      }
      
      public function getChildTypeId() : String
      {
         return "01";
      }
      
      public function isCanRebuildB() : Boolean
      {
         return !this.save.lockB && !this.save.getDefine().noRebuildB;
      }
      
      override public function getResolveItemsDefine() : IO_ResolveItemsDefine
      {
         return this.save.getDefine();
      }
      
      override public function getResolveGift() : GiftAddDefineGroup
      {
         return HeroSkillReset.getOneGift(this,false);
      }
      
      public function getGatherTip(skillDecripB0:Boolean = false) : String
      {
         var addData0:SkillAddData = null;
         var d0:HeroSkillDefine = this.save.getDefine();
         var str0:String = "";
         if(this.canUseB() == false)
         {
            str0 += ComMethod.color("<b>此技能只能在P1状态下使用。</b>","#FF0000");
         }
         if(this.save.lockB)
         {
            str0 += ComMethod.color("<b>天赋技能</b>","#FF66FF");
         }
         if(skillDecripB0)
         {
            str0 += d0.getLevelGatherTip(d0.lv);
         }
         else
         {
            str0 += d0.getGatherTip();
            if(placeType == "wear" && d0.isActiveB())
            {
            }
            if(normalPlayerData is NormalPlayerData)
            {
               addData0 = this.getSkillAddData();
               if(addData0.haveDataB())
               {
                  str0 += "\n<purple " + addData0.getGatherTip() + "/>";
               }
               if(d0.haveProfiB())
               {
                  str0 += "\n\n<gray2 今日获得熟练度：/><blue " + this.save.dayProfi + "/>/" + maxDayProfi;
                  str0 += "\n<gray2 累计获得熟练度：/><blue " + this.save.profi + "/>/" + maxProfi;
               }
            }
            if(d0.effectType == "possession")
            {
               str0 += "\n\n<orange 附身后，玩家通过切换武器键来切换攻击方式，通过射击键来执行攻击。/>";
               if(d0.lv >= 6)
               {
                  str0 += "\n\n<purple 目前可附身的首领：/><gray " + SkillTargetAlert.getPossessionBossListGather() + "/>";
               }
            }
         }
         return str0;
      }
   }
}

