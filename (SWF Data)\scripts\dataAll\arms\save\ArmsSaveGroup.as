package dataAll.arms.save
{
   import com.sounto.utils.ClassProperty;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.items.save.ItemsSaveGroup;
   
   public class ArmsSaveGroup extends ItemsSaveGroup
   {
      public static var pro_arr:Array = null;
      
      public static var mePro_arr:Array = [];
      
      public function ArmsSaveGroup()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         super.inData_byObjAndClass(obj0,ArmsSave);
         ClassProperty.inData_bySaveObj(this,obj0,mePro_arr);
      }
      
      public function getArmsImageArr() : Array
      {
         var n:* = undefined;
         var s0:ArmsSave = null;
         var arr2:Array = [];
         for(n in arr)
         {
            s0 = arr[n];
            arr2.push(s0.getImgLabel());
         }
         return arr2;
      }
      
      public function getArmsSoundUrlArr() : Array
      {
         var n:* = undefined;
         var s0:ArmsSave = null;
         var arr2:Array = [];
         for(n in arr)
         {
            s0 = arr[n];
            arr2.push(s0.shootSoundUrl);
         }
         return arr2;
      }
      
      public function getDefineArr() : Array
      {
         var n:* = undefined;
         var s0:ArmsSave = null;
         var d0:ArmsRangeDefine = null;
         var arr2:Array = [];
         for(n in arr)
         {
            s0 = arr[n];
            d0 = Gaming.defineGroup.bullet.getArmsRangeDefine(s0.name);
            if(d0 is ArmsRangeDefine)
            {
               arr2.push(d0.def);
            }
         }
         return arr2;
      }
      
      public function dealSaveObj(obj0:Object) : void
      {
         var n:* = undefined;
         var oldObj0:Object = null;
         var s0:ArmsSave = null;
         var newArr0:Array = [];
         for(n in arr)
         {
            oldObj0 = obj0.arr[n];
            s0 = arr[n];
            newArr0.push(s0.getSaveObj(oldObj0));
         }
         obj0.arr = newArr0;
      }
   }
}

