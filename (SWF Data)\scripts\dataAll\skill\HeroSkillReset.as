package dataAll.skill
{
   import dataAll._player.more.NormalPlayerData;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.must.define.MustDefine;
   import dataAll.must.define.MustSimpleThingsDefine;
   import dataAll.skill.define.HeroSkillDefine;
   import dataAll.skill.save.HeroSkillSave;
   
   public class HeroSkillReset
   {
      public function HeroSkillReset()
      {
         super();
      }
      
      public static function getCanResetNum(pd0:NormalPlayerData) : int
      {
         var lv0:int = pd0.level;
         return int(lv0 / 10);
      }
      
      public static function getResetByPlayerData(pd0:NormalPlayerData) : GiftAddDefineGroup
      {
         return getReset(excludeLockSkill(pd0.skill.dataArr.concat(pd0.skillBag.dataArr)));
      }
      
      public static function getReset(skillDataArr0:Array, mul0:Number = 1, haveCoinB0:Boolean = true) : GiftAddDefineGroup
      {
         var n:* = undefined;
         var newObj0:Object = null;
         var obj2:Object = null;
         var arr0:Array = skillDataArr0;
         var obj0:Object = {};
         for(n in arr0)
         {
            obj2 = getOne(arr0[n]);
            fixedObj(obj0,obj2);
         }
         newObj0 = {};
         for(n in obj0)
         {
            if(n == "coin")
            {
               newObj0[n] = Math.ceil(obj0[n] / 2 * mul0);
            }
            else
            {
               newObj0[n] = Math.ceil(obj0[n] * mul0);
            }
         }
         if(!haveCoinB0)
         {
            newObj0["coin"] = 0;
         }
         return getGift(newObj0);
      }
      
      public static function excludeLockSkill(arr0:Array) : Array
      {
         var da0:HeroSkillData = null;
         var arr2:Array = [];
         for each(da0 in arr0)
         {
            if(da0.isCanRebuildB())
            {
               arr2.push(da0);
            }
         }
         return arr2;
      }
      
      private static function getOne(da0:HeroSkillData) : Object
      {
         var obj0:Object = getStudy(da0);
         var obj2:Object = getUpgrade(da0);
         fixedObj(obj0,obj2);
         return obj0;
      }
      
      public static function getOneGift(da0:HeroSkillData, haveCoinB0:Boolean = true) : GiftAddDefineGroup
      {
         var obj0:Object = getOne(da0);
         if(!haveCoinB0)
         {
            obj0["coin"] = 0;
         }
         return getGift(obj0);
      }
      
      private static function getStudy(da0:HeroSkillData) : Object
      {
         var s0:HeroSkillSave = da0.save;
         var d0:HeroSkillDefine = s0.getDefine();
         var mustLv0:int = s0.studyBodyLv;
         var must_d0:MustDefine = d0.getThisStudyMust(mustLv0);
         return getObjByMustDefine(must_d0);
      }
      
      private static function getUpgrade(da0:HeroSkillData) : Object
      {
         var must_d0:MustDefine = null;
         var obj2:Object = null;
         var obj0:Object = {};
         var s0:HeroSkillSave = da0.save;
         var d0:HeroSkillDefine = s0.getDefine();
         var mustLv0:int = s0.studyBodyLv;
         var maxLv0:int = da0.save.lv;
         for(var lv0:int = 1; lv0 < maxLv0; lv0++)
         {
            must_d0 = HeroSkillSave.getUpradeMustBySave(mustLv0,lv0,s0.getOriginalDefine());
            obj2 = getObjByMustDefine(must_d0);
            fixedObj(obj0,obj2);
         }
         return obj0;
      }
      
      private static function getObjByMustDefine(d0:MustDefine) : Object
      {
         var td0:MustSimpleThingsDefine = null;
         var obj0:Object = {};
         obj0["coin"] = d0.coin;
         var arr0:Array = d0.getThingsArr();
         if(arr0 is Array)
         {
            for each(td0 in arr0)
            {
               obj0[td0.name] = td0.num;
            }
         }
         return obj0;
      }
      
      private static function fixedObj(obj0:Object, obj2:Object) : void
      {
         var n:* = undefined;
         for(n in obj2)
         {
            if(obj0.hasOwnProperty(n))
            {
               obj0[n] += obj2[n];
            }
            else
            {
               obj0[n] = obj2[n];
            }
         }
      }
      
      private static function getGift(obj0:Object) : GiftAddDefineGroup
      {
         var n:* = undefined;
         var d0:GiftAddDefineGroup = new GiftAddDefineGroup();
         for(n in obj0)
         {
            if(obj0[n] > 0)
            {
               if(n == "coin")
               {
                  d0.addGiftByStr("base;coin;" + obj0[n]);
               }
               else
               {
                  d0.addGiftByStr("things;" + n + ";" + obj0[n]);
               }
            }
         }
         return d0;
      }
   }
}

