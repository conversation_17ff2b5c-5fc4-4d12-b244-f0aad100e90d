package dataAll._app.wilder.define
{
   import com.sounto.utils.ClassProperty;
   
   public class WilderFatherDefine
   {
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var info:String = "";
      
      public var sortIndex:int = 0;
      
      public var wilderArr:Array = [];
      
      public function WilderFatherDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function getUICnName() : String
      {
         var s0:String = "";
         var len0:int = this.cnName.length;
         for(var i:int = 0; i < len0; i++)
         {
            s0 += (i == 0 ? "" : "\n") + this.cnName.charAt(i);
         }
         return s0;
      }
      
      public function getWilderNum() : int
      {
         return this.wilderArr.length;
      }
      
      public function getHeight() : int
      {
         return 160 * Math.ceil(this.getWilderNum() / 4);
      }
   }
}

