package dataAll._app.worldMap.define
{
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.StringMethod;
   import dataAll._base.IO_Define;
   import dataAll.arms.define.ArmsType;
   import dataAll.level.LevelDiffGetting;
   import dataAll.skill.define.SkillDescrip;
   import dataAll.ui.GatherColor;
   import flash.geom.Point;
   import gameAll.drop.bodyDrop.MadBodyDrop;
   
   public class WorldMapDefine implements IO_Define
   {
      public static const INFECTED_AREA:String = "infected_area";
      
      public static const OTHER:String = "other";
      
      public static const TASK:String = "task";
      
      public static const EDIT_ARR:Array = [INFECTED_AREA,OTHER,TASK];
      
      public static const SPACE:String = "space";
      
      public static const SPACE_TASK:String = "spaceTask";
      
      public static var pro_arr:Array = [];
      
      private static const noRainLabelArr:Array = ["室内","地下"];
      
      public var index:int = 0;
      
      public var father:String = "";
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var geogIconUrl:String = "";
      
      public var description:String = "";
      
      public var unlockTask:String = "";
      
      public var geogType:String = "";
      
      public var labelArr:Array = [];
      
      public var noEndlessB:Boolean = false;
      
      public var noTaskB:Boolean = false;
      
      public var lv:int = 0;
      
      public var priority:int = 0;
      
      public var point:Point = new Point();
      
      public var pointer:Point = new Point();
      
      public var levelArr:Array = null;
      
      public var linkArr:Array = [];
      
      public var mustWinShowB:Boolean = false;
      
      public var firstShowB:Boolean = false;
      
      public var demSkillArr:Array = [];
      
      public var demBossSkillArr:Array = [];
      
      public var demNoModeArr:Array = [];
      
      public var diffMax:int = 0;
      
      public var sweepMax:int = 0;
      
      public var swfUrl:String = "";
      
      private var demonDiy:String = "close";
      
      private var demTip:String = "";
      
      private var demSkill8:String = "";
      
      private var demSkillArr9:Array = null;
      
      private var dem9BossArr:Array = null;
      
      public function WorldMapDefine()
      {
         super();
      }
      
      public static function dealMapName(name0:String) : String
      {
         if(name0 == "CavesDeepGeology")
         {
            return "CavesDeep";
         }
         return name0;
      }
      
      public function inData_byXML(xml0:XML, fatherName0:String) : void
      {
         var n:* = undefined;
         var levelArr_xml0:XML = null;
         var level_d0:WorldMapLevelDefine = null;
         ClassProperty.inData_byXML(this,xml0,pro_arr);
         this.father = fatherName0;
         this.lv = xml0.@lv;
         this.diffMax = xml0.@diffMax;
         this.sweepMax = xml0.@sweepMax;
         this.priority = xml0.@priority;
         this.unlockTask = xml0.@unlockTask;
         this.levelArr = [];
         var levelArr_xmlList0:XMLList = xml0.levelArr.level;
         for(n in levelArr_xmlList0)
         {
            levelArr_xml0 = levelArr_xmlList0[n];
            level_d0 = new WorldMapLevelDefine();
            level_d0.inData_byXML(levelArr_xml0);
            this.levelArr.push(level_d0);
         }
         if(this.geogIconUrl == "")
         {
            this.geogIconUrl = "MainUI/" + this.geogType + "Icon";
         }
      }
      
      public function getLevelName() : String
      {
         var d0:WorldMapLevelDefine = this.levelArr[0];
         if(Boolean(d0))
         {
            return d0.name;
         }
         return "";
      }
      
      public function getNormalLevelName() : String
      {
         var d0:WorldMapLevelDefine = null;
         if(this.levelArr.length > 0)
         {
            d0 = this.levelArr[this.levelArr.length - 1];
            return d0.name;
         }
         return "";
      }
      
      public function isShowMapB() : Boolean
      {
         return this.father == INFECTED_AREA;
      }
      
      public function isSpaceB() : Boolean
      {
         return this.father == SPACE || this.father == SPACE_TASK;
      }
      
      public function isGreenIsB() : Boolean
      {
         var d0:WorldMapDefine = null;
         if(this.name.indexOf("Green") == 0)
         {
            d0 = Gaming.defineGroup.worldMap.getDefine("GreenIs1");
            if(this.index >= d0.index)
            {
               return true;
            }
         }
         return false;
      }
      
      public function isXiShanB() : Boolean
      {
         if(this.name.indexOf("XiShan") == 0)
         {
            return true;
         }
         return false;
      }
      
      public function isAfterXingGuB() : Boolean
      {
         var d0:WorldMapDefine = Gaming.defineGroup.worldMap.getDefine(MadBodyDrop.XingGu);
         return this.index > d0.index;
      }
      
      public function isXingGuB() : Boolean
      {
         return this.name == MadBodyDrop.XingGu;
      }
      
      public function dropByDiffB() : Boolean
      {
         return this.isXingGuB() || this.isAfterXingGuB();
      }
      
      public function getSpaceIconUrl() : String
      {
         return "SpaceUI/" + this.name;
      }
      
      public function canEditB() : Boolean
      {
         return true;
      }
      
      public function getRepeatLevelName() : String
      {
         return this.name + "_2";
      }
      
      public function havePointerB() : Boolean
      {
         return !(this.pointer.x == 0 && this.pointer.y == 0);
      }
      
      public function getGripUrl() : String
      {
         if(this.havePointerB())
         {
            return "MainUI/worldMapBtn";
         }
         return "MainUI/worldMapBtn";
      }
      
      public function getRainPro() : Number
      {
         var pro0:Number = 1;
         if(ArrayMethod.onlyOneElementSamePan(this.labelArr,noRainLabelArr))
         {
            pro0 = 0;
         }
         else if(this.isSpaceB())
         {
            pro0 = 0;
         }
         else if(this.labelArr.indexOf("沙漠") >= 0)
         {
            pro0 = 0.1;
         }
         else if(this.isGreenIsB())
         {
            pro0 = 3;
         }
         else if(this.geogType == "hill")
         {
            pro0 = 1.5;
         }
         else if(this.geogType == "lab")
         {
            pro0 = 0.5;
         }
         return pro0 * 0.01;
      }
      
      public function setDemon9Boss(bossCn0:String, skillArr9:Array) : void
      {
         if(bossCn0 != "")
         {
            if(this.dem9BossArr == null)
            {
               this.dem9BossArr = [bossCn0];
            }
         }
         else
         {
            this.dem9BossArr = null;
         }
         this.demSkillArr9 = skillArr9;
      }
      
      public function getDemon9BossCnArr() : Array
      {
         return this.dem9BossArr;
      }
      
      public function setDemonDiy(diyName0:String, skill8:String) : void
      {
         this.demonDiy = diyName0;
         if(skill8 != "")
         {
            this.demSkill8 = skill8;
         }
      }
      
      public function clearDemTip() : void
      {
         this.demTip = "";
      }
      
      public function getDemonDiy() : String
      {
         return this.demonDiy;
      }
      
      public function getDemBossSkillArr(diff0:int) : Array
      {
         var arr0:Array = this.demBossSkillArr;
         var extraArr0:Array = this.getExtraDemBossSkillArr(diff0);
         if(Boolean(extraArr0))
         {
            arr0 = arr0.concat(extraArr0);
         }
         return arr0;
      }
      
      public function getExtraDemBossSkillArr(diff0:int) : Array
      {
         var arr0:Array = null;
         if(diff0 == LevelDiffGetting.getDemonDiff8())
         {
            if(this.demSkill8 != "")
            {
               arr0 = [this.demSkill8];
            }
         }
         else if(diff0 == LevelDiffGetting.getDemonDiff9())
         {
            if(Boolean(this.demSkillArr9))
            {
               arr0 = this.demSkillArr9;
            }
         }
         return arr0;
      }
      
      public function getDemSkillGatherTip() : String
      {
         var s0:String = null;
         var bossStr0:String = null;
         var extraSkillStr0:String = null;
         if(this.demTip == "")
         {
            s0 = "";
            if(this.demBossSkillArr.length > 0)
            {
               bossStr0 = "首领";
               extraSkillStr0 = this.getAllExtraDemSkillTip();
               s0 += StringMethod.addNewLine(s0,extraSkillStr0 + SkillDescrip.getSkillArrGather(this.demBossSkillArr,GatherColor.blueColor,false,true),"<b><yellow 首领修罗技能：/></b>\n");
            }
            if(this.demSkillArr.length > 0)
            {
               s0 = StringMethod.addNewLine(s0,SkillDescrip.getSkillArrGather(this.demSkillArr,GatherColor.blueColor,false,true),"\n<b><greeness 小怪修罗技能：/></b>\n");
            }
            this.demTip = s0;
         }
         return this.demTip;
      }
      
      private function getAllExtraDemSkillTip() : String
      {
         var skillArr0:Array = null;
         var skill0:* = null;
         var diffArr0:Array = null;
         var diffCn0:String = null;
         var diffCnArr0:Array = null;
         var s0:String = "";
         var obj0:Object = {};
         var arr0:Array = [];
         for(var diff0:int = LevelDiffGetting.getDemonDiff8(); diff0 <= LevelDiffGetting.getDemonDiffMax(this); diff0++)
         {
            skillArr0 = this.getExtraDemBossSkillArr(diff0);
            for each(skill0 in skillArr0)
            {
               if(obj0.hasOwnProperty(skill0) == false)
               {
                  obj0[skill0] = [];
                  arr0.unshift(skill0);
               }
               diffArr0 = obj0[skill0];
               diffCn0 = LevelDiffGetting.getCnName(diff0,MapMode.DEMON);
               ArrayMethod.addNoRepeatInArr(diffArr0,diffCn0);
            }
         }
         for each(skill0 in arr0)
         {
            diffCnArr0 = obj0[skill0];
            s0 += "<orange [" + StringMethod.concatStringArr(diffCnArr0,999) + "]/>";
            s0 += SkillDescrip.getSkillArrGather([skill0],GatherColor.blueColor,false,true);
            s0 += "\n";
         }
         return s0;
      }
      
      public function getExtraDemSkillTip(diff0:int) : String
      {
         var str0:String = "";
         var arr0:Array = this.getExtraDemBossSkillArr(diff0);
         if(Boolean(arr0) && arr0.length > 0)
         {
            str0 = SkillDescrip.getSkillArrGather(arr0,GatherColor.blueColor,false,true);
         }
         return str0;
      }
      
      public function getName() : String
      {
         return this.name;
      }
      
      public function getCnName() : String
      {
         return this.cnName;
      }
      
      public function getRareRedType() : String
      {
         if(this.lv == 90 || this.lv == 98)
         {
            return ArmsType.pistol;
         }
         if(this.lv == 91 || this.lv == 96)
         {
            return ArmsType.shotgun;
         }
         if(this.lv == 92 || this.lv == 97)
         {
            return ArmsType.rifle;
         }
         if(this.lv == 93 || this.name == "Hospital5")
         {
            return ArmsType.sniper;
         }
         if(this.lv == 94)
         {
            return ArmsType.rocket;
         }
         if(this.name == "XiShan1")
         {
            return ArmsType.laser;
         }
         return "";
      }
   }
}

