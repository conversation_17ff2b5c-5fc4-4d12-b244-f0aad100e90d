package dataAll._app.union
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.StringMethod;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._app.union.battle.UBattleAgent;
   import dataAll._app.union.battle.UBattleMapAgent;
   import dataAll._app.union.building.UnionBuildingDataGroup;
   import dataAll._app.union.define.MilitaryDefine;
   import dataAll._app.union.extra.UnionExtraGetting;
   import dataAll._app.union.info.MemberInfo;
   import dataAll._app.union.info.MemberListInfo;
   import dataAll._app.union.info.OwnUnionInfo;
   import dataAll._app.union.info.UnionInfo;
   import dataAll._app.union.task.UnionTaskData;
   import dataAll._app.union.task.UnionTaskDefine;
   import dataAll._player.IO_PlayerLevelGetter;
   import dataAll._player.PlayerData;
   import dataAll._player.count.PlayerCountSave;
   import dataAll.equip.EquipPropertyData;
   import dataAll.equip.add.EquipAddChild;
   import dataAll.equip.add.IO_EquipAddGetter;
   import dataAll.equip.add.IO_EquipAddTipGetter;
   import dataAll.equip.define.EquipColor;
   import gameAll.level.data.LevelData;
   
   public class UnionData implements IO_EquipAddTipGetter, IO_EquipAddGetter
   {
      public static var ROLE_B:Boolean = false;
      
      public var playerData:PlayerData;
      
      public var save:UnionSave;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var taskObj:Object = {};
      
      public var nowUnion:UnionInfo = null;
      
      public var nowMember:MemberInfo = null;
      
      public var nowMemberList:MemberListInfo = null;
      
      public var building:UnionBuildingDataGroup = new UnionBuildingDataGroup();
      
      public var extraGet:UnionExtraGetting = new UnionExtraGetting();
      
      public var unionIsZuobiB:Boolean = false;
      
      public var battleAgent:UBattleAgent = new UBattleAgent();
      
      private var attackMapName:String = "";
      
      private var trainMapName:String = "";
      
      private var trainB:Boolean = false;
      
      public function UnionData()
      {
         super();
      }
      
      public static function getContributionCountStrBy(nowTime0:StringDate, conObj0:Object) : String
      {
         var p0:int = 0;
         var con0:int = 0;
         var cn0:String = null;
         var s0:String = null;
         var phase0:int = UnionSave.getPhaseByTime(nowTime0);
         var cnArr0:Array = ["本周","上周","上上周"];
         var strArr0:Array = [];
         for(var i:int = 0; i < 3; i++)
         {
            p0 = phase0 - i;
            if(p0 >= 2)
            {
               con0 = int(conObj0[p0]);
               cn0 = cnArr0[i];
               s0 = cn0 + "贡献：<green " + con0 + "/>";
               strArr0.push(s0);
            }
         }
         var str0:String = "";
         if(strArr0.length > 0)
         {
            str0 += "\n" + StringMethod.concatStringArr(strArr0,1,"\n");
            str0 += "\n<purple 贡献统计数据会有一些误差，仅作为参考。/>";
         }
         return str0;
      }
      
      public static function getWeekContributionBy(nowTime0:StringDate, conObj0:Object, phaseAdd0:int = 0) : int
      {
         var phase0:int = UnionSave.getPhaseByTime(nowTime0) + phaseAdd0;
         return int(conObj0[phase0]);
      }
      
      public function inData_bySave(s0:UnionSave) : void
      {
         this.save = s0;
         this.building.inData_bySaveGroup(s0.building);
         this.taskSaveToData();
      }
      
      public function newDayCtrl(timeDa0:StringDate) : void
      {
         var da0:UnionTaskData = null;
         this.save.newDayCtrl(timeDa0);
         this.building.newDayCtrl();
         for each(da0 in this.taskObj)
         {
            da0.newDayCtrl();
         }
      }
      
      public function newWeek(str0:String) : void
      {
         this.save.newWeek();
      }
      
      public function newWeek6(str0:String) : void
      {
         this.save.newWeek6();
      }
      
      public function setPlayerData(pd0:PlayerData) : void
      {
         this.playerData = pd0;
         this.extraGet.PD = pd0;
         this.extraGet.U = this;
         this.building.playerData = pd0;
      }
      
      public function gotoTaskBoard(nowDate0:StringDate) : Boolean
      {
         var firstDate0:StringDate = null;
         var cDay0:Number = NaN;
         if(this.save.firstTime == "")
         {
            this.save.firstTime = nowDate0.getStr();
         }
         if(!this.isKingB())
         {
            firstDate0 = new StringDate();
            firstDate0.inData_byStr(this.save.firstTime);
            cDay0 = firstDate0.compareDateValue(nowDate0);
            if(cDay0 < 1)
            {
               return true;
            }
         }
         return false;
      }
      
      public function inOwnUnionInfo(i0:OwnUnionInfo) : void
      {
         this.nowUnion = i0.unionInfo;
         i0.member.inBeforeInfo(this.nowMember);
         this.nowMember = i0.member;
         this.save.inUnionId(i0.unionInfo.id);
         if(this.isKingB())
         {
            this.nowUnion.extraObj.kingName = this.playerData.base.save.playerName;
         }
         this.unionIsZuobiB = i0.unionInfo.zuobiPan() != "";
      }
      
      public function inMemberListInfo(u0:MemberListInfo) : void
      {
         this.nowMemberList = u0;
         var battleScore0:int = -1;
         if(this.battleAgent.haveDataB())
         {
            battleScore0 = this.battleAgent.getUnionScore();
         }
         u0.countInUnionInfo(this.nowUnion,battleScore0);
      }
      
      public function clearInfo() : void
      {
         this.nowUnion = null;
         this.nowMember = null;
         this.nowMemberList = null;
         this.save.quitT = "";
      }
      
      public function getDonationGoodsData(name0:String) : GoodsData
      {
         var da0:GoodsData = new GoodsData();
         var d0:GoodsDefine = Gaming.defineGroup.goods.getDefine(name0 + "Donation");
         da0.def = d0;
         da0.playerData = this.playerData;
         da0.showTextType = name0 + "Donation";
         da0.setMaxNumLimit(this.save.getSurplusDonationNum(name0));
         return da0;
      }
      
      public function getContribution() : Number
      {
         if(this.nowMember is MemberInfo)
         {
            return this.nowMember.contribution;
         }
         return 0;
      }
      
      public function getContributionCountStr(nowTime0:StringDate) : String
      {
         var str0:String = "今日贡献：<green " + this.save.conDay + "/>";
         return str0 + getContributionCountStrBy(nowTime0,this.save.conObj);
      }
      
      private function addTaskNum(name0:String) : void
      {
      }
      
      public function getTaskContributionEvent(name0:String) : void
      {
         var da0:UnionTaskData = this.taskObj[name0];
         if(Boolean(da0))
         {
            da0.getTaskContribution();
            this.save.addCountDonation(da0.def.contribution,Gaming.api.save.getNowServerDate());
         }
      }
      
      public function getTaskDataArr() : Array
      {
         var d0:UnionTaskDefine = null;
         var da0:UnionTaskData = null;
         var arr0:Array = Gaming.defineGroup.union.task.arr;
         var arr2:Array = [];
         for each(d0 in arr0)
         {
            da0 = this.taskObj[d0.name];
            da0.nowNum = this.getTaskNowNum(da0.def.name);
            arr2.push(da0);
         }
         return arr2;
      }
      
      public function getTaskDataNum() : int
      {
         return Gaming.defineGroup.union.task.arr.length;
      }
      
      private function getTaskNowNum(name0:String) : int
      {
         var maxLove0:Number = NaN;
         var equipPurgold0:int = 0;
         var armsPurgold0:int = 0;
         var count0:PlayerCountSave = this.playerData.getSave().getCount();
         var MAX0:int = UnionTaskData.MAX_NUM;
         var con0:Number = this.getContribution();
         var maxDps0:Number = this.playerData.main.save.maxDp;
         var num0:int = 0;
         if(name0 == "kingTask")
         {
            num0 = this.playerData.gift.save.daily.todayHaveGetGiftB() ? 1 : 0;
         }
         else if(name0 == "extraTask")
         {
            num0 = this.playerData.task.saveGroup.getTrueTodayCompleteNum("extra");
            if(maxDps0 >= 1000000000)
            {
               num0 = MAX0;
            }
         }
         else if(name0 == "level")
         {
            num0 = count0.normalLevelNum;
            if(maxDps0 >= 500000000)
            {
               num0 = MAX0;
            }
         }
         else if(name0 == "arena")
         {
            num0 = this.playerData.arena.getOutWinNum();
         }
         else if(name0 == "wilder")
         {
            num0 = this.playerData.wilder.getDayNum();
            if(maxDps0 >= 3000000000)
            {
               num0 = MAX0;
            }
         }
         else if(name0 == "loveGift")
         {
            num0 = this.playerData.moreWay.getLoveGiftNumDay();
            maxLove0 = this.playerData.moreWay.getMaxLoveValue();
            if(maxLove0 >= 15000)
            {
               num0 = MAX0;
            }
         }
         else if(name0 == "federal")
         {
            num0 = this.building.saveGroup.federalState > 0 ? 1 : 0;
            if(con0 >= 60000)
            {
               num0 = MAX0;
            }
         }
         else if(name0 == "smelt")
         {
            num0 = this.playerData.city.save.num;
            equipPurgold0 = int(this.playerData.getEquipArrByColorMore(EquipColor.PURGOLD).length);
            if(equipPurgold0 >= 1)
            {
               num0 = MAX0;
            }
         }
         else if(name0 == "activeValue")
         {
            num0 = this.playerData.active.nowActive;
            armsPurgold0 = int(this.playerData.getArmsArrByColorMore(EquipColor.PURGOLD).length);
            if(armsPurgold0 >= 1)
            {
               num0 = MAX0;
            }
         }
         return num0;
      }
      
      public function getTaskBarTip(name0:String) : String
      {
         var maxDps0:Number = NaN;
         var maxLove0:Number = NaN;
         var con0:Number = NaN;
         var equipPurgold0:int = 0;
         var armsPurgold0:int = 0;
         var s0:String = "";
         if(name0 != "kingTask")
         {
            if(name0 == "extraTask" || name0 == "wilder" || name0 == "level")
            {
               maxDps0 = this.playerData.main.save.maxDp;
               s0 += "主角战斗力历史最高为：<yellow " + NumberMethod.toBigWan(maxDps0) + "/>";
            }
            else if(name0 != "arena")
            {
               if(name0 == "loveGift")
               {
                  maxLove0 = this.playerData.moreWay.getMaxLoveValue();
                  s0 += "队友最高的好感度/忠诚度：<yellow " + maxLove0 + "/>";
               }
               else if(name0 == "federal")
               {
                  con0 = this.getContribution();
                  s0 += "当前个人贡献：<yellow " + con0 + "/>";
               }
               else if(name0 == "smelt")
               {
                  equipPurgold0 = int(this.playerData.getEquipArrByColorMore(EquipColor.PURGOLD).length);
                  s0 += "你拥有战神装备（不包括仓库）的数量：<yellow " + equipPurgold0 + "/>件";
               }
               else if(name0 == "activeValue")
               {
                  armsPurgold0 = int(this.playerData.getArmsArrByColorMore(EquipColor.PURGOLD).length);
                  s0 += "你拥有无双武器（不包括仓库）的数量：<yellow " + armsPurgold0 + "/>把";
               }
            }
         }
         return s0;
      }
      
      private function taskSaveToData() : void
      {
         var n:* = undefined;
         var da0:UnionTaskData = null;
         var obj0:Object = {};
         for(n in this.save.taskObj)
         {
            da0 = new UnionTaskData();
            da0.inData_bySave(this.save.taskObj[n],n);
            obj0[n] = da0;
         }
         this.taskObj = obj0;
      }
      
      public function isInUnionB() : Boolean
      {
         return this.nowUnion;
      }
      
      public function topPan() : String
      {
         var must0:Number = 4 * Math.pow(10,3);
         if(Boolean(this.nowUnion) && Boolean(this.nowMember))
         {
            if(this.nowMember.contribution >= must0)
            {
               return "";
            }
         }
         return "加入军队并且贡献超过" + must0 + "，才可上传成绩。";
      }
      
      public function getKingId() : Number
      {
         return this.nowUnion.uId;
      }
      
      public function getMeRoleId() : Number
      {
         return this.nowMember.getRoleID(this.getKingId());
      }
      
      public function isKingB() : Boolean
      {
         return this.nowUnion.uId == this.nowMember.uId;
      }
      
      public function isAdminB() : Boolean
      {
         return this.nowMember.roleId == 1;
      }
      
      public function isViceKingB() : Boolean
      {
         return this.nowMember.roleId == 27;
      }
      
      public function canNoticeB() : Boolean
      {
         return this.isKingB() || this.isViceKingB() || ROLE_B;
      }
      
      public function canUploadUnionB() : Boolean
      {
         return this.isKingB() || this.isViceKingB() || ROLE_B;
      }
      
      public function canSetRoleB() : Boolean
      {
         return this.isKingB() || this.isViceKingB() || ROLE_B;
      }
      
      public function haveCheckRightB() : Boolean
      {
         return this.isKingB() || this.isAdminB() || this.isViceKingB();
      }
      
      public function getProAddObj() : Object
      {
         return this.getAddData();
      }
      
      public function getProAddMaxObj(pro0:String) : Object
      {
         return null;
      }
      
      public function getAddData() : EquipPropertyData
      {
         var da0:EquipPropertyData = new EquipPropertyData();
         if(Boolean(this.nowUnion))
         {
            da0.dpsMul += this.nowUnion.getDpsMul();
         }
         if(Boolean(this.nowMember))
         {
            da0.dpsMul += this.nowMember.getDpsMul();
            da0.dpsMul += this.nowMember.getBatDps();
            da0.lifeMul += this.nowMember.getBatLife();
            da0.dpsMul += this.battleAgent.getUnionDpsMul();
         }
         da0.addData(this.building.getAddObj());
         return da0;
      }
      
      public function getProAddTipObj() : Object
      {
         var batObj0:Object = null;
         var tipObj0:Object = {};
         if(Boolean(this.nowUnion))
         {
            EquipAddChild.addTipInObj({"dpsMul":this.nowUnion.getDpsMul()},tipObj0,"军队");
         }
         if(Boolean(this.nowMember))
         {
            EquipAddChild.addTipInObj({"dpsMul":this.nowMember.getDpsMul()},tipObj0,"军衔");
            batObj0 = {};
            batObj0.dpsMul = this.nowMember.getBatDps();
            batObj0.lifeMul = this.nowMember.getBatLife();
            EquipAddChild.addTipInObj(batObj0,tipObj0,"个人争霸");
            EquipAddChild.addTipInObj({"dpsMul":this.battleAgent.getUnionDpsMul()},tipObj0,"军队争霸");
         }
         this.building.inProAddTipObj(tipObj0);
         return tipObj0;
      }
      
      public function getSkillLabelArr() : Array
      {
         var arr0:Array = [];
         return arr0.concat(this.building.getSkillLabelArr());
      }
      
      public function getDpsMulString(zeroPanB0:Boolean = false) : String
      {
         var militaryD0:MilitaryDefine = null;
         var str0:String = "";
         var u0:UnionInfo = this.nowUnion;
         var m0:MemberInfo = this.nowMember;
         if(Boolean(this.nowUnion))
         {
            if(this.nowUnion.getDpsMul() > 0 || !zeroPanB0)
            {
               str0 += "\n军队对人物的战斗力加成：<yellow " + u0.getDpsMulString() + "/>";
            }
         }
         if(Boolean(this.nowMember))
         {
            militaryD0 = m0.getMilitaryDefine();
            if(this.nowMember.getDpsMul() > 0 || !zeroPanB0)
            {
               str0 += "\n军衔对人物的战斗力加成：<yellow " + militaryD0.getDpsMulString() + "/>";
            }
         }
         return str0;
      }
      
      public function getLifeMulString() : String
      {
         return "";
      }
      
      public function getEquipRareMulString() : String
      {
         return "";
      }
      
      public function getTodayGift() : void
      {
         this.save.todayGiftB = true;
         ++this.save.building.daySuppliesNum;
      }
      
      public function removeMemberEvent() : void
      {
         ++this.save.removeNum;
      }
      
      public function getCanRemoveNum() : int
      {
         var num0:int = this.getRemoveMax() - this.save.removeNum;
         if(num0 <= 0)
         {
            num0 = 0;
         }
         return num0;
      }
      
      public function getRemoveMax() : int
      {
         if(this.isKingB() || this.isViceKingB())
         {
            return 10;
         }
         if(this.isAdminB())
         {
            return 5;
         }
         return 0;
      }
      
      public function getRemoveErrorTip() : String
      {
         var can0:int = this.getCanRemoveNum();
         if(can0 <= 0)
         {
            return "无法操作！你今天已经踢出" + this.save.removeNum + "个成员！";
         }
         return "";
      }
      
      public function cancelQuit() : void
      {
         this.save.quitT = "";
      }
      
      public function applyQuit() : void
      {
         this.save.quitT = this.playerData.time.getReadTime();
      }
      
      public function getQuitApplyDay() : int
      {
         if(this.save.quitT == "")
         {
            return -1;
         }
         return this.playerData.time.getReadTimeDate().reductionOneStr(this.save.quitT);
      }
      
      public function getQuitApplyMax() : int
      {
         return 3;
      }
      
      public function isBattleB(nowMap0:String) : Boolean
      {
         if(this.attackMapName == nowMap0)
         {
            return true;
         }
         if(this.trainMapName == nowMap0)
         {
            return true;
         }
         return false;
      }
      
      public function getBattleScore() : int
      {
         return this.battleAgent.getUnionScore();
      }
      
      public function chooseMapEvent(a0:UBattleMapAgent, severTime0:String) : void
      {
         this.nowMember.chooseMapEvent(a0,severTime0);
      }
      
      public function clearMapData() : void
      {
         this.nowMember.clearMapData();
      }
      
      public function gotoTrain(map0:String) : void
      {
         this.clearMapData();
         this.attackMapName = "";
         this.trainMapName = map0;
      }
      
      public function isTrainB() : Boolean
      {
         return this.trainMapName != "";
      }
      
      public function chooseLevelEvent(map0:String) : void
      {
         this.attackMapName = map0;
         this.save.mp = map0;
         this.trainMapName = "";
      }
      
      public function killBossFlesher(lg0:IO_PlayerLevelGetter) : Boolean
      {
         if(this.isTrainB())
         {
            return false;
         }
         var uploadB0:Boolean = false;
         var map0:String = lg0.getNowWorldMapName();
         var levelDat0:LevelData = lg0.getLevelDataNull();
         if(this.attackMapName != "" && this.attackMapName == map0 && Boolean(levelDat0))
         {
            this.nowMember.extraObj.lt = levelDat0.getFixedLevelTime();
            this.save.lt = levelDat0.getFixedLevelTime();
            uploadB0 = true;
         }
         return uploadB0;
      }
      
      public function overLevelEvent(lg0:IO_PlayerLevelGetter) : void
      {
         this.attackMapName = "";
         this.trainMapName = "";
      }
      
      public function getAttackMapName() : String
      {
         return this.attackMapName;
      }
      
      public function getAttackOrTrainMapName() : String
      {
         if(this.attackMapName != "")
         {
            return this.attackMapName;
         }
         if(this.trainMapName != "")
         {
            return this.trainMapName;
         }
         return "";
      }
      
      public function getBattleGiftEvent() : void
      {
         this.save.bGiftB = true;
         ++this.save.bgNum;
      }
      
      public function FTimerSecond(lg0:IO_PlayerLevelGetter) : void
      {
         this.building.FTimerSecond(lg0);
      }
   }
}

