package dataAll._app.edit.card
{
   import UI.test.SaveTestBox;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ObjectMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._base.OneData;
   import dataAll._base.OneDataGroup;
   import dataAll._base.OneSaveGroup;
   import dataAll._player.IO_PlayerLevelGetter;
   import dataAll._player.PlayerData;
   import dataAll.equip.add.EquipAddChild;
   import dataAll.equip.add.IO_EquipAddGetter;
   import dataAll.equip.add.IO_EquipAddTipGetter;
   import dataAll.equip.creator.EquipPropertyDataCreator;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.level.define.LevelDefine;
   import dataAll.level.define.event.LevelEventOrderDefine;
   import dataAll.pro.PropertyArrayDefine;
   import dataAll.ui.GatherColor;
   import gameAll.body.IO_NormalBody;
   
   public class BossCardDataGroup extends OneDataGroup implements IO_EquipAddTipGetter, IO_EquipAddGetter
   {
      private var cardSaveG:BossCardSaveGroup = null;
      
      protected var playerData:PlayerData;
      
      private var tempFightData:BossCardData = null;
      
      private var killBossB:Boolean = false;
      
      public var addCardB:Boolean = false;
      
      private var wePKDa:BossCardData = null;
      
      private var enemyPKDa:BossCardData = null;
      
      private var hsvMul:Number = -1;
      
      private var sortIndex:int = 0;
      
      private var tempTipObj:Object = null;
      
      private var tempMaxObj:Object = null;
      
      public function BossCardDataGroup()
      {
         super();
         dataClass = BossCardData;
      }
      
      public static function GetPkStoneTime() : String
      {
         return "2025-1-13";
      }
      
      public static function GetPkStoneNum() : int
      {
         return 2;
      }
      
      public static function get HIGH_MUST() : Number
      {
         return 100;
      }
      
      public static function get DRAW7_MUST() : Number
      {
         return 500;
      }
      
      private static function getLevelLimitMax() : int
      {
         return 12;
      }
      
      private static function getDemLimitMax() : int
      {
         return 2;
      }
      
      public function setPlayerData(pd0:PlayerData) : void
      {
         this.playerData = pd0;
      }
      
      override public function inData_bySave(sg0:OneSaveGroup) : void
      {
         this.cardSaveG = sg0 as BossCardSaveGroup;
         super.inData_bySave(sg0);
         this.tempFightData = this.getFightData();
      }
      
      public function newWeek() : void
      {
         this.cardSaveG.newWeek();
      }
      
      public function newDayCtrl(timeDa0:StringDate) : void
      {
         this.cardSaveG.newDayCtrl();
      }
      
      override protected function insideAddData(da0:OneData) : void
      {
         super.addData(da0);
         var da2:BossCardData = da0 as BossCardData;
         da2.setFatherData(this);
      }
      
      override public function addData(da0:OneData) : void
      {
         super.addData(da0);
         var da2:BossCardData = da0 as BossCardData;
         da2.setFatherData(this);
         da2.newB = true;
      }
      
      public function addByDefId(id0:String) : void
      {
         var da0:BossCardData = null;
         var s0:BossCardSave = null;
         da0 = BCardPKCreator.getData(id0);
         if(Boolean(da0))
         {
            s0 = new BossCardSave();
            s0.inData_byObj(da0.getCardSave());
            da0 = addSave(s0) as BossCardData;
            da0.lockB = true;
            da0.toGiftID();
         }
      }
      
      public function getDataById(id0:String) : BossCardData
      {
         var da0:BossCardData = null;
         for each(da0 in arr)
         {
            if(da0.id == id0)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function getUIAddGather(maxLine0:int = -1) : String
      {
         var s0:String = null;
         var sarr0:Array = null;
         var i:int = 0;
         var obj0:Object = this.getProAddObj();
         if(Boolean(obj0))
         {
            if(ObjectMethod.getObjElementNum(obj0) > 0)
            {
               s0 = EquipPropertyDataCreator.getText_byObjNoColor(obj0,null,true,this.gatherProLastFun);
               if(maxLine0 > 0)
               {
                  sarr0 = s0.split("\n");
                  s0 = "";
                  for(i = 0; i < maxLine0; i++)
                  {
                     if(s0 != "")
                     {
                        s0 += "\n";
                     }
                     s0 += sarr0[i];
                  }
               }
               return s0;
            }
         }
         return "无";
      }
      
      private function gatherProLastFun(d0:PropertyArrayDefine, s0:String, v0:*) : String
      {
         var ls0:String = null;
         var v00:Number = NaN;
         var max0:Number = BossCardCreator.getRanProMax(d0.name);
         if(max0 > 0 && v0 is Number)
         {
            ls0 = " (上限" + d0.getValueString(max0,false) + ")";
            v00 = v0 as Number;
            if(v00 >= max0)
            {
               ls0 = " <b><green " + ls0 + "/></b>";
            }
            else
            {
               ls0 = " <blue " + ls0 + "/>";
            }
            s0 += ls0;
         }
         return s0;
      }
      
      public function getProAddObj() : Object
      {
         var da0:BossCardData = null;
         var tipObj0:Object = null;
         var n:* = undefined;
         var o0:Object = null;
         var now0:Number = NaN;
         var before0:Number = NaN;
         var proObj0:Object = null;
         if(this.isBugMax())
         {
            return null;
         }
         var obj0:Object = {};
         var cardObj0:Object = {};
         for each(da0 in arr)
         {
            da0.clearMaxPro();
            o0 = da0.getAddObj();
            for(n in o0)
            {
               now0 = Number(o0[n]);
               before0 = 0;
               if(obj0.hasOwnProperty(n))
               {
                  before0 = Number(obj0[n]);
               }
               if(before0 < now0)
               {
                  obj0[n] = now0;
                  cardObj0[n] = da0;
               }
            }
         }
         tipObj0 = {};
         for(n in cardObj0)
         {
            da0 = cardObj0[n];
            da0.addMaxPro(n);
            proObj0 = {};
            proObj0[n] = obj0[n];
            EquipAddChild.addTipInObj(proObj0,tipObj0,da0.cnName);
         }
         this.tempTipObj = tipObj0;
         return obj0;
      }
      
      public function getProAddMaxObj(pro0:String) : Object
      {
         var obj0:Object = null;
         var max0:Number = NaN;
         if(this.tempMaxObj == null)
         {
            this.tempMaxObj = {};
         }
         if(this.tempMaxObj.hasOwnProperty(pro0) == false)
         {
            obj0 = {};
            max0 = BossCardCreator.getRanProMax(pro0);
            EquipAddChild.IN_MaxObj(obj0,max0);
            this.tempMaxObj[pro0] = obj0;
         }
         return this.tempMaxObj[pro0];
      }
      
      public function getProAddTipObj() : Object
      {
         return this.tempTipObj;
      }
      
      public function getAddObj2() : Object
      {
         var da0:BossCardData = null;
         var o0:Object = null;
         var n:* = undefined;
         var now0:Number = NaN;
         var before0:Number = NaN;
         if(this.isBugMax())
         {
            return null;
         }
         var obj0:Object = {};
         for each(da0 in arr)
         {
            o0 = da0.getAddObj();
            for(n in o0)
            {
               now0 = Number(o0[n]);
               before0 = 0;
               if(obj0.hasOwnProperty(n))
               {
                  before0 = Number(obj0[n]);
               }
               if(before0 < now0)
               {
                  obj0[n] = now0;
               }
            }
         }
         return obj0;
      }
      
      public function sortByStar() : void
      {
         this.sortIndex = (this.sortIndex + 1) % 2;
         if(this.sortIndex == 0)
         {
            arr.sort(this.sortByStarFun);
         }
         else if(this.sortIndex == 1)
         {
            arr.sort(this.sortByNameFun);
         }
         this.fleshSaveGroup();
      }
      
      private function sortByStarFun(a0:BossCardData, b0:BossCardData) : int
      {
         var v0:int = 0;
         var as0:int = a0.getStar();
         var bs0:int = b0.getStar();
         if(as0 == bs0)
         {
            v0 = this.sortGiftFirst(a0,b0);
            if(v0 != 0)
            {
               return v0;
            }
            return ArrayMethod.sortObjectFun(a0.getBodyName(),b0.getBodyName());
         }
         return ArrayMethod.sortNumberFun(bs0,as0);
      }
      
      private function sortByNameFun(a0:BossCardData, b0:BossCardData) : int
      {
         var v0:int = this.sortGiftFirst(a0,b0);
         if(v0 != 0)
         {
            return v0;
         }
         var as0:String = a0.getBodyName();
         var bs0:String = b0.getBodyName();
         if(as0 == bs0)
         {
            return ArrayMethod.sortNumberFun(b0.getStar(),a0.getStar());
         }
         return ArrayMethod.sortObjectFun(as0,bs0);
      }
      
      private function sortGiftFirst(a0:BossCardData, b0:BossCardData) : int
      {
         var ag1:Boolean = a0.isGiftID();
         var bg1:Boolean = b0.isGiftID();
         if(ag1 && !bg1)
         {
            return -1;
         }
         if(!ag1 && bg1)
         {
            return 1;
         }
         return 0;
      }
      
      public function removeLessStar(star0:int) : int
      {
         var da0:BossCardData = null;
         var delB0:Boolean = false;
         if(star0 > 5)
         {
            return 0;
         }
         var num0:int = 0;
         var narr0:Array = [];
         var sarr0:Array = [];
         for each(da0 in arr)
         {
            delB0 = false;
            if(da0.lockB == false)
            {
               if(da0.getStar() <= star0)
               {
                  if(da0.id != this.cardSaveG.fid)
                  {
                     delB0 = true;
                     num0++;
                  }
               }
            }
            if(delB0 == false)
            {
               narr0.push(da0);
               sarr0.push(da0.getCardSave());
            }
         }
         if(num0 > 0)
         {
            this.cardSaveG.setNewArr(sarr0);
            arr = narr0;
         }
         return num0;
      }
      
      private function fleshSaveGroup() : void
      {
         var da0:BossCardData = null;
         var sarr0:Array = [];
         for each(da0 in arr)
         {
            sarr0.push(da0.getCardSave());
         }
         this.cardSaveG.setNewArr(sarr0);
      }
      
      public function overGamingClear() : void
      {
         var da0:BossCardData = null;
         for each(da0 in arr)
         {
            da0.overGamingClear();
         }
         if(this.killBossB)
         {
            if(this.cardSaveG.autoDown)
            {
               this.toDown();
            }
         }
         this.killBossB = false;
         this.addCardB = false;
      }
      
      public function startLevelEvent() : void
      {
         this.killBossB = false;
      }
      
      public function bodyDieEvent(b0:IO_NormalBody, lg0:IO_PlayerLevelGetter) : void
      {
         var bodyArr0:Array = null;
         if(lg0.isDemonB() == false)
         {
            if(this.killBossB == false)
            {
               bodyArr0 = this.getFightBodyArr();
               if(bodyArr0.length > 0)
               {
                  if(b0.getDieCtrl().isTrueBody())
                  {
                     if(b0.getData().isEnemyBossB())
                     {
                        this.killBossB = true;
                        if(lg0.isXingGu())
                        {
                           ++this.cardSaveG.demV;
                        }
                        else
                        {
                           ++this.cardSaveG.v;
                        }
                     }
                  }
               }
            }
         }
      }
      
      public function demonDieEvent() : void
      {
         var bodyArr0:Array = null;
         if(this.killBossB == false)
         {
            bodyArr0 = this.getFightBodyArr();
            if(bodyArr0.length > 0)
            {
               this.killBossB = true;
               ++this.cardSaveG.demV;
            }
         }
      }
      
      public function evoEvent(da0:BossCardData) : void
      {
         var star0:int = da0.getStar();
         var sg0:BossCardSaveGroup = this.getSaveG();
         if(star0 == 6)
         {
            ++sg0.evo6;
         }
         else if(star0 == 7)
         {
            ++sg0.evo7;
         }
         else if(star0 == 8)
         {
            ++sg0.evo8;
         }
      }
      
      public function getSaveG() : BossCardSaveGroup
      {
         return this.cardSaveG;
      }
      
      public function addBagNum(v0:int) : void
      {
         this.cardSaveG.bag += v0;
      }
      
      public function getBagMax() : int
      {
         return 100 + this.cardSaveG.bag;
      }
      
      public function getBagSurplus() : int
      {
         return this.getBagMax() - this.getBagNum();
      }
      
      public function getBagNum() : int
      {
         return arr.length;
      }
      
      public function outAddDraw(num0:int) : void
      {
         this.cardSaveG.on += num0;
      }
      
      public function getDrawMax() : int
      {
         return 40 + this.playerData.bossEdit.getSave().bs + this.getPKDrawAdd() + this.cardSaveG.on + this.getDrawGoodsNum();
      }
      
      public function getDrawNum() : int
      {
         return this.cardSaveG.num;
      }
      
      public function getDrawSurplus() : int
      {
         return this.getDrawMax() - this.getDrawNum();
      }
      
      public function getDrawCan() : int
      {
         var n1:int = this.getDrawSurplus();
         var n2:int = this.getBagSurplus();
         if(n1 < n2)
         {
            return n1;
         }
         return n2;
      }
      
      private function getDrawGoodsNum() : int
      {
         return this.playerData.thingsBag.getThingsNum("bossCardStamp");
      }
      
      public function drawEvent(highB0:Boolean) : void
      {
         if(highB0)
         {
            ++this.cardSaveG.hNum;
         }
         else
         {
            ++this.cardSaveG.num;
         }
      }
      
      public function drawEvent7() : void
      {
         ++this.cardSaveG.s7N;
      }
      
      public function getBossSumNum() : Number
      {
         var now0:int = this.getDrawNum();
         var num0:int = (now0 - 4000) / 30;
         if(num0 < 0)
         {
            num0 = 0;
         }
         return num0;
      }
      
      public function getBossSumGetted() : Number
      {
         return this.cardSaveG.sumN;
      }
      
      public function bossSumGetEvent() : void
      {
         this.cardSaveG.sumN = this.getBossSumNum();
      }
      
      public function getBossSumStr() : String
      {
         return "超过4000次抽卡次数之后，每增加30次抽卡次数，将获得1张首领召唤卡。当前可获得" + this.getBossSumNum() + "张，已领取" + this.getBossSumGetted() + "张。";
      }
      
      public function getBossSumGift() : GiftAddDefineGroup
      {
         var can0:int = this.getBossSumNum() - this.getBossSumGetted();
         var g0:GiftAddDefineGroup = new GiftAddDefineGroup();
         g0.addGiftByStr("things;bossSumCard;" + can0);
         return g0;
      }
      
      public function getHighDrawMax() : int
      {
         return Math.floor(this.getDrawNum() / HIGH_MUST);
      }
      
      public function getHighDrawNum() : int
      {
         return this.cardSaveG.hNum;
      }
      
      public function getHighDrawSurplus() : int
      {
         return this.getHighDrawMax() - this.getHighDrawNum();
      }
      
      public function getHighDrawCan() : int
      {
         var n1:int = this.getHighDrawSurplus();
         var n2:int = this.getBagSurplus();
         if(n1 < n2)
         {
            return n1;
         }
         return n2;
      }
      
      public function get7DrawMax() : int
      {
         return Math.floor(this.getDrawNum() / DRAW7_MUST);
      }
      
      public function get7DrawNum() : int
      {
         return this.cardSaveG.s7N;
      }
      
      public function get7DrawSurplus() : int
      {
         return this.get7DrawMax() - this.get7DrawNum();
      }
      
      public function get7DrawCan() : int
      {
         var n1:int = this.get7DrawSurplus();
         var n2:int = this.getBagSurplus();
         if(n1 < n2)
         {
            return n1;
         }
         return n2;
      }
      
      public function toFight(da0:BossCardData) : void
      {
         this.cardSaveG.fid = da0.id;
         this.tempFightData = da0;
      }
      
      public function toDown(da0:BossCardData = null) : void
      {
         this.cardSaveG.fid = "";
         this.tempFightData = null;
      }
      
      public function fightPan(da0:BossCardData) : Boolean
      {
         if(this.cardSaveG.fid != "")
         {
            if(da0.id == this.cardSaveG.fid)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getFightData() : BossCardData
      {
         var id0:String = this.cardSaveG.fid;
         if(id0 != "")
         {
            return this.getDataById(id0);
         }
         return null;
      }
      
      public function getUITimeTxt() : String
      {
         var st0:Number = this.getTimeSurplus();
         var s0:String = "今日剩余出战时间\n";
         var color0:String = st0 > 0 ? GatherColor.blueColor : GatherColor.redColor;
         s0 += ComMethod.color(ComMethod.getTimeStrTwo(st0,true),color0);
         s0 += "\n\n每周取胜次数";
         s0 += "\n" + ComMethod.color("·普通关卡 ",GatherColor.gray2Color) + TextMethod.colorSurplusNum(this.cardSaveG.v,getLevelLimitMax());
         return s0 + ("\n" + ComMethod.color("·修罗及星谷地图 ",GatherColor.gray2Color) + TextMethod.colorSurplusNum(this.cardSaveG.demV,getDemLimitMax()));
      }
      
      private function timeAndVPan(lg0:IO_PlayerLevelGetter) : Boolean
      {
         if(this.getTimeSurplus() <= 0)
         {
            return false;
         }
         if(lg0.isActiveTaskB())
         {
            return false;
         }
         if(lg0.isDemonOrXingGu())
         {
            if(this.cardSaveG.demV >= getDemLimitMax())
            {
               return false;
            }
         }
         else if(this.cardSaveG.v >= getLevelLimitMax())
         {
            return false;
         }
         return true;
      }
      
      public function getTimeMax() : Number
      {
         return 1200;
      }
      
      public function getTimeSurplus() : Number
      {
         var c0:Number = this.getTimeMax() - this.cardSaveG.t;
         if(c0 < 0)
         {
            c0 = 0;
         }
         return c0;
      }
      
      public function bodyTimerSecond(da0:BossCardData) : void
      {
         this.cardSaveG.t += 1;
      }
      
      public function get autoDownB() : Boolean
      {
         return this.cardSaveG.autoDown;
      }
      
      public function set autoDownB(v0:Boolean) : void
      {
         this.cardSaveG.autoDown = v0;
      }
      
      public function getFightArr(lg0:IO_PlayerLevelGetter) : Array
      {
         var da0:BossCardData = null;
         var arr0:Array = [];
         if(this.timeAndVPan(lg0))
         {
            da0 = this.getFightData();
            if(Boolean(da0))
            {
               arr0.push(da0);
            }
         }
         return arr0;
      }
      
      public function getFightBodyArr() : Array
      {
         var b0:IO_NormalBody = null;
         var arr2:Array = [];
         if(Boolean(this.tempFightData))
         {
            b0 = this.tempFightData.getFightBody();
            if(Boolean(b0))
            {
               arr2.push(b0);
            }
         }
         return arr2;
      }
      
      public function getWePKDa() : BossCardData
      {
         return this.wePKDa;
      }
      
      public function getEnemyPKDa() : BossCardData
      {
         return this.enemyPKDa;
      }
      
      public function getPKPreArr(levelD0:LevelDefine) : Array
      {
         var arr0:Array = [];
         if(levelD0.name == BCardPKCreator.bossCardPK)
         {
            if(Boolean(this.wePKDa))
            {
               arr0.push(this.wePKDa);
            }
            if(Boolean(this.enemyPKDa))
            {
               arr0.push(this.enemyPKDa);
            }
         }
         return arr0;
      }
      
      public function getPKArr() : Array
      {
         var da0:BossCardData = null;
         var arr0:Array = [];
         for each(da0 in arr)
         {
            if(da0.getStar() >= this.cardSaveG.starF)
            {
               arr0.push(da0);
               da0.pkB = this.getWePKB(da0.id);
            }
         }
         return arr0;
      }
      
      public function fleshEnemyPKArr() : Array
      {
         var da0:BossCardData = null;
         var index0:int = this.playerData.time.getWeekIndex() - 68;
         var sarr0:Array = BCardPKCreator.getArrByFather(index0 + "");
         if(Boolean(sarr0))
         {
            for each(da0 in sarr0)
            {
               da0.pkB = this.getEnemyPKB(da0.id);
            }
            return sarr0;
         }
         return [];
      }
      
      private function getEnemyPKB(id0:String) : Boolean
      {
         return this.cardSaveG.emPk.indexOf(id0) >= 0;
      }
      
      private function getWePKB(id0:String) : Boolean
      {
         return this.cardSaveG.wePk.indexOf(id0) >= 0;
      }
      
      public function startPK(we0:BossCardData, enemy0:BossCardData) : void
      {
         this.wePKDa = we0;
         this.enemyPKDa = enemy0;
      }
      
      public function pkWinEvent(led0:LevelEventOrderDefine) : void
      {
         var star0:int = 0;
         var cday0:int = 0;
         var num0:int = 0;
         if(Boolean(this.wePKDa))
         {
            ArrayMethod.addNoRepeatInArr(this.cardSaveG.wePk,this.wePKDa.id);
         }
         if(Boolean(this.enemyPKDa))
         {
            star0 = this.enemyPKDa.getStar();
            ArrayMethod.addNoRepeatInArr(this.cardSaveG.emPk,this.enemyPKDa.id);
            ++this.cardSaveG.pkWin;
            this.cardSaveG.pkWinStar += star0;
            if(star0 >= 8)
            {
               if(Boolean(this.playerData))
               {
                  cday0 = this.playerData.time.getReadTimeDate().reductionOneStr(GetPkStoneTime());
                  if(cday0 >= 0)
                  {
                     num0 = GetPkStoneNum();
                     this.cardSaveG.pkStone += num0;
                     this.playerData.thingsBag.addDataByName("ghostStone",num0);
                  }
               }
            }
         }
         this.wePKDa = null;
         this.enemyPKDa = null;
      }
      
      public function swapStarFilter() : void
      {
         this.cardSaveG.starF = this.cardSaveG.starF % 5 + 1;
      }
      
      public function getStarFilter() : int
      {
         return this.cardSaveG.starF;
      }
      
      public function getPKWinNum() : int
      {
         return this.cardSaveG.emPk.length;
      }
      
      public function getPKWeekNum() : int
      {
         return 10;
      }
      
      public function getPKEnemyMax() : int
      {
         return (this.playerData.time.getWeekIndex() - 68 + 1) * this.getPKWeekNum();
      }
      
      public function getPKWinAll() : int
      {
         return this.cardSaveG.pkWin;
      }
      
      public function getPKDrawAdd() : int
      {
         return this.cardSaveG.pkWinStar;
      }
      
      public function getAchivevGiftGetted(da0:BossCardData) : Boolean
      {
         var saveId0:String = da0.getAchieveGiftSaveId();
         return this.cardSaveG.giftIdA.indexOf(saveId0) >= 0;
      }
      
      public function setAchivevGiftGetted(da0:BossCardData, bb0:Boolean) : Boolean
      {
         var saveId0:String = da0.getAchieveGiftSaveId();
         return ArrayMethod.addNoRepeatInArr(this.cardSaveG.giftIdA,saveId0);
      }
      
      public function isBug() : Boolean
      {
         return this.getHSVMul() >= 100;
      }
      
      public function getBugOpenMust() : Number
      {
         var hsv0:Number = this.getHSV();
         var h0:Number = this.getHighDrawNum() * (BossCardCreator.getCardPro(6,true) + 10 * BossCardCreator.getCardPro(7,true));
         var n0:Number = BossCardCreator.getCardPro(6,false) + 10 * BossCardCreator.getCardPro(7,false);
         var n:Number = (hsv0 / 5 - h0) / n0;
         return Math.ceil(n) + 1;
      }
      
      public function getBugOpenTip() : String
      {
         return "你的抽卡次数可能要到达" + this.getBugOpenMust() + "次，才能开放该功能。";
      }
      
      public function isBugMax() : Boolean
      {
         return this.getHSVMul() >= 400;
      }
      
      public function getHSVMul() : Number
      {
         var v0:Number = NaN;
         var line0:Number = NaN;
         var s0:String = null;
         if(this.hsvMul == -1)
         {
            v0 = this.getHSV();
            line0 = this.getHSVLine();
            if(line0 > 0)
            {
               this.hsvMul = v0 / line0;
            }
            else if(v0 > 0)
            {
               this.hsvMul = 9999;
            }
            else
            {
               this.hsvMul = 0;
            }
            s0 = "v:" + v0 + "  line:" + Number(line0).toFixed(3) + "  mul:" + this.hsvMul;
            SaveTestBox.addText(s0);
         }
         return this.hsvMul;
      }
      
      private function getHSV() : Number
      {
         var da0:BossCardData = null;
         var star0:int = 0;
         var v0:Number = 0;
         for each(da0 in arr)
         {
            if(da0.isGiftID() == false)
            {
               star0 = da0.getStar();
               if(star0 >= 7)
               {
                  if(da0.getCardSave().getOValueArg() >= 0.1)
                  {
                     v0 += 30;
                  }
                  else
                  {
                     v0 += 10;
                  }
               }
               else if(star0 == 6)
               {
                  v0 += 1;
               }
            }
         }
         return v0;
      }
      
      private function getHSVLine() : Number
      {
         var num0:int = this.getDrawNum();
         if(num0 < 300)
         {
            num0 = 300;
         }
         var high0:int = this.getHighDrawNum();
         var v6:Number = num0 * BossCardCreator.getCardPro(6,false) + high0 * BossCardCreator.getCardPro(6,true);
         var v7:Number = num0 * BossCardCreator.getCardPro(7,false) + high0 * BossCardCreator.getCardPro(7,true);
         return v7 * 10 + v6;
      }
   }
}

