package dataAll._app.union.info
{
   public class UnionInfoGroup
   {
      private var listInfo:UnionListInfo;
      
      public function UnionInfoGroup()
      {
         super();
      }
      
      public function init() : void
      {
         this.listInfo = UnionListInfo.getSimulated(14,2000);
      }
      
      public function getUnionList(idx:int, pageNum:int, pageSize:int) : String
      {
         return this.listInfo.getSimulatedJson(pageNum,pageSize);
      }
      
      public function unionCreate(idx:int, title:String, extra:String) : void
      {
         var i0:UnionInfo = UnionInfo.getSimulated(0);
         i0.title = title;
         i0.extra = extra;
         this.listInfo.unionList.unshift(i0);
      }
   }
}

