package com.sounto.utils
{
   import com.adobe.serialization.json.JSON2;
   import com.common.data.Base64;
   
   public class XmlMethod
   {
      public function XmlMethod()
      {
         super();
      }
      
      public static function xmlToStringArr(xml0:XML) : Array
      {
         var str0:String = null;
         if(Boolean(xml0))
         {
            str0 = String(StringMethod.toHan2(String(xml0)));
            if(str0 == "" || str0 == "null")
            {
               return [];
            }
            return str0.split(",");
         }
         return [];
      }
      
      public static function xmlJsonToObj(objStr0:String, obj0:Object) : Object
      {
         if(objStr0 != "")
         {
            objStr0 = "{" + objStr0 + "}";
            obj0 = JSON2.decode(objStr0);
         }
         return obj0;
      }
      
      public static function inSwfName(swfName0:String, url0:String) : String
      {
         if(url0.indexOf("/") == -1)
         {
            url0 = swfName0 + "/" + url0;
         }
         return url0;
      }
      
      public static function xmlInObj(xml0:XML, obj0:Object) : void
      {
         var n:* = undefined;
         for(n in obj0)
         {
            xml0["@" + n] = obj0[n];
         }
      }
      
      public static function objToXml(obj0:Object, title0:String = "body") : XML
      {
         var xml0:XML = new XML("<" + title0 + "/>");
         xmlInObj(xml0,obj0);
         return xml0;
      }
      
      public static function jsonToXml(json0:String) : XML
      {
         var obj0:Object = JSON2.decode(json0);
         return objToXml(obj0);
      }
      
      public static function base64ToXml(base64:String) : XML
      {
         var obj0:Object = Base64.decodeObject(base64);
         return objToXml(obj0);
      }
   }
}

