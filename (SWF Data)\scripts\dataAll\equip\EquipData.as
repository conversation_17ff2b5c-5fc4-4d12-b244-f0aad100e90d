package dataAll.equip
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ObjectMethod;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._player.IO_PlayerLevelGetter;
   import dataAll._player.PlayerData;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.body.define.HeroDefine;
   import dataAll.equip.creator.EquipEvoCtrl;
   import dataAll.equip.creator.EquipPropertyDataCreator;
   import dataAll.equip.creator.EquipSkillAddCtreator;
   import dataAll.equip.creator.EquipSkillCreator;
   import dataAll.equip.creator.EquipStrengthenCtrl;
   import dataAll.equip.creator.EquipUpgradeCtrl;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipFatherDefine;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.save.EquipSave;
   import dataAll.equip.save.FashionSave;
   import dataAll.equip.suit.SuitCtreator;
   import dataAll.equip.vehicle.VehicleData;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.IO_StrengthenData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.NormalItemsData;
   import dataAll.items.creator.ItemsStrengthenCtrl;
   import dataAll.items.define.IO_ResolveItemsDefine;
   import dataAll.items.save.ItemsSave;
   import dataAll.pro.PropertyArrayDefine;
   import dataAll.skill.define.SkillDescrip;
   import dataAll.ui.GatherColor;
   
   public class EquipData extends NormalItemsData implements IO_StrengthenData, IO_ItemsData
   {
      public var save:EquipSave = null;
      
      public var haveSuitB:Boolean = false;
      
      public var isConverB:Boolean = false;
      
      public function EquipData()
      {
         super();
      }
      
      public function inData_bySave(s0:EquipSave, pd0:NormalPlayerData, dg0:ItemsDataGroup = null) : void
      {
         this.save = s0;
         setPlayerData(pd0);
         setFatherData(dg0);
         var d0:EquipDefine = this.save.getDefine();
         this.save.cnName = d0.cnName;
         this.save.name = d0.name;
         this.fleshHaveSuitB();
      }
      
      public function startLevel(pg0:IO_PlayerLevelGetter) : void
      {
      }
      
      public function fleshHaveSuitB() : void
      {
         var d0:EquipDefine = null;
         var f0:EquipFatherDefine = null;
         this.haveSuitB = false;
         if(getColor() != EquipColor.WHITE)
         {
            d0 = this.save.getDefine();
            if(Boolean(d0))
            {
               f0 = Gaming.defineGroup.equip.getFatherDefine(d0.father);
               this.haveSuitB = f0.haveSuitB;
            }
         }
      }
      
      public function canLoadSuitB() : Boolean
      {
         if(this.haveSuitB)
         {
            return true;
         }
         return false;
      }
      
      public function canLoadNowPlayerData(pd0:NormalPlayerData) : Boolean
      {
         if(!(pd0 is PlayerData))
         {
            if(this.save.partType == EquipType.VEHICLE)
            {
               return false;
            }
         }
         return true;
      }
      
      public function clone() : EquipData
      {
         var da0:EquipData = null;
         da0 = this.save.getDataClass();
         da0.save = this.save.clone();
         da0.placeType = placeType;
         da0.playerData = playerData;
         da0.normalPlayerData = normalPlayerData;
         da0.haveSuitB = this.haveSuitB;
         return da0;
      }
      
      public function getTempClone() : EquipData
      {
         var da0:EquipData = this.save.getDataClass();
         var s0:EquipSave = this.save.clone();
         da0.inData_bySave(s0,null,fatherData);
         da0.placeType = placeType;
         da0.haveSuitB = this.haveSuitB;
         return da0;
      }
      
      public function isNormalEquipB() : Boolean
      {
         return EquipType.NORMAL_ARR.indexOf(this.save.getChildType()) >= 0;
      }
      
      public function isAllHurtProB() : Boolean
      {
         return Gaming.defineGroup.equipCreator.propertyCtreator.isAllHurtProB(this.save.obj);
      }
      
      public function normalClone() : IO_ItemsData
      {
         return this.clone();
      }
      
      override public function getSave() : ItemsSave
      {
         return this.save;
      }
      
      public function getCnType() : String
      {
         return "装备";
      }
      
      public function getIconImgUrl(maxWidth0:int = 0, maxHeight0:int = 0) : String
      {
         return this.save.getDefine().iconLabel;
      }
      
      public function getCnName() : String
      {
         return this.save.getCnName();
      }
      
      public function getSellPrice() : Number
      {
         var goods_d0:GoodsDefine = null;
         if(this.save.partType == EquipType.FASHION)
         {
            goods_d0 = Gaming.defineGroup.goods.getDefine(this.save.imgName);
            if(goods_d0 is GoodsDefine)
            {
               if(goods_d0.priceType == "money")
               {
                  return goods_d0.price * 5000;
               }
               return goods_d0.price * 500;
            }
         }
         return this.save.getSellPrice("equip");
      }
      
      public function getWearLevel() : int
      {
         return this.save.getTrueLevel();
      }
      
      public function getTypeId() : String
      {
         return "02";
      }
      
      public function getChildTypeId() : String
      {
         return EquipType.getID_byType(this.save.partType);
      }
      
      public function getPartType() : String
      {
         return this.save.getPartType();
      }
      
      public function toSuitSortId(da0:EquipData) : void
      {
         this.toOtherSortId(da0);
         var f0:EquipFatherDefine = this.save.getFatherDefine();
         if(f0 is EquipFatherDefine)
         {
            tempSortId = f0.getSortId() + "_" + tempSortId;
         }
         else
         {
            tempSortId = "00_" + tempSortId;
         }
      }
      
      public function toOtherSortId(da0:EquipData) : void
      {
         var moreId0:String = null;
         var d0:EquipDefine = null;
         if(this.isNormalEquipB())
         {
            moreId0 = "00_000000";
            if(da0 is EquipData)
            {
               moreId0 = this.save.getSortIdByOtherSave(da0.save);
            }
            tempSortId = this.getChildTypeId() + "_" + this.save.getColorSortId() + "_" + moreId0 + "_" + this.save.getLevelSortId();
         }
         else
         {
            d0 = this.save.getDefine();
            tempSortId = this.getChildTypeId() + "_" + this.save.getColorSortId() + "_" + TextWay.toNum(d0.getSortIndex() + "",4) + "_" + this.save.getLevelSortId();
            if(d0.type == EquipType.FASHION)
            {
               tempSortId += "_" + d0.getApplySex() + "_" + d0.name;
            }
         }
      }
      
      public function canRemakeB() : Boolean
      {
         return EquipType.NORMAL_ARR.indexOf(this.save.partType) >= 0;
      }
      
      override public function getNowNum() : int
      {
         return 1;
      }
      
      override public function setNowNum(num0:int) : void
      {
      }
      
      override public function addNowNum(num0:int, otherDa0:IO_ItemsData = null) : void
      {
      }
      
      public function getStartLv() : int
      {
         return this.save.getStarNum();
      }
      
      override public function getResolveItemsDefine() : IO_ResolveItemsDefine
      {
         return this.save.getDefine();
      }
      
      public function canUpgradeB() : Boolean
      {
         return EquipUpgradeCtrl.canUpgradeB(this) && EquipType.NORMAL_ARR.indexOf(this.save.partType) >= 0;
      }
      
      override public function isArenaGiftB() : Boolean
      {
         var f0:EquipFatherDefine = this.save.getFatherDefine();
         if(f0 is EquipFatherDefine && this.save.itemsLevel >= 55)
         {
            return f0.name == "blueArmy";
         }
         return false;
      }
      
      override public function getAllUpgradeConverStoneNum() : int
      {
         if(this.isArenaGiftB() || this.save.addLevel <= 0)
         {
            return 0;
         }
         if(this is VehicleData)
         {
            return 0;
         }
         return int(this.save.getAllUpgradeConverStoneNum() / 3);
      }
      
      override public function canStrengthenB() : Boolean
      {
         return EquipStrengthenCtrl.canStrengthenB(this);
      }
      
      override public function canStrengthenMoveB() : Boolean
      {
         return EquipStrengthenCtrl.canStrengthenMoveB(this);
      }
      
      public function getStrengthenTitle() : String
      {
         var name0:* = null;
         var v0:Number = NaN;
         var proD0:PropertyArrayDefine = null;
         var s0:String = ComMethod.color("强化" + this.getStrengthenLv() + "级",GatherColor.yellowColor);
         var addObj0:Object = EquipStrengthenCtrl.getProByPartType(this.getPartType(),this.getStrengthenLv());
         var nameArr0:Array = ObjectMethod.getNameArr(addObj0);
         if(nameArr0.length > 0)
         {
            for each(name0 in nameArr0)
            {
               v0 = Number(addObj0[name0]);
               proD0 = Gaming.defineGroup.getPropertyArrayDefine(name0);
               s0 += "\n" + proD0.cnName + ":" + proD0.getValueString(v0);
            }
         }
         return s0;
      }
      
      public function getStrengthenLv() : int
      {
         return this.save.strengthenLv;
      }
      
      public function getSMaxLv() : int
      {
         return this.save.sMaxLv;
      }
      
      public function getStrengthenMustMul() : Number
      {
         if(EquipColor.strengthenMustMul1_8.indexOf(getColor()) >= 0)
         {
            return 1.8;
         }
         return 1;
      }
      
      public function strengthenNumAdd() : void
      {
         ++this.save.strengthenNum;
      }
      
      public function haveMoreSkinB() : Boolean
      {
         return false;
      }
      
      public function canEchelonB() : Boolean
      {
         return ItemsStrengthenCtrl.canEchelonB(this);
      }
      
      public function getStrengthenLightLv() : int
      {
         var sLv0:int = this.save.strengthenLv;
         if(sLv0 >= 25)
         {
            return 3;
         }
         if(sLv0 >= 20)
         {
            return 2;
         }
         if(sLv0 >= 15)
         {
            return 1;
         }
         return 0;
      }
      
      public function canEvoB() : Boolean
      {
         return EquipEvoCtrl.canEvoB(this);
      }
      
      override public function canRefiningB() : Boolean
      {
         if(this.save.isOnceGetB())
         {
            return false;
         }
         var d0:EquipDefine = this.save.getDefine();
         if(d0.canRefiningB())
         {
            if(this.save.getTrueLevel() >= 80)
            {
               return true;
            }
         }
         return false;
      }
      
      override public function getResolveGift() : GiftAddDefineGroup
      {
         return super.getResolveGift();
      }
      
      override public function canResolveB() : Boolean
      {
         if(this.save.evoLv <= 1)
         {
            return super.canResolveB();
         }
         return false;
      }
      
      override public function dealBtnListCn(label0:String) : String
      {
         var d0:EquipDefine = this.save.getDefine();
         return d0.dealBtnListCn(label0);
      }
      
      public function getFashionShowBtnName(leftPd0:NormalPlayerData) : String
      {
         var s0:FashionSave = null;
         var leftD0:HeroDefine = null;
         var d0:EquipDefine = null;
         if(placeType == ItemsDataGroup.PLACE_BAG || placeType == ItemsDataGroup.PLACE_WEAR)
         {
            s0 = this.save as FashionSave;
            if(Boolean(s0))
            {
               leftD0 = leftPd0.heroData.def;
               d0 = s0.getDefine();
               if(leftD0.sex == d0.getApplySex() && d0.panRole(leftD0.name))
               {
                  if(s0.showHero != leftPd0.getRoleName() || placeType == ItemsDataGroup.PLACE_WEAR)
                  {
                     return "useFashion";
                  }
                  return "cancelFashion";
               }
            }
         }
         return "";
      }
      
      public function setFashionShowRoleName(name0:String) : void
      {
         var s0:FashionSave = this.save as FashionSave;
         if(Boolean(s0))
         {
            s0.showHero = name0;
         }
      }
      
      public function getFashionShowRoleName() : String
      {
         var s0:FashionSave = this.save as FashionSave;
         if(Boolean(s0))
         {
            return s0.showHero;
         }
         return "";
      }
      
      public function moveSkillAdd(da0:EquipData) : void
      {
         var before0:Object = this.save.heroSkillAddObj;
         this.save.heroSkillAddObj = da0.save.heroSkillAddObj;
         da0.save.heroSkillAddObj = before0;
      }
      
      public function getTipSkillDecripB() : Boolean
      {
         return this.save.getDefine().isDarkgoldAndMoreB() == false;
      }
      
      public function getGatherTip(compareDa0:EquipData = null, tipType0:String = "", showSkillB:Boolean = true) : String
      {
         var compareObj0:Object = null;
         if(Boolean(compareDa0))
         {
            compareObj0 = compareDa0.save.getTrueObj();
         }
         var d0:EquipDefine = this.save.getDefine();
         var str0:String = "";
         if(this.save.partType == EquipType.FASHION)
         {
            str0 += this.getFashionGatherTip();
            str0 += "\n";
         }
         else if(EquipType.NORMAL_ARR.indexOf(this.save.partType) >= 0)
         {
            str0 += "基础等级：<yellow " + this.save.itemsLevel + "级/>";
         }
         var trueObj0:Object = this.save.getTrueObj();
         if(ObjectMethod.getObjElementNum(trueObj0) > 0)
         {
            str0 += "\n<i1>|<blue <b>提升：</b>/>";
            str0 += "\n" + EquipPropertyDataCreator.getText_byObj(trueObj0,compareObj0,true);
         }
         str0 += SuitCtreator.getGatherTip(this);
         str0 += EquipSkillAddCtreator.getGatherTip(this.save.heroSkillAddObj);
         if(d0.description != "" && !d0.isNormalB())
         {
            if(d0.type == EquipType.FASHION)
            {
               str0 += "\n" + d0.description;
            }
            else if(!d0.isNormalB())
            {
               str0 += "\n<i1>|<blue <b>特殊技能：</b>/>\n" + d0.description;
            }
         }
         var skillArr0:Array = this.save.getSkillArr();
         if(skillArr0.length == 0)
         {
            skillArr0 = d0.skillArr;
         }
         if(skillArr0.length > 0)
         {
            str0 += "\n<i1>|<blue <b>技能：</b>/>";
            if(showSkillB)
            {
               str0 += "\n" + EquipSkillCreator.getAllSkillTip(skillArr0);
            }
            else
            {
               str0 += "<yellow " + SkillDescrip.getCnText(skillArr0) + "/>";
            }
         }
         return str0 + d0.getHDFashionTipAdd();
      }
      
      public function getProTipLastFun(d0:PropertyArrayDefine, s0:String, v0:*) : String
      {
         var more50B0:Boolean = false;
         if(Boolean(normalPlayerData))
         {
            more50B0 = normalPlayerData.isMore50Pro(d0.name);
            if(more50B0)
            {
               s0 += ComMethod.color("(衰减50%)","#AA51FF");
            }
         }
         return s0;
      }
      
      protected function getSurplusDayGatherTip(tipType0:String = "") : String
      {
         var d0:EquipDefine = null;
         var surplusDay0:Number = NaN;
         var str0:String = "";
         if(tipType0 == "")
         {
            d0 = this.save.getDefine();
            if(this.save.partType != EquipType.FASHION)
            {
               if(d0.life > 0)
               {
                  surplusDay0 = this.getNowHaveDay();
                  if(surplusDay0 > -4000)
                  {
                     str0 += "\n剩余使用天数：<purple <b>" + (surplusDay0 + "天") + "</b>/>";
                  }
                  else
                  {
                     str0 += "\n寿命：<purple <b>" + (d0.life + "天") + "</b>/>";
                  }
               }
            }
         }
         return str0;
      }
      
      private function getFashionGatherTip() : String
      {
         var d0:EquipDefine = this.save.getDefine();
         return d0.getFashionGatherTip(placeType == "" ? -1 : this.getNowHaveDay());
      }
      
      public function inNowTimeCountHaveDay(timeStr0:String) : Boolean
      {
         var d0:EquipDefine = this.save.getDefine();
         if(d0.life > 0)
         {
            return this.getHaveDayNum(timeStr0) <= 0;
         }
         return false;
      }
      
      private function getHaveDayNum(timeStr0:String) : int
      {
         var getTime0:StringDate = null;
         var now0:StringDate = null;
         var haveNum0:int = 0;
         var d0:EquipDefine = this.save.getDefine();
         if(d0.life > 0)
         {
            getTime0 = new StringDate(this.save.severTime);
            now0 = new StringDate(timeStr0);
            haveNum0 = now0.reductionOne(getTime0);
            return d0.life - haveNum0;
         }
         return 0;
      }
      
      protected function getNowHaveDay() : int
      {
         if(Boolean(Gaming.PG.da))
         {
            return this.getHaveDayNum(Gaming.PG.da.time.getReadTime());
         }
         return 0;
      }
   }
}

