package dataAll._app.wilder
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.wilder.define.WilderDefine;
   
   public class WilderSave
   {
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var name:String = "";
      
      public var diff:String = "3";
      
      public var noDropValueObj:Object = {};
      
      public var timeState:int = -1;
      
      public var isWeekendB:Boolean = false;
      
      public function WilderSave()
      {
         super();
         this.all = 0;
         this.num = 0;
         this.buyNum = 0;
         this.exchangeNum = 0;
         this.winNum = 0;
         this.dropNum = 0;
         this.haveExchangeNum = 0;
         this.sweepingNum = 0;
         this.weekNoUseNum = 0;
      }
      
      public function get all() : Number
      {
         return this.CF.getAttribute("all");
      }
      
      public function set all(v0:Number) : void
      {
         this.CF.setAttribute("all",v0);
      }
      
      public function get num() : Number
      {
         return this.CF.getAttribute("num");
      }
      
      public function set num(v0:Number) : void
      {
         this.CF.setAttribute("num",v0);
      }
      
      public function get buyNum() : Number
      {
         return this.CF.getAttribute("buyNum");
      }
      
      public function set buyNum(v0:Number) : void
      {
         this.CF.setAttribute("buyNum",v0);
      }
      
      public function get sweepingNum() : Number
      {
         return this.CF.getAttribute("sweepingNum");
      }
      
      public function set sweepingNum(v0:Number) : void
      {
         this.CF.setAttribute("sweepingNum",v0);
      }
      
      public function get exchangeNum() : Number
      {
         return this.CF.getAttribute("exchangeNum");
      }
      
      public function set exchangeNum(v0:Number) : void
      {
         this.CF.setAttribute("exchangeNum",v0);
      }
      
      public function get winNum() : Number
      {
         return this.CF.getAttribute("winNum");
      }
      
      public function set winNum(v0:Number) : void
      {
         this.CF.setAttribute("winNum",v0);
      }
      
      public function get dropNum() : Number
      {
         return this.CF.getAttribute("dropNum");
      }
      
      public function set dropNum(v0:Number) : void
      {
         this.CF.setAttribute("dropNum",v0);
      }
      
      public function get haveExchangeNum() : Number
      {
         return this.CF.getAttribute("haveExchangeNum");
      }
      
      public function set haveExchangeNum(v0:Number) : void
      {
         this.CF.setAttribute("haveExchangeNum",v0);
      }
      
      public function get weekNoUseNum() : Number
      {
         return this.CF.getAttribute("weekNoUseNum");
      }
      
      public function set weekNoUseNum(v0:Number) : void
      {
         this.CF.setAttribute("weekNoUseNum",v0);
      }
      
      public function get d() : Number
      {
         return this.CF.getAttribute("d");
      }
      
      public function set d(v0:Number) : void
      {
         this.CF.setAttribute("d",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.noDropValueObj = ClassProperty.copyObj(obj0["noDropValueObj"]);
         if(obj0.hasOwnProperty("diff4B"))
         {
            if(Boolean(obj0["diff4B"]))
            {
               if(this.d < 4)
               {
                  this.d = 4;
               }
            }
         }
         if(obj0.hasOwnProperty("d6"))
         {
            if(obj0["d6"] > 0)
            {
               if(this.d < 6)
               {
                  this.d = 6;
               }
            }
         }
      }
      
      public function inData_byDefine(d0:WilderDefine) : void
      {
         this.name = d0.name;
      }
      
      public function noUseCount(noLoginDay0:int, overWeekB0:Boolean) : void
      {
         var beforeWeekNum0:int = 0;
         var beforeNum0:int = 0;
         var noUseNum0:int = 0;
         if(overWeekB0)
         {
            noUseNum0 = noLoginDay0;
         }
         else
         {
            beforeWeekNum0 = this.weekNoUseNum - this.num;
            if(beforeWeekNum0 < 0)
            {
               beforeWeekNum0 = 0;
            }
            beforeNum0 = 1 + this.weekNoUseNum + this.buyNum + this.exchangeNum - this.num;
            if(beforeNum0 > 1)
            {
               beforeNum0 = 1;
            }
            if(noLoginDay0 >= 1)
            {
               noLoginDay0 -= 1;
            }
            noUseNum0 = beforeNum0 + noLoginDay0 + beforeWeekNum0;
         }
         if(noUseNum0 > 3)
         {
            noUseNum0 = 3;
         }
         this.weekNoUseNum = noUseNum0;
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.num = 0;
         this.haveExchangeNum = 0;
         this.exchangeNum = 0;
         this.buyNum = 0;
         this.sweepingNum = 0;
         var da0:StringDate = new StringDate(timeStr0);
         this.isWeekendB = da0.isWeekendB();
      }
      
      public function getAllNum() : int
      {
         var base0:int = 0;
         if(this.timeState == 0)
         {
            if(this.getDefine().noBuyB)
            {
               base0 = 1;
            }
            else
            {
               base0 = (this.isWeekendB ? 2 : 1) + this.weekNoUseNum;
            }
         }
         return base0 + this.buyNum + this.exchangeNum;
      }
      
      public function getCanChallangeNum() : int
      {
         return this.getAllNum() - this.num;
      }
      
      public function getDefine() : WilderDefine
      {
         return Gaming.defineGroup.wilder.getDefine(this.name);
      }
      
      public function addNoDropValue(index0:int, v0:Number) : void
      {
         if(!this.noDropValueObj.hasOwnProperty(index0))
         {
            this.noDropValueObj[index0] = 0;
         }
         this.noDropValueObj[index0] += v0;
      }
      
      public function clearNoDropValue(index0:int) : void
      {
         this.noDropValueObj[index0] = 0;
      }
      
      public function getNoDropValue(index0:int) : Number
      {
         if(this.noDropValueObj.hasOwnProperty(index0))
         {
            return this.noDropValueObj[index0];
         }
         return 0;
      }
   }
}

