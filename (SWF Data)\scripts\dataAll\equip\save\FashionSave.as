package dataAll.equip.save
{
   import com.sounto.utils.ClassProperty;
   
   public class FashionSave extends EquipSave
   {
      private static const ZERO:FashionSave = new FashionSave();
      
      public static var pro_arr:Array = null;
      
      public static var mePro_arr:Array = null;
      
      public var showHero:String = "";
      
      public function FashionSave()
      {
         super();
      }
      
      override public function getProArr() : Array
      {
         return pro_arr;
      }
      
      override public function getZero() : EquipSave
      {
         return ZERO;
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         super.inData_byObj(obj0);
         ClassProperty.inData_bySaveObj(this,obj0,mePro_arr);
      }
   }
}

