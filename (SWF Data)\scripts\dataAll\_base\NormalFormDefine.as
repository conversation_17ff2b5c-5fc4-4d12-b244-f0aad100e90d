package dataAll._base
{
   import com.sounto.utils.ClassProperty;
   
   public class NormalFormDefine extends NormalDefine
   {
      protected static var formNoFormPro_arr:Array = ["name","cnName","father","formName"];
      
      public static var FORM:String = "form";
      
      public var formName:String = "";
      
      public function NormalFormDefine()
      {
         super();
      }
      
      public static function haveFormPan(formName0:String) : Boolean
      {
         return formName0 != FORM && formName0 != "";
      }
      
      public function inFormData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,this.getFormProArr());
      }
      
      protected function getFormProArr() : Array
      {
         return getFinalProArr();
      }
      
      public function isFormB() : Boolean
      {
         return this.formName == FORM;
      }
      
      public function haveFormB() : Boolean
      {
         return haveFormPan(this.formName);
      }
   }
}

