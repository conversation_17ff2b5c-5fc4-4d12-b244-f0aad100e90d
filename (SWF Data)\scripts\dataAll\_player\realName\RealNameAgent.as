package dataAll._player.realName
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   
   public class RealNameAgent
   {
      public static var pro_arr:Array = null;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var success:int = 0;
      
      public var desc:String = "";
      
      public function RealNameAgent()
      {
         super();
      }
      
      public static function getSuccessAgent() : RealNameAgent
      {
         var a0:RealNameAgent = new RealNameAgent();
         a0.success = 1;
         a0.cert = 1;
         a0.reconfirm = 0;
         a0.status = 1;
         return a0;
      }
      
      public function get cert() : Number
      {
         return this.CF.getAttribute("cert");
      }
      
      public function set cert(v0:Number) : void
      {
         this.CF.setAttribute("cert",v0);
      }
      
      public function get reconfirm() : Number
      {
         return this.CF.getAttribute("reconfirm");
      }
      
      public function set reconfirm(v0:Number) : void
      {
         this.CF.setAttribute("reconfirm",v0);
      }
      
      public function get status() : Number
      {
         return this.CF.getAttribute("status");
      }
      
      public function set status(v0:Number) : void
      {
         this.CF.setAttribute("status",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function haveCertB() : Boolean
      {
         return this.cert == 1;
      }
      
      public function canReviseB() : Boolean
      {
         return this.reconfirm == 1;
      }
   }
}

