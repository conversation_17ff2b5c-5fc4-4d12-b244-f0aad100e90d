package dataAll._app.wilder
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.NumberMethod;
   import dataAll._app.wilder.define.WilderDefine;
   import dataAll._app.wilder.define.WilderDropDefine;
   import dataAll._player.PlayerData;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.things.define.ThingsName;
   import gameAll.drop.BodyDropCtrl;
   
   public class WilderData
   {
      private var playerData:PlayerData;
      
      public var name:String = "";
      
      public var def:WilderDefine = null;
      
      public var save:WilderSave = null;
      
      public function WilderData()
      {
         super();
      }
      
      private static function getRanSkillArr(day0:Number, wilderIndex0:int, allArr0:Array, meArr0:Array) : Array
      {
         var index0:int = 0;
         var name0:String = null;
         var newArr0:Array = ComMethod.deductArr(allArr0,meArr0);
         if(newArr0.length > 0)
         {
            index0 = (day0 + wilderIndex0) % newArr0.length;
            name0 = ArrayMethod.getElementLimit(newArr0,index0);
            if(name0 != null && name0 != "")
            {
               return [name0];
            }
         }
         return [];
      }
      
      public function inData_bySave(s0:WilderSave, pd0:PlayerData) : void
      {
         this.playerData = pd0;
         this.name = s0.name;
         this.save = s0;
         this.def = Gaming.defineGroup.wilder.getDefine(this.name);
      }
      
      public function newDayCtrl(timeStr0:String, noLoginDay0:int, overWeekB0:Boolean) : void
      {
         this.save.noUseCount(noLoginDay0,overWeekB0);
         this.save.newDayCtrl(timeStr0);
         this.setTimeState(this.countTimeState(timeStr0));
      }
      
      public function supple(timeStr0:String) : void
      {
         this.setTimeState(this.countTimeState(timeStr0));
      }
      
      public function useNum(sweepingB0:Boolean = false) : void
      {
         ++this.save.all;
         ++this.save.num;
         ++this.save.winNum;
         if(sweepingB0)
         {
            ++this.save.sweepingNum;
         }
      }
      
      public function getSweepingTitle() : String
      {
         return "正在扫荡" + ComMethod.color(this.def.cnName + "（" + WilderDiff.getCnByName(this.save.diff) + "）","#00FF00");
      }
      
      public function getTimeState() : int
      {
         return this.save.timeState;
      }
      
      public function setTimeState(state0:int) : void
      {
         this.save.timeState = state0;
         if(state0 != 0)
         {
            this.save.weekNoUseNum = 0;
         }
      }
      
      public function addBuyNum(num0:int) : void
      {
         this.save.buyNum += num0;
      }
      
      public function addExchangeNum(num0:int) : void
      {
         this.save.exchangeNum += num0;
         this.save.haveExchangeNum += num0;
      }
      
      public function getSurplusExchangeNum() : int
      {
         var max0:int = 3;
         max0 += this.getActiveExchangeAdd();
         if(this.def.noExchangeB)
         {
            max0 = this.getActiveExchangeNoMax();
         }
         return max0 - this.save.haveExchangeNum;
      }
      
      public function noExchangeB() : Boolean
      {
         var num0:int = 0;
         if(this.def.noExchangeB)
         {
            num0 = this.getActiveExchangeNoMax();
            if(num0 > 0)
            {
               return false;
            }
            return true;
         }
         return false;
      }
      
      public function getActiveExchangeAdd() : int
      {
         if(Boolean(this.playerData))
         {
            if(this.playerData.time.isNowActiveB())
            {
               return BodyDropCtrl.getActiveAdd_wilderExchange();
            }
         }
         return 0;
      }
      
      private function getActiveExchangeNoMax() : int
      {
         var num0:int = 0;
         if(Boolean(this.playerData))
         {
            if(this.playerData.time.isNowActiveB())
            {
               return int(BodyDropCtrl.getActiveAdd_wilderExchangeNo());
            }
         }
         return 0;
      }
      
      public function getCanChallangeNum() : int
      {
         var allSurplus0:Number = NaN;
         var v0:int = 0;
         if(this.def.isGM3())
         {
            if(this.save.timeState == 0)
            {
               if(Boolean(this.playerData))
               {
                  v0 = this.playerData.gift.save.anniverGm.getSurplusNum();
               }
               else
               {
                  v0 = 1;
               }
            }
            else
            {
               v0 = 0;
            }
         }
         else
         {
            v0 = this.save.getCanChallangeNum();
         }
         var allMax0:int = this.def.limitAll;
         if(allMax0 > 0)
         {
            allSurplus0 = allMax0 - this.save.all;
            if(allSurplus0 < 0)
            {
               allSurplus0 = 0;
            }
            if(v0 > allSurplus0)
            {
               v0 = allSurplus0;
            }
         }
         return v0;
      }
      
      public function getEnemyLv() : int
      {
         return WilderDiff.getEnemyLv(this.save.diff);
      }
      
      public function getEnemyLvByDiff(diff0:String) : int
      {
         return WilderDiff.getEnemyLv(diff0);
      }
      
      public function randomSkillB() : Boolean
      {
         return this.def.noBuyB == false;
      }
      
      public function getDropStr() : String
      {
         var d0:WilderDropDefine = null;
         var str0:String = "";
         var index0:int = 0;
         for each(d0 in this.def.dropArr)
         {
            str0 += (index0 == 0 ? "" : "、") + d0.getDropStr(this.save.diff,this.getDropAdd(d0));
            index0++;
         }
         return str0;
      }
      
      protected function getDropAdd(d0:WilderDropDefine) : Number
      {
         var add0:Number = 0;
         if(Boolean(this.playerData))
         {
            if(d0.isWeaponB())
            {
               add0 = this.playerData.getDropMerge().weaponDropPro;
            }
         }
         return add0;
      }
      
      public function haveDropGift(diff0:String) : Boolean
      {
         var d0:WilderDropDefine = null;
         for each(d0 in this.def.dropArr)
         {
            if(d0.getDiff(diff0) > 0)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getDropGiftAddDefineGroup(index0:int) : GiftAddDefineGroup
      {
         var petBookAdd0:Number = NaN;
         var gd0:GiftAddDefine = null;
         var new0:Number = NaN;
         var num0:int = 0;
         var drop0:WilderDropDefine = this.def.dropArr[index0];
         var g0:GiftAddDefineGroup = drop0.getGiftAddDefineGroup(this.save.diff);
         if(g0 == null)
         {
            return null;
         }
         if(Boolean(this.playerData))
         {
            petBookAdd0 = this.playerData.moreWay.get_petBookDropPro();
            if(petBookAdd0 > 0)
            {
               for each(gd0 in g0.arr)
               {
                  if(ThingsName.petBookArr.indexOf(gd0.name) >= 0)
                  {
                     new0 = gd0.num * (1 + petBookAdd0);
                     num0 = NumberMethod.getRandomInt(new0);
                     gd0.num = num0;
                  }
               }
            }
         }
         return g0;
      }
      
      public function getUIDropGiftG() : GiftAddDefineGroup
      {
         return this.def.getUIDropGiftG(this.save.diff);
      }
      
      public function getChallangeStr() : String
      {
         return "挑战" + WilderDiff.getCnByName(this.save.diff);
      }
      
      public function getMouseTip(timeStr0:String) : String
      {
         var day0:int = this.getSurplusDay(timeStr0);
         var str0:String = "";
         if(day0 > 0)
         {
            str0 = "当前副本将在<green " + day0 + "天/>后过期。";
         }
         else if(day0 == 0)
         {
            str0 = "当前副本将在<green 明天/>过期。";
         }
         else if(this.getTimeState() == -1)
         {
            str0 = this.getOpenTimeStr();
         }
         else
         {
            str0 = "当前副本已过期。";
         }
         if(this.save.all > 0)
         {
            str0 += "\n已战胜首领<blue " + this.save.all + "/>次。";
         }
         return str0 + ("\n奖励：<orange " + this.def.getDropMouseTip() + "/>。");
      }
      
      public function getSweepingEnabled() : Boolean
      {
         return int(this.save.diff) < 4 && this.save.sweepingNum < this.save.buyNum;
      }
      
      public function getCanSweepingNum() : int
      {
         var num0:int = this.save.buyNum - this.save.sweepingNum;
         var now0:int = this.getCanChallangeNum();
         if(num0 > now0)
         {
            num0 = now0;
         }
         return num0;
      }
      
      private function weekOpenB() : Boolean
      {
         var openId0:int = this.playerData.time.getWilderOpenId();
         if(openId0 > 0 && openId0 == this.def.id)
         {
            return true;
         }
         return false;
      }
      
      private function getOpenTime() : StringDate
      {
         if(this.weekOpenB())
         {
            return this.playerData.time.getWilderOpenTime();
         }
         return new StringDate(this.def.openTime);
      }
      
      private function getOpenTimeStr() : String
      {
         if(this.weekOpenB())
         {
            return this.playerData.time.getWilderOpenTime().getStr();
         }
         return this.def.openTime;
      }
      
      private function getDay() : int
      {
         if(this.weekOpenB())
         {
            return 6;
         }
         return this.def.day;
      }
      
      public function getOpenStr() : String
      {
         var da0:StringDate = this.getOpenTime();
         return ComMethod.color(da0.month + 1 + "月" + da0.date + "日开放","#FFFF00");
      }
      
      public function getOverTimeValue() : Number
      {
         var da0:StringDate = this.getOpenTime();
         var v0:Number = da0.getDateClass().time;
         v0 += this.getDay() * 24 * 3600 * 1000;
         return v0 / 100000;
      }
      
      public function getSurplusDay(timeStr0:String) : int
      {
         var have0:int = this.getHaveDay(timeStr0);
         if(have0 >= 0)
         {
            return this.getDay() - have0;
         }
         return -1;
      }
      
      public function getHaveDay(timeStr0:String) : int
      {
         return StringDate.compareDateByStr(this.getOpenTimeStr(),timeStr0);
      }
      
      private function countTimeState(timeStr0:String) : int
      {
         var have0:int = this.getHaveDay(timeStr0);
         if(have0 < 0)
         {
            return -1;
         }
         if(have0 > this.getDay())
         {
            return 1;
         }
         return 0;
      }
      
      private function getNowNoDropValue(index0:int) : Number
      {
         var drop0:WilderDropDefine = this.def.dropArr[index0];
         return drop0.getNoDropValue(this.save.diff);
      }
      
      public function getDropPro(index0:int) : Number
      {
         var drop0:WilderDropDefine = this.def.dropArr[index0];
         return drop0.getTruePro(this.save.diff,this.save.getNoDropValue(index0),this.getDropAdd(drop0));
      }
      
      public function dropEvent(index0:int, num0:int) : void
      {
         var diff0:int = int(this.save.diff);
         if(this.save.d < diff0)
         {
            this.save.d = diff0;
         }
         if(num0 > 0)
         {
            this.save.clearNoDropValue(index0);
            this.save.dropNum += num0;
         }
         else
         {
            this.save.addNoDropValue(index0,this.getNowNoDropValue(index0));
         }
         this.playerData.gift.save.wilderWin();
      }
      
      public function getDiff() : String
      {
         return this.save.diff;
      }
      
      public function getNowDiffSkillArr() : Array
      {
         return this.getDiffSkillArr(this.save.diff);
      }
      
      public function getDiffSkillArr(diffStr0:String) : Array
      {
         var meSkillArr0:Array = null;
         var time0:Number = NaN;
         var day0:Number = NaN;
         var arr6:Array = null;
         var arr7:Array = null;
         var skillArr0:Array = [];
         var diff0:int = int(diffStr0);
         if(diff0 >= 6)
         {
            meSkillArr0 = this.def.getBodyDefine().getWilderTipSkillArr();
            time0 = this.playerData.time.getReadTimeDate().getDateClass().time;
            day0 = Math.ceil(time0 / 1000 / 60 / 60 / 24);
            if(this.randomSkillB())
            {
               arr6 = getRanSkillArr(day0,this.def.index,Gaming.defineGroup.skill.getSuperEnemySkillNameArr(99),meSkillArr0);
               skillArr0 = skillArr0.concat(arr6);
            }
            if(diff0 >= 7)
            {
               if(this.def.name != "BoomSkullS")
               {
                  arr7 = getRanSkillArr(day0,this.def.index,WilderDiff.demonSkillArr,meSkillArr0);
                  skillArr0 = skillArr0.concat(arr7);
               }
            }
         }
         return skillArr0;
      }
   }
}

