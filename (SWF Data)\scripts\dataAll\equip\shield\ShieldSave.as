package dataAll.equip.shield
{
   import com.sounto.utils.ClassProperty;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.save.EquipSave;
   
   public class ShieldSave extends EquipSave
   {
      private static const ZERO:ShieldSave = new ShieldSave();
      
      public function ShieldSave()
      {
         super();
      }
      
      override public function getZero() : EquipSave
      {
         return ZERO;
      }
      
      override public function inDataByDefine(d0:EquipDefine) : void
      {
         super.inDataByDefine(d0);
         var md0:ShieldDefine = d0 as ShieldDefine;
         name = md0.name;
         itemsLevel = md0.lv;
         skillArr = md0.skillArr.concat();
      }
      
      public function getShieldDefine() : ShieldDefine
      {
         return Gaming.defineGroup.shield.getDefine(imgName);
      }
      
      override public function getTrueObj() : Object
      {
         return this.getShieldDefine().getAddObj();
      }
      
      override public function getDefine() : EquipDefine
      {
         return this.getShieldDefine();
      }
      
      override public function getDataClass() : EquipData
      {
         return new ShieldData();
      }
      
      override public function getSkillArr() : Array
      {
         return this.getShieldDefine().skillArr;
      }
      
      override public function clone() : EquipSave
      {
         var obj0:Object = ClassProperty.copyObj(this);
         var s0:EquipSave = new ShieldSave();
         s0.inData_byObj(obj0);
         return s0;
      }
   }
}

