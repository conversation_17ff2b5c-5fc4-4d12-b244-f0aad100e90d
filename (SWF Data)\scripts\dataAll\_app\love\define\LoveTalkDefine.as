package dataAll._app.love.define
{
   import com.sounto.utils.ClassProperty;
   import dataAll.body.define.BodySex;
   import dataAll.body.define.NormalBodyDefine;
   
   public class LoveTalkDefine
   {
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var context:String = "";
      
      public var type:String = "";
      
      public var things:String = "";
      
      public var fc:String = "";
      
      public function LoveTalkDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML, type0:String) : void
      {
         if(!xml0)
         {
            return;
         }
         this.type = type0;
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         this.context = String(xml0);
      }
      
      public function panThingsCnName(cn0:String) : Boolean
      {
         var name0:* = null;
         var arr0:Array = this.things.split(",");
         for each(name0 in arr0)
         {
            if(cn0.indexOf(name0) >= 0)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getContext(p1Role0:String) : String
      {
         var bd0:NormalBodyDefine = Gaming.defineGroup.body.getDefine(p1Role0);
         if(<PERSON><PERSON><PERSON>(bd0))
         {
            if(bd0.sex == BodySex.FEMALE)
            {
               if(this.fc != "")
               {
                  return this.fc;
               }
            }
         }
         return this.context;
      }
   }
}

