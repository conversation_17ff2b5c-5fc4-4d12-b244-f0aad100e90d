package dataAll._app.edit.tor
{
   import com.common.data.Base64;
   import dataAll._app.edit.TorData;
   import dataAll._app.edit.arms.ArmsTorData;
   import flash.system.System;
   
   public class TorEditMethod
   {
      private static var tempDa:TorData = null;
      
      public function TorEditMethod()
      {
         super();
      }
      
      public static function shareCode(da0:TorData) : void
      {
         tempDa = da0;
         var code0:String = da0.getShareCode(Gaming.PG);
         Gaming.uiGroup.alertBox.textInput.showTextInput("确认复制以下代码？",code0,yesCode,"yesAndNo",10000);
      }
      
      private static function yesCode(str0:String) : void
      {
         var code0:String = tempDa.getShareCode(Gaming.PG);
         var title0:String = "首领工厂";
         if(tempDa is ArmsTorData)
         {
            title0 = "“创造武器”";
         }
         System.setClipboard(code0);
         Gaming.uiGroup.alertBox.showSuccess("代码已复制到剪贴板！发给你的朋友，\n让他们在" + title0 + "添加该代码，即可体验。");
         tempDa = null;
      }
      
      public static function codeToObjAndPan(code0:String) : Object
      {
         var obj0:Object = null;
         if(code0 == "")
         {
            return "代码不能为空！";
         }
         try
         {
            return codeToObj(code0);
         }
         catch(e:Error)
         {
         }
         return "代码格式不正确！";
      }
      
      public static function codeToObj(code0:String) : Object
      {
         return Base64.decodeObject(code0);
      }
      
      public static function objToCode(obj0:Object) : String
      {
         return Base64.encodeObject(obj0);
      }
   }
}

