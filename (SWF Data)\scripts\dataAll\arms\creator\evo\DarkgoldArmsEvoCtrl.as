package dataAll.arms.creator.evo
{
   import com.sounto.utils.ArrayMethod;
   import dataAll.arms.ArmsData;
   import dataAll.arms.creator.ArmsEvoCtrl;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.save.ArmsSave;
   import dataAll.must.define.MustDefine;
   
   public class DarkgoldArmsEvoCtrl extends DiyArmsEvoCtrl
   {
      public static const nameArr:Array = ["falconGun","extremeGun"];
      
      private const hurtMulArr:Array = [150,180,210,250].concat([300,340]).concat([380]);
      
      private const cnArr:Array = ["$","$II","$III","$IV"].concat(["无双·$","无双·$II"]).concat(["氩星·$"]);
      
      public function DarkgoldArmsEvoCtrl()
      {
         super();
      }
      
      override public function getHurtMul(evoLv0:int, d0:ArmsDefine) : Number
      {
         var mul0:Number = ArrayMethod.getElementLimit(this.hurtMulArr,evoLv0 - 1) as Number;
         return mul0 / 100;
      }
      
      override public function getCnName(cn0:String, evoLv0:int, d0:ArmsDefine) : String
      {
         var name0:String = null;
         name0 = ArrayMethod.getElementLimit(this.cnArr,evoLv0 - 1) as String;
         return name0.replace("$",cn0);
      }
      
      override public function getArmsImageName(d0:ArmsDefine, evoLv0:int) : String
      {
         var back0:String = "";
         var allEvoLv0:int = evoLv0 + d0.evoMustFirstLv;
         if(allEvoLv0 >= ArmsEvoCtrl.yagoldLv)
         {
            back0 = "6";
         }
         else if(allEvoLv0 >= ArmsEvoCtrl.purgoldLv)
         {
            back0 = "5";
         }
         return d0.getImageLabelByBodySuffix(back0);
      }
      
      override public function doEvo(da0:ArmsData) : void
      {
         var s0:ArmsSave = da0.save;
         var lv0:int = s0.evoLv + 1 + da0.def.evoMustFirstLv;
         s0.doEvo();
         s0.armsImgLabel = this.getArmsImageName(da0.def,s0.evoLv);
         if(lv0 == ArmsEvoCtrl.purgoldLv)
         {
            ArrayMethod.addNoRepeatInArr(s0.godSkillArr,da0.name + "Skill2");
         }
      }
      
      override public function getMust(evoLv0:int, ad0:ArmsDefine) : MustDefine
      {
         var arr0:Array = ["demStone;9999999"];
         var mustLv0:int = evoLv0 + 1 + ad0.evoMustFirstLv;
         var chipName0:String = ad0.getChipName();
         var d0:MustDefine = new MustDefine();
         if(mustLv0 == 11)
         {
            arr0 = [chipName0 + ";150","yearDog;60","yearHourse;60"];
         }
         if(mustLv0 == 12)
         {
            arr0 = [chipName0 + ";150","yearSnake;40","yearMouse;40","yearPig;40"];
         }
         if(mustLv0 == 13)
         {
            arr0 = [chipName0 + ";150","yearCattle;40","yearMonkey;40","yearRabbit;40"];
         }
         if(mustLv0 == 14)
         {
            arr0 = [chipName0 + ";200","yaRock;90","demStone;300"];
         }
         if(mustLv0 == 15)
         {
            arr0 = [chipName0 + ";200","yaRock;90","demStone;350"];
         }
         if(mustLv0 == 16)
         {
            arr0 = [chipName0 + ";300","yaStone;100"];
         }
         d0.lv = 80;
         d0.coin = 0;
         d0.inThingsDataByArr(arr0);
         return d0;
      }
   }
}

