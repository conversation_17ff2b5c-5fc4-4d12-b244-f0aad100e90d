package dataAll._app.union.building.federal
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.union.building.UnionBuildingData;
   import dataAll._app.union.building.cooking.CookingState;
   import dataAll._app.union.building.define.UnionSendTaskDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class UnionSendTaskData
   {
      public var name:String = "";
      
      public var save:UnionSendTaskSave;
      
      public var def:UnionSendTaskDefine;
      
      private var buildingData:UnionBuildingData;
      
      public function UnionSendTaskData()
      {
         super();
      }
      
      public function inData_bySave(s0:UnionSendTaskSave, buildingData0:UnionBuildingData) : void
      {
         this.name = s0.name;
         this.save = s0;
         this.def = Gaming.defineGroup.union.building.getSendTaskDefine(this.name);
         this.buildingData = buildingData0;
      }
      
      public function newDayCtrl() : void
      {
         this.save.onHookTime = 0;
         this.save.state = CookingState.NO;
         this.save.surplusTime = 0;
      }
      
      public function getMaxSurplusTime() : Number
      {
         return 300;
      }
      
      public function getSurplusTimeStr() : String
      {
         var state0:String = this.getState();
         if(state0 == CookingState.ING)
         {
            return "剩余时间：" + ComMethod.getTimeStrTwo(this.save.surplusTime);
         }
         if(state0 == CookingState.WAITING)
         {
            return "排队中";
         }
         return "";
      }
      
      public function getState() : String
      {
         return this.save.state;
      }
      
      public function setState(state0:String) : void
      {
         this.save.state = state0;
         if(state0 == CookingState.ING)
         {
            this.save.surplusTime = this.getMaxSurplusTime();
         }
         else
         {
            this.save.surplusTime = 0;
         }
      }
      
      public function getBuildGrowLv() : int
      {
         var da0:UnionBuildingData = this.buildingData;
         if(da0 is UnionBuildingData)
         {
            return da0.save.lv;
         }
         return 1;
      }
      
      public function getGiftAddDefineGroup() : GiftAddDefineGroup
      {
         var num0:int = Gaming.defineGroup.union.building.property.getPropertyValue("sendTaskGift",this.getBuildGrowLv());
         var d0:GiftAddDefineGroup = new GiftAddDefineGroup();
         d0.addGiftByStr("things;militarySupplies;" + num0);
         return d0;
      }
      
      public function getTipStr() : String
      {
         var str0:String = "<yellow 所需时间：/>" + Math.round(this.getMaxSurplusTime() / 60) + "分钟";
         str0 += "\n\n<b>奖励：</b>";
         str0 += "\n" + this.getGiftAddDefineGroup().getDescription(1);
         return str0 + ("\n\n<b><orange " + this.buildingData.def.lvName + "越高，所获得的奖励也会越多。/></b>");
      }
      
      public function FTimerSecond() : Boolean
      {
         var t0:Number = NaN;
         var overB0:Boolean = false;
         if(this.getState() == CookingState.ING)
         {
            t0 = this.save.surplusTime;
            t0--;
            ++this.save.onHookTime;
            if(t0 <= 0)
            {
               t0 = 0;
               this.setState(CookingState.COMPLETE);
               overB0 = true;
            }
            this.save.surplusTime = t0;
         }
         return overB0;
      }
   }
}

