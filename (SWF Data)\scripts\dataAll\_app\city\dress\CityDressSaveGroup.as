package dataAll._app.city.dress
{
   import com.sounto.cf.NumberEncodeObj;
   import com.sounto.utils.ClassProperty;
   
   public class CityDressSaveGroup
   {
      public static var pro_arr:Array = null;
      
      public var arr:Array = [];
      
      public var idIndex:int = 0;
      
      public var space:NumberEncodeObj = new NumberEncodeObj();
      
      public function CityDressSaveGroup()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.arr = ClassProperty.copySaveArray(obj0["arr"],CityDressSave);
      }
      
      public function getNewId() : String
      {
         ++this.idIndex;
         if(this.idIndex > 1000000)
         {
            this.idIndex = 0;
         }
         return this.idIndex + "";
      }
   }
}

