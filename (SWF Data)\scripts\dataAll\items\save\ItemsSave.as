package dataAll.items.save
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.Sounto64;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.goods.define.CrrencyGroup;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.save.EquipSave;
   import dataAll.equip.vehicle.VehicleDefine;
   import dataAll.equip.vehicle.VehicleSave;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.creator.ItemsUpgradeCtrl;
   
   public class ItemsSave
   {
      public static var pro_arr:Array = [];
      
      public var id:String = "";
      
      public var site:Number = 0;
      
      public var itemsType:String = "";
      
      private var _itemsLevel:String = "";
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var color:String = "white";
      
      public var newB:Boolean = false;
      
      public function ItemsSave()
      {
         super();
         this.itemsLevel = 1;
         this.site = 0;
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function getSellPrice(type0:String) : Number
      {
         return Gaming.defineGroup.normal.getItemsSellPrice(this.getTrueLevel(),this.color,type0);
      }
      
      public function getCnName() : String
      {
         return this.cnName;
      }
      
      public function getBaseCn() : String
      {
         return this.cnName;
      }
      
      public function isImportantB() : Boolean
      {
         if(this.getStrengthenLv() > 0)
         {
            return true;
         }
         if(this.getEvoLv() > 1)
         {
            return true;
         }
         if(this.isOnceGetB())
         {
            return true;
         }
         return false;
      }
      
      public function isOnceGetB() : Boolean
      {
         return false;
      }
      
      final public function getColorCnName(color0:String = "") : String
      {
         if(color0 == "")
         {
            color0 = this.color;
         }
         return ComMethod.color(this.getCnName(),EquipColor.htmlColor(color0));
      }
      
      final public function getColorCnName2(color0:String = "") : String
      {
         if(color0 == "")
         {
            color0 = this.color;
         }
         return ComMethod.color(this.getCnName(),EquipColor.htmlColor(color0));
      }
      
      public function getTrueLevel() : int
      {
         return this.itemsLevel;
      }
      
      public function getLabel() : String
      {
         if(this is EquipSave)
         {
            return (this as EquipSave).imgName;
         }
         return this.name;
      }
      
      public function set itemsLevel(v0:Number) : void
      {
         this._itemsLevel = Sounto64.encode(String(v0));
      }
      
      public function get itemsLevel() : Number
      {
         return Number(Sounto64.decode(this._itemsLevel));
      }
      
      public function getStrengthenLv() : int
      {
         return 0;
      }
      
      public function getEvoLv() : int
      {
         return 0;
      }
      
      public function setStrengthenLvAndMax(lv0:int) : void
      {
      }
      
      public function copyOne() : ItemsSave
      {
         return null;
      }
      
      public function isArenaGiftB() : Boolean
      {
         return false;
      }
      
      public function isMoreRedB() : Boolean
      {
         return false;
      }
      
      public function getAllUpgradeConverStoneNum() : int
      {
         return ItemsUpgradeCtrl.getAllConverStone(this.itemsLevel,this.getAddLevel(),this.color);
      }
      
      public function getTrueName() : String
      {
         return this.name;
      }
      
      public function getChildType() : String
      {
         return "";
      }
      
      public function getChildTypeCnName() : String
      {
         return "";
      }
      
      public function getColorSortId() : String
      {
         return EquipColor.getID_byType(this.color);
      }
      
      public function getLevelSortId() : String
      {
         return TextWay.toNum(String(999 - this.getTrueLevel()),3);
      }
      
      public function getSimulateData(pd0:NormalPlayerData) : IO_ItemsData
      {
         return null;
      }
      
      public function getGetTime() : String
      {
         var s0:ComplexSave = this as ComplexSave;
         if(Boolean(s0))
         {
            return s0.getTime;
         }
         return "";
      }
      
      public function setGetTime(v0:String) : void
      {
         var s0:ComplexSave = this as ComplexSave;
         if(Boolean(s0))
         {
            s0.getTime = v0;
         }
      }
      
      public function getSeverTime() : String
      {
         var s0:ComplexSave = this as ComplexSave;
         if(Boolean(s0))
         {
            return s0.severTime;
         }
         return "";
      }
      
      public function setSeverTime(v0:String) : void
      {
         var s0:ComplexSave = this as ComplexSave;
         if(Boolean(s0))
         {
            s0.severTime = v0;
         }
      }
      
      public function getInHouseTime() : String
      {
         var s0:ComplexSave = this as ComplexSave;
         if(Boolean(s0))
         {
            return s0.inHouseTime;
         }
         return "";
      }
      
      public function setInHouseTime(v0:String) : void
      {
         var s0:ComplexSave = this as ComplexSave;
         if(Boolean(s0))
         {
            s0.inHouseTime = v0;
         }
      }
      
      public function getLockB() : Boolean
      {
         var s0:LockItemsSave = this as LockItemsSave;
         if(Boolean(s0))
         {
            return s0.lockB;
         }
         return false;
      }
      
      public function setLockB(v0:Boolean) : void
      {
         var s0:LockItemsSave = this as LockItemsSave;
         if(Boolean(s0))
         {
            s0.lockB = v0;
         }
      }
      
      public function getAddLevel() : int
      {
         return 0;
      }
      
      public function getCrrencyGroup() : CrrencyGroup
      {
         var vd0:VehicleDefine = null;
         var c0:CrrencyGroup = null;
         var name0:String = this.getLabel();
         if(this is VehicleSave)
         {
            vd0 = Gaming.defineGroup.vehicle.getDefine(name0);
            if(Boolean(vd0))
            {
               name0 = vd0.getBaseDefine().name;
            }
         }
         var d0:GoodsDefine = Gaming.defineGroup.goods.getDefine(name0);
         if(d0 is GoodsDefine)
         {
            if(d0.noOtherPathB)
            {
               c0 = new CrrencyGroup();
               c0.inGoodsDefine(d0);
               return c0;
            }
         }
         return null;
      }
   }
}

