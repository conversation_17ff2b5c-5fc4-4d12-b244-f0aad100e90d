package dataAll._app.worldMap.save
{
   import com.common.text.TextWay;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.Sounto64;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.ObjectMethod;
   import com.sounto.utils.StringMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._app.task.define.TaskDefine;
   import dataAll._app.worldMap.WorldMapDataGroup;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.level.DemonDataCtrl;
   import dataAll.level.LevelCountSave;
   import dataAll.level.LevelDiffGetting;
   import dataAll.level.define.LevelDefine;
   import dataAll.level.modeDiy.ModeDiyDefine;
   import dataAll.ui.GatherColor;
   
   public class WorldMapSave
   {
      private static const ZERO:WorldMapSave = new WorldMapSave();
      
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      private var def:WorldMapDefine = null;
      
      private var diyDef:ModeDiyDefine = null;
      
      public var name:String = "";
      
      public var winB:Boolean = false;
      
      public var levelName:String = "";
      
      public var countSave:LevelCountSave = new LevelCountSave();
      
      public var winNum:Number = 0;
      
      public var failNum:Number = 0;
      
      public var quitNum:Number = 0;
      
      private var _challengeLevel:String = "";
      
      public var challengeNumArr:Array = ["0","0","0","0","0","0"];
      
      public var timeObj:Object = {};
      
      private var bossTip:String = "";
      
      private var bossCnArr:Array = null;
      
      public function WorldMapSave()
      {
         super();
         this.maxEndlessGrade = 0;
         this.maxEndlessScore = 0;
         this.challengeLevel = 0;
         this.diffUnlock = 1;
         this.setAllChallengeNum(0);
      }
      
      public function get diffUnlock() : Number
      {
         return this.CF.getAttribute("diffUnlock");
      }
      
      public function set diffUnlock(v0:Number) : void
      {
         this.CF.setAttribute("diffUnlock",v0);
      }
      
      public function get maxEndlessScore() : Number
      {
         return this.CF.getAttribute("maxEndlessScore");
      }
      
      public function set maxEndlessScore(v0:Number) : void
      {
         this.CF.setAttribute("maxEndlessScore",v0);
      }
      
      public function get maxEndlessGrade() : Number
      {
         return this.CF.getAttribute("maxEndlessGrade");
      }
      
      public function set maxEndlessGrade(v0:Number) : void
      {
         this.CF.setAttribute("maxEndlessGrade",v0);
      }
      
      public function get demStone() : Number
      {
         return this.CF.getAttribute("demStone");
      }
      
      public function set demStone(v0:Number) : void
      {
         this.CF.setAttribute("demStone",v0);
      }
      
      public function get demBall() : Number
      {
         return this.CF.getAttribute("demBall");
      }
      
      public function set demBall(v0:Number) : void
      {
         this.CF.setAttribute("demBall",v0);
      }
      
      public function get dm() : Number
      {
         return this.CF.getAttribute("dm");
      }
      
      public function set dm(v0:Number) : void
      {
         this.CF.setAttribute("dm",v0);
      }
      
      public function get db() : Number
      {
         return this.CF.getAttribute("db");
      }
      
      public function set db(v0:Number) : void
      {
         this.CF.setAttribute("db",v0);
      }
      
      public function get demWin() : Number
      {
         return this.CF.getAttribute("demWin");
      }
      
      public function set demWin(v0:Number) : void
      {
         this.CF.setAttribute("demWin",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.timeObj = ClassProperty.copyObj(obj0["timeObj"]);
      }
      
      public function getSaveObj(oldObj0:Object) : Object
      {
         var obj0:Object = ClassProperty.thinSaveObj(this,ZERO,pro_arr);
         obj0.name = this.name;
         if(Gaming.isLocal())
         {
            this.checkSaveObj(oldObj0,obj0);
         }
         return obj0;
      }
      
      private function checkSaveObj(oldObj0:Object, obj0:Object) : Boolean
      {
         var s0:WorldMapSave = new WorldMapSave();
         s0.inData_byObj(obj0);
         var a:Object = ClassProperty.copyObj(s0);
         var b:Object = ClassProperty.copyObj(oldObj0);
         var bb0:Boolean = ObjectMethod.samePan(a,b);
         if(bb0 == false)
         {
            INIT.showError("地图 " + this.name + "：检测异常不同");
         }
         return bb0;
      }
      
      public function newWeek(timeStr0:String) : void
      {
         this.demStone = 0;
         this.demBall = 0;
         this.demWin = 0;
         this.timeObj = {};
      }
      
      public function win(diff0:int, mapModel:String) : void
      {
         ++this.winNum;
         this.winB = true;
         if(mapModel == MapMode.CHALLENGE)
         {
            this.addChallengeNum(diff0,-1);
         }
         else if(mapModel == MapMode.REGULAR)
         {
            if(this.diffUnlock <= diff0)
            {
               this.diffUnlock = diff0 + 1;
            }
         }
         else if(mapModel == MapMode.DEMON)
         {
         }
      }
      
      public function setLevelName(name0:String) : void
      {
         if(name0 != this.levelName)
         {
            this.bossTip = "";
            this.bossCnArr = null;
         }
         this.levelName = name0;
      }
      
      public function getLevelName() : String
      {
         var d0:LevelDefine = null;
         if(this.levelName == "")
         {
            return this.getDefine().getLevelName();
         }
         d0 = Gaming.defineGroup.level.getDefine(this.levelName);
         if(d0 is LevelDefine)
         {
            return this.levelName;
         }
         return this.getDefine().getLevelName();
      }
      
      public function getLevelDefine() : LevelDefine
      {
         var d0:LevelDefine = null;
         if(this.levelName != "")
         {
            d0 = Gaming.defineGroup.level.getDefine(this.levelName);
         }
         if(!d0)
         {
            d0 = Gaming.defineGroup.level.getDefine(this.getDefine().getLevelName());
         }
         return d0;
      }
      
      public function getFirstLevelName() : String
      {
         return this.getDefine().getLevelName();
      }
      
      public function getDefine() : WorldMapDefine
      {
         if(!this.def)
         {
            this.def = Gaming.defineGroup.worldMap.getDefine(this.name);
         }
         return this.def;
      }
      
      public function getNormalLv() : int
      {
         var d0:LevelDefine = Gaming.defineGroup.level.getDefine(this.getLevelName());
         if(Boolean(d0))
         {
            return d0.info.enemyLv;
         }
         return 0;
      }
      
      public function getBossTip(levelD0:LevelDefine = null) : String
      {
         var bossStr0:String = null;
         var bossD0:NormalBodyDefine = null;
         var skillTip0:String = null;
         if(this.bossTip == "")
         {
            bossStr0 = this.getBossCnStr(levelD0);
            if(bossStr0 != "")
            {
               bossD0 = Gaming.defineGroup.body.getCnDefine(bossStr0);
               if(Boolean(bossD0))
               {
                  skillTip0 = bossD0.getBossSkillTip("#00FFFF");
                  if(skillTip0 != "")
                  {
                     bossStr0 = "<i1>|<yellow <b>" + bossStr0 + "</b>/> 特别技能：\n" + skillTip0;
                  }
                  else
                  {
                     bossStr0 = "首领：<yellow " + bossStr0 + "/>";
                  }
               }
               this.bossTip = bossStr0;
            }
         }
         return this.bossTip;
      }
      
      public function getBossCnStr(levelD0:LevelDefine = null, noStr0:String = "") : String
      {
         var arr0:Array = this.getBossCnArr(levelD0);
         if(Boolean(arr0) && arr0.length > 0)
         {
            return StringMethod.concatStringArr(arr0,10);
         }
         return noStr0;
      }
      
      public function getBossCnArr(levelD0:LevelDefine = null) : Array
      {
         var d0:LevelDefine = this.getLevelDefine();
         if(Boolean(levelD0) && levelD0 != d0)
         {
            return levelD0.unitG.getBossCnArr();
         }
         if(!this.bossCnArr)
         {
            if(Boolean(d0))
            {
               this.bossCnArr = d0.unitG.getBossCnArr();
            }
         }
         return this.bossCnArr;
      }
      
      public function getOneBossCn() : String
      {
         var arr0:Array = this.getBossCnArr();
         if(Boolean(arr0) && arr0.length > 0)
         {
            return arr0[0];
         }
         return "";
      }
      
      public function demonDropEvent(diff0:int, time0:Number) : void
      {
         ++this.demWin;
         this.setDemonWinTime(diff0,time0);
      }
      
      public function getDemonDiyName() : String
      {
         return this.getDefine().getDemonDiy();
      }
      
      public function getDemonDiyDef() : ModeDiyDefine
      {
         var diy0:String = this.getDefine().getDemonDiy();
         if(Boolean(this.diyDef))
         {
            if(this.diyDef.name != diy0)
            {
               this.diyDef = null;
            }
         }
         if(!this.diyDef)
         {
            this.diyDef = Gaming.defineGroup.modelDiy.getDefine(diy0);
         }
         return this.diyDef;
      }
      
      public function getDemonWinTime(diff0:int) : Number
      {
         var demDiff0:int = diff0;
         if(!this.timeObj.hasOwnProperty(demDiff0))
         {
            return 0;
         }
         return this.timeObj[demDiff0];
      }
      
      private function setDemonWinTime(diff0:int, time0:Number) : Boolean
      {
         time0 = Math.ceil(time0);
         var demDiff0:int = diff0;
         var save0:Number = this.getDemonWinTime(diff0);
         if(save0 == 0 || time0 < save0)
         {
            this.timeObj[demDiff0] = time0;
            return true;
         }
         return false;
      }
      
      private function getBestDiffWinStr() : String
      {
         var time0:Number = NaN;
         var max0:int = this.getDemonDiffMax();
         for(var i:int = max0; i >= 0; i--)
         {
            time0 = this.getDemonWinTime(i);
            if(time0 > 0)
            {
               return "本周最快通关时间：<orange <b>" + ComMethod.getTimeStrTwo(time0) + "</b>/><gray2 (" + LevelDiffGetting.getCnName(i,MapMode.DEMON) + ")/>";
            }
         }
         return "";
      }
      
      public function getBestDiffWin() : int
      {
         var time0:Number = NaN;
         var max0:int = this.getDemonDiffMax();
         for(var i:int = max0; i >= 0; i--)
         {
            time0 = this.getDemonWinTime(i);
            if(time0 > 0)
            {
               return i;
            }
         }
         return -1;
      }
      
      public function getFastWinTime() : Number
      {
         var time0:Number = NaN;
         var fast0:Number = 999;
         var max0:int = this.getDemonDiffMax();
         for(var i:int = max0; i >= 0; i--)
         {
            time0 = this.getDemonWinTime(i);
            if(time0 > 0)
            {
               if(time0 < fast0)
               {
                  fast0 = time0;
               }
            }
         }
         return fast0;
      }
      
      public function getStonePro(diff0:int) : Number
      {
         return DemonDataCtrl.getStonePro(diff0,this.getDefine());
      }
      
      public function getStoneMax(diff0:int) : Number
      {
         return DemonDataCtrl.getStoneMax(diff0,this.getDefine());
      }
      
      public function getStoneMaxAll() : int
      {
         return DemonDataCtrl.getStoneMaxAll(this.getDefine());
      }
      
      public function getBallPro(diff0:int) : Number
      {
         return DemonDataCtrl.getBallPro(diff0,this.getDefine());
      }
      
      public function getBallMax(diff0:int) : Number
      {
         return DemonDataCtrl.getBallMax(diff0,this.getDefine());
      }
      
      public function getBallMaxAll() : int
      {
         return DemonDataCtrl.getBallMaxAll(this.getDefine());
      }
      
      public function getDemWinMax() : int
      {
         return 5;
      }
      
      public function getDemWinSurplus() : int
      {
         return this.getDemWinMax() - this.demWin;
      }
      
      public function getDemBtnTip() : String
      {
         return "";
      }
      
      public function getDemModeTip(dataG0:WorldMapDataGroup, tipB0:Boolean = false) : String
      {
         var s0:String = null;
         var timeStr0:String = null;
         var error0:String = DemonDataCtrl.openPan(dataG0.playerData);
         if(error0 != "")
         {
            return error0;
         }
         var diyD0:ModeDiyDefine = this.getDemonDiyDef();
         var d0:WorldMapDefine = this.getDefine();
         if(diyD0.closeB)
         {
            return "本周未开放";
         }
         s0 = "本周无双水晶掉落：" + TextMethod.colorMustNum(this.demStone,this.getStoneMaxAll(),GatherColor.bluenessColor);
         s0 += "\n本周万能球掉落：" + TextMethod.colorMustNum(this.demBall,this.getBallMaxAll(),GatherColor.bluenessColor);
         s0 += "\n本周通关次数：" + TextMethod.colorMustNum(this.demWin,this.getDemWinMax(),GatherColor.bluenessColor,"00FF00","",false,TextMethod.color("(已用完)","#FF0000"));
         if(tipB0)
         {
            s0 += "\n\n<graydark 鼠标放在首领名字上可查看修罗技能/>";
         }
         timeStr0 = this.getBestDiffWinStr();
         if(timeStr0 != "")
         {
            s0 = timeStr0 + "\n\n" + s0;
         }
         return s0;
      }
      
      public function getDemSkillGatherTip() : String
      {
         var s0:String = "<b><yellow 首领：/></b>" + this.getBossCnStr(null,"随机");
         if(Boolean(this.def.getDemon9BossCnArr()))
         {
            s0 += "、<orange [修罗九]/>" + StringMethod.concatStringArr(this.def.getDemon9BossCnArr(),99);
         }
         return s0 + ("\n" + this.def.getDemSkillGatherTip());
      }
      
      public function getDemModeActied(dataG0:WorldMapDataGroup) : Boolean
      {
         if(DemonDataCtrl.openPan(dataG0.playerData) != "")
         {
            return false;
         }
         var diyD0:ModeDiyDefine = this.getDemonDiyDef();
         if(diyD0.closeB)
         {
            return false;
         }
         if(this.getDemWinSurplus() <= 0)
         {
            return false;
         }
         return true;
      }
      
      public function getDemDiffTip(diff0:int) : String
      {
         var diff8:int = 0;
         var diff9:int = 0;
         var s0:String = null;
         var winTime0:Number = NaN;
         var extraDiffSkillTip0:String = null;
         var boss9CnArr0:Array = null;
         var diyD0:ModeDiyDefine = this.getDemonDiyDef();
         var d0:WorldMapDefine = this.getDefine();
         if(diyD0.closeB)
         {
            return "本周未开放";
         }
         diff8 = 7;
         diff9 = 8;
         if(diff0 > this.getDemonDiffMax())
         {
            if(diff0 == diff9 && Boolean(d0.getDemon9BossCnArr()))
            {
               return "11月18日开放";
            }
            return "未开放";
         }
         s0 = "无双水晶每周掉落上限：<blue <b>" + this.getStoneMax(diff0) + "</b>/>";
         s0 += "\n万能球每周掉落上限：<blue <b>" + this.getBallMax(diff0) + "</b>/>";
         winTime0 = this.getDemonWinTime(diff0);
         if(winTime0 > 0)
         {
            s0 = "本周最快通关时间：<orange <b>" + ComMethod.getTimeStrTwo(winTime0) + "</b>/>\n\n" + s0;
         }
         s0 += "\n";
         extraDiffSkillTip0 = this.def.getExtraDemSkillTip(diff0);
         if(extraDiffSkillTip0 != "")
         {
            s0 += "\n<b><yellow 首领额外技能：/></b>\n" + extraDiffSkillTip0;
         }
         if(diff0 == diff9)
         {
            boss9CnArr0 = d0.getDemon9BossCnArr();
            if(Boolean(boss9CnArr0))
            {
               s0 += "\n第二位首领：<b><yellow " + boss9CnArr0 + "/></b>";
               s0 += "\n<purple 每位首领都有“化锁”技能。/>";
            }
         }
         return s0;
      }
      
      public function getDemDiffTitle() : String
      {
         var diyD0:ModeDiyDefine = this.getDemonDiyDef();
         return this.getDefine().cnName + diyD0.getTitle();
      }
      
      public function getDemonDiffMax() : int
      {
         return LevelDiffGetting.getDemonDiffMax(this.getDefine());
      }
      
      public function setAllChallengeNum(num0:int) : void
      {
         var len0:int = int(LevelDiffGetting.coinMul.length);
         for(var i:int = 0; i < len0; i++)
         {
            this.setChallengeNum(i,num0);
         }
      }
      
      private function setChallengeNum(diff0:int, num0:int) : void
      {
         this.challengeNumArr[diff0] = TextWay.toCode(String(num0));
      }
      
      public function addChallengeNum(diff0:int, num0:int) : void
      {
         var now0:int = this.getChallengeNum(diff0);
         now0 += num0;
         if(now0 < 0)
         {
            now0 = 0;
         }
         this.setChallengeNum(diff0,now0);
      }
      
      public function getChallengeNum(diff0:int) : int
      {
         var str0:String = this.challengeNumArr[diff0];
         if(!str0)
         {
            return 0;
         }
         return int(TextWay.getText(str0));
      }
      
      public function getChallengeMaxWorldMapDefine() : WorldMapDefine
      {
         var d0:TaskDefine = Gaming.defineGroup.task.getMainByMaxLevel(this.challengeLevel);
         return d0.getWorldMapDefine();
      }
      
      public function winOtherOne(name0:String, diff0:int, sg0:WorldMapSaveGroup, mapModel0:String) : void
      {
         var s0:WorldMapSave = null;
         if(this.challengeLevel > 0 && this.winB && name0 != this.name)
         {
            s0 = sg0.getSave(name0);
            if(s0 is WorldMapSave)
            {
               if(mapModel0 == MapMode.REGULAR)
               {
                  if(s0.getNormalLv() == this.challengeLevel)
                  {
                     this.addChallengeNum(diff0,1);
                  }
               }
            }
         }
      }
      
      public function set challengeLevel(v0:Number) : void
      {
         this._challengeLevel = Sounto64.encode(String(v0));
      }
      
      public function get challengeLevel() : Number
      {
         return Number(Sounto64.decode(this._challengeLevel));
      }
      
      public function inEndlessScore(score0:Number, grade0:int) : void
      {
         if(this.maxEndlessScore < score0)
         {
            this.maxEndlessScore = score0;
         }
         if(this.maxEndlessGrade < grade0)
         {
            this.maxEndlessGrade = grade0;
         }
      }
      
      public function levelInEndlessScore(score0:Number, grade0:int) : void
      {
         this.inEndlessScore(score0,grade0);
      }
      
      public function getCountString() : String
      {
         var str0:String = "";
         str0 += "winNum:" + this.winNum + "\n";
         return str0 + ("failNum:" + this.failNum);
      }
   }
}

