package dataAll.bullet
{
   import com.sounto.utils.ClassProperty;
   import dataAll._base.IO_CanThinSave;
   
   public class BulletCritDefine implements IO_CanThinSave
   {
      public static var pro_arr:Array = [];
      
      protected var V:Number = Math.random() / 5 + 0.01;
      
      private var _mul:Number = 0;
      
      private var _pro:Number = 0;
      
      public function BulletCritDefine()
      {
         super();
      }
      
      public function getProArr() : Array
      {
         return pro_arr;
      }
      
      public function set mul(v0:Number) : void
      {
         this._mul = v0 / this.V;
      }
      
      public function get mul() : Number
      {
         return this._mul * this.V;
      }
      
      public function set pro(v0:Number) : void
      {
         this._pro = v0 / this.V;
      }
      
      public function get pro() : Number
      {
         return this._pro * this.V;
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function getHurt(v0:Number) : Number
      {
         if(Math.random() < this.pro)
         {
            return v0 * (1 + this.mul);
         }
         return v0;
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function copy() : BulletCritDefine
      {
         var d0:BulletCritDefine = new BulletCritDefine();
         ClassProperty.inData(d0,this,pro_arr);
         return d0;
      }
      
      public function haveDataB() : Boolean
      {
         return this.mul > 0 && this.pro > 0;
      }
   }
}

