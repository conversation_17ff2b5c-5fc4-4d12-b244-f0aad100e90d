package dataAll._app.union.building.geology
{
   import com.sounto.utils.ClassProperty;
   import dataAll._app.union.building.define.UnionGeologyThingsDefine;
   
   public class UnionGeologySave
   {
      public static var pro_arr:Array = [];
      
      public var thingsObj:Object = {};
      
      public var haveTime:Number = 0;
      
      public function UnionGeologySave()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.thingsObj = ClassProperty.copySaveObj(obj0["thingsObj"],UnionGeologyThingsSave);
         this.fixedSave();
      }
      
      public function fixedSave() : void
      {
         var d0:UnionGeologyThingsDefine = null;
         var s0:UnionGeologyThingsSave = null;
         var defineArr0:Array = Gaming.defineGroup.union.building.geologyBase.arr;
         for each(d0 in defineArr0)
         {
            if(this.thingsObj.hasOwnProperty(d0.name) == false)
            {
               s0 = new UnionGeologyThingsSave();
               s0.inDataByDefine(d0);
               this.thingsObj[s0.name] = s0;
            }
         }
      }
   }
}

