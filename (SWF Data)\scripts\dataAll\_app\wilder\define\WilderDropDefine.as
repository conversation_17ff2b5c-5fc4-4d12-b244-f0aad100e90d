package dataAll._app.wilder.define
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.wilder.WilderDiff;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipType;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.gift.define.GiftType;
   
   public class WilderDropDefine
   {
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var index:int = 0;
      
      public var cnName:String = "";
      
      public var gift:GiftAddDefine = new GiftAddDefine();
      
      public function WilderDropDefine()
      {
         super();
         this.num = 0;
         this.pro = 0;
         this.d6 = 0;
         this.d5 = 0;
         this.d4 = 0;
         this.d3 = 0;
         this.d2 = 0;
         this.d1 = 0;
         this.d0 = 0;
         this.noDropMax = 0;
      }
      
      public function get num() : Number
      {
         return this.CF.getAttribute("num");
      }
      
      public function set num(v0:Number) : void
      {
         this.CF.setAttribute("num",v0);
      }
      
      public function get pro() : Number
      {
         return this.CF.getAttribute("pro");
      }
      
      public function set pro(v0:Number) : void
      {
         this.CF.setAttribute("pro",v0);
      }
      
      public function get d7() : Number
      {
         return this.CF.getAttribute("d7");
      }
      
      public function set d7(v0:Number) : void
      {
         this.CF.setAttribute("d7",v0);
      }
      
      public function get d6() : Number
      {
         return this.CF.getAttribute("d6");
      }
      
      public function set d6(v0:Number) : void
      {
         this.CF.setAttribute("d6",v0);
      }
      
      public function get d5() : Number
      {
         return this.CF.getAttribute("d5");
      }
      
      public function set d5(v0:Number) : void
      {
         this.CF.setAttribute("d5",v0);
      }
      
      public function get d4() : Number
      {
         return this.CF.getAttribute("d4");
      }
      
      public function set d4(v0:Number) : void
      {
         this.CF.setAttribute("d4",v0);
      }
      
      public function get d3() : Number
      {
         return this.CF.getAttribute("d3");
      }
      
      public function set d3(v0:Number) : void
      {
         this.CF.setAttribute("d3",v0);
      }
      
      public function get d2() : Number
      {
         return this.CF.getAttribute("d2");
      }
      
      public function set d2(v0:Number) : void
      {
         this.CF.setAttribute("d2",v0);
      }
      
      public function get d1() : Number
      {
         return this.CF.getAttribute("d1");
      }
      
      public function set d1(v0:Number) : void
      {
         this.CF.setAttribute("d1",v0);
      }
      
      public function get d0() : Number
      {
         return this.CF.getAttribute("d0");
      }
      
      public function set d0(v0:Number) : void
      {
         this.CF.setAttribute("d0",v0);
      }
      
      public function get noDropMax() : Number
      {
         return this.CF.getAttribute("noDropMax");
      }
      
      public function set noDropMax(v0:Number) : void
      {
         this.CF.setAttribute("noDropMax",v0);
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var x0:int = 0;
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         this.gift.inData_byXML(xml0.gift[0]);
         if(this.d6 == 0)
         {
            x0 = 0;
         }
      }
      
      public function isWeaponB() : Boolean
      {
         var d0:EquipDefine = null;
         if(this.gift.type == GiftType.equip)
         {
            d0 = Gaming.defineGroup.getAllEquipDefine(this.gift.name);
            if(Boolean(d0))
            {
               if(d0.type == EquipType.WEAPON)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function getDropStr(diff0:String, dropAdd0:Number = 0) : String
      {
         var pro0:Number = this.getPro(diff0) * (1 + dropAdd0);
         var num0:int = this.getNum(diff0);
         if(pro0 <= 0 || num0 <= 0)
         {
            return "";
         }
         var proStr0:String = "";
         var numStr0:String = "";
         if(this.num == 0)
         {
            proStr0 = WilderDiff.getProNameByValue(pro0);
         }
         else
         {
            numStr0 = num0 + "个";
            proStr0 = WilderDiff.getProNameByValue(pro0);
         }
         numStr0 = ComMethod.color(num0 + "个","#00FF00");
         proStr0 = ComMethod.color(proStr0,"#FF99FF");
         var s0:String = numStr0 + ComMethod.color(this.cnName,"#FFFF00");
         if(pro0 < 1 || this.index == 0)
         {
            s0 = proStr0 + (this.gift.isThingsOrBaseB() ? "获得" : "掉落") + s0;
         }
         return s0;
      }
      
      public function getTruePro(diff0:String, noValue0:Number, dropAdd0:Number = 0) : Number
      {
         var pro0:Number = this.getPro(diff0);
         var max0:Number = this.noDropMax;
         if(max0 > 0)
         {
            if(noValue0 >= max0 * 2)
            {
               pro0 = 1;
            }
            else if(noValue0 >= max0)
            {
               pro0 *= 2;
            }
         }
         return pro0 * (1 + dropAdd0);
      }
      
      public function getPro(diff0:String) : Number
      {
         var pro0:Number = this.pro;
         var num0:int = this.num;
         if(num0 > 0)
         {
            pro0 = this.getDiff(diff0);
         }
         return pro0;
      }
      
      public function getNum(diff0:String) : Number
      {
         var num0:int = this.num;
         if(num0 == 0)
         {
            num0 = this.getDiff(diff0);
         }
         return num0;
      }
      
      public function getNoDropValue(diff0:String) : Number
      {
         return this.getPro(diff0);
      }
      
      public function getGiftAddDefineGroup(diff0:String) : GiftAddDefineGroup
      {
         var num0:Number = this.getNum(diff0);
         if(num0 <= 0)
         {
            return null;
         }
         var g0:GiftAddDefineGroup = new GiftAddDefineGroup();
         var gift0:GiftAddDefine = this.gift.clone();
         gift0.num *= num0;
         g0.addGift(gift0);
         return g0;
      }
      
      public function getDiff(diff0:String) : Number
      {
         return this["d" + diff0];
      }
   }
}

