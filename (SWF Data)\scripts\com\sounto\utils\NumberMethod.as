package com.sounto.utils
{
   import com.sounto.oldUtils.ComMethod;
   
   public class NumberMethod
   {
      private static const YI_MAX:Number = 9999999999;
      
      private static const ALL_MAX:Number = 999999999999;
      
      public static var YI_B:Boolean = true;
      
      public function NumberMethod()
      {
         super();
      }
      
      public static function converToNumber(s0:Object) : Object
      {
         var wanYiF0:int = 0;
         var wanF0:int = 0;
         var yiF0:int = 0;
         var v0:Number = Number(s0);
         if(isNaN(v0))
         {
            wanYiF0 = int(s0.indexOf("万亿"));
            if(wanYiF0 > 0)
            {
               v0 = Number(s0.substr(0,wanYiF0)) * 1000000000000;
            }
            else
            {
               wanF0 = int(s0.indexOf("万"));
               if(wanF0 > 0)
               {
                  v0 = Number(s0.substr(0,wanF0)) * 10000;
               }
               else
               {
                  yiF0 = int(s0.indexOf("亿"));
                  if(yiF0 > 0)
                  {
                     v0 = Number(s0.substr(0,yiF0)) * 100000000;
                  }
               }
            }
         }
         if(isNaN(v0))
         {
            return s0;
         }
         return v0;
      }
      
      public static function getNumInArrStr(str0:String, index0:int, nan0:Number) : Number
      {
         var strArr0:Array = null;
         var v0:Number = NaN;
         if(str0 != "")
         {
            strArr0 = str0.split(",");
            if(index0 <= strArr0.length - 1)
            {
               v0 = Number(strArr0[index0]);
               if(isNaN(v0) == false)
               {
                  return v0;
               }
            }
         }
         return nan0;
      }
      
      public static function setNumInArrStr(str0:String, v0:Number, index0:int) : String
      {
         var strArr0:Array = str0.split(",");
         if(index0 > strArr0.length - 1)
         {
            strArr0.length = index0 + 1;
         }
         strArr0[index0] = v0;
         return String(strArr0);
      }
      
      public static function toPer(v0:Number, fixedNum0:int = 1) : String
      {
         return Number(Number(v0 * 100).toFixed(fixedNum0)) + "%";
      }
      
      public static function toFixed(v0:Number, fixedNum0:int = 1) : Number
      {
         return Number(Number(v0).toFixed(fixedNum0));
      }
      
      public static function getFixedPoint(fixedNum0:int) : Number
      {
         return toFixed(1 / Math.pow(10,fixedNum0),fixedNum0);
      }
      
      public static function perStringToNumber(s0:String) : Number
      {
         var index0:int = int(s0.indexOf("%"));
         if(index0 > 0)
         {
            return Number(s0.substring(0,index0)) / 100;
         }
         return Number(s0);
      }
      
      public static function converToNormalNumber(v0:Number) : Number
      {
         var c0:Number = v0 - Math.floor(v0);
         if(c0 < 1e-7 || c0 > 0.9999999)
         {
            v0 = Math.round(v0);
         }
         return v0;
      }
      
      public static function randomInRange(min0:Number, max0:Number) : Number
      {
         return (max0 - min0) * Math.random() + min0;
      }
      
      public static function getRangeTip(min0:Number, max0:Number, kuoB0:Boolean = true) : String
      {
         var s0:String = min0 + "~" + max0;
         if(kuoB0)
         {
            s0 = "（" + s0 + "）";
         }
         return s0;
      }
      
      public static function getRandomInt(num0:Number) : int
      {
         var n0:int = Math.floor(num0);
         var c0:Number = num0 - n0;
         if(c0 > 0.000001)
         {
            if(Math.random() < c0)
            {
               n0 += 1;
            }
         }
         return n0;
      }
      
      public static function getRandom(min0:Number, max0:Number) : Number
      {
         return min0 + Math.random() * (max0 - min0);
      }
      
      public static function systemChange(txt:String, radix:uint, target:uint) : String
      {
         var num:Number = parseInt(txt,radix);
         return num.toString(target);
      }
      
      public static function getRandomPerArray(num0:int, randomMul:Number, minPer0:Number = 0, maxPer0:Number = 1) : Array
      {
         var tper0:Number = NaN;
         var min0:Number = NaN;
         var max0:Number = NaN;
         var per0:Number = NaN;
         var arr0:Array = [];
         var len0:Number = maxPer0 - minPer0;
         var den0:int = num0 + 1;
         var range0:Number = len0 / den0;
         var ranRange0:Number = range0 * randomMul;
         var beforePer0:Number = 0;
         for(var i:int = 0; i < num0; i++)
         {
            tper0 = (i + 1) * range0 + minPer0;
            min0 = tper0 - ranRange0 / 2;
            if(min0 < beforePer0)
            {
               min0 = beforePer0;
            }
            max0 = tper0 + ranRange0 / 2;
            per0 = min0 + Math.random() * (max0 - min0);
            per0 = toFixed(per0,2);
            beforePer0 = per0;
            arr0.push(per0);
         }
         return arr0;
      }
      
      public static function test() : void
      {
         var num0:int = 0;
         var perArr0:Array = null;
         for(var i:int = 0; i < 10; i++)
         {
            num0 = 1 + Math.random() * 5;
            perArr0 = getRandomPerArray(num0,0.5,0.3);
            INIT.TRACE(num0 + "：" + perArr0);
         }
      }
      
      public static function to2(num:int) : String
      {
         if(num < 10)
         {
            return "0" + num;
         }
         return String(num);
      }
      
      public static function toWan(v0:Number, color0:String = "", max0:Number = 999999) : String
      {
         return ComMethod.numberToSmall(v0,color0,max0,YI_MAX);
      }
      
      public static function toFixedWan(v0:Number, color0:String = "") : String
      {
         return ComMethod.numberToSmall(v0,color0,99999,999999999,1);
      }
      
      public static function toBigWan(v0:Number, color0:String = "", max0:Number = 999999) : String
      {
         v0 = NumberMethod.toFixed(v0,0);
         var yimax0:Number = YI_MAX;
         if(!YI_B)
         {
            yimax0 = ALL_MAX;
         }
         return ComMethod.numberToSmall(v0,color0,max0,yimax0);
      }
      
      public static function toShortLimit(v0:Number, color0:String = "") : String
      {
         var v2:Number = v0;
         var unit0:String = "";
         if(v0 > 9999999999999)
         {
            v2 = Math.ceil(v0 / 1000000000000);
            unit0 = ComMethod.WAN + ComMethod.YI;
         }
         else if(v0 > 999999999)
         {
            v2 = Math.ceil(v0 / 100000000);
            unit0 = ComMethod.YI;
         }
         else if(v0 > 99999)
         {
            v2 = Math.ceil(v0 / 10000);
            unit0 = ComMethod.WAN;
         }
         if(color0 != "" && unit0 != "")
         {
            unit0 = ComMethod.color(unit0,color0);
         }
         return v2 + unit0;
      }
      
      public static function limitRange(v0:Number, min0:Number, max0:Number) : Number
      {
         if(v0 < min0)
         {
            v0 = min0;
         }
         if(v0 > max0)
         {
            v0 = max0;
         }
         return v0;
      }
   }
}

