package dataAll._app.union.task
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   
   public class UnionTaskData
   {
      public static const MAX_NUM:int = 9999;
      
      public static var TEST_COMPLETE_B:* = false;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var save:UnionTaskSave;
      
      public var def:UnionTaskDefine;
      
      public function UnionTaskData()
      {
         super();
         this.nowNum = 0;
      }
      
      public function get nowNum() : Number
      {
         return this.CF.getAttribute("nowNum");
      }
      
      public function set nowNum(v0:Number) : void
      {
         this.CF.setAttribute("nowNum",v0);
      }
      
      public function inData_bySave(s0:UnionTaskSave, name0:String) : void
      {
         this.save = s0;
         this.def = Gaming.defineGroup.union.task.getDefine(name0);
      }
      
      public function newDayCtrl() : void
      {
         this.save.getB = false;
         this.nowNum = 0;
      }
      
      public function getTaskContribution() : void
      {
         this.save.getB = true;
      }
      
      public function isCompleteB() : Boolean
      {
         if(TEST_COMPLETE_B)
         {
            return true;
         }
         return this.nowNum >= this.def.mustNum;
      }
      
      public function canGetB() : Boolean
      {
         return !this.save.getB && this.isCompleteB();
      }
      
      public function getProcessStr() : String
      {
         if(this.nowNum >= MAX_NUM)
         {
            return ComMethod.color("已达标","#00FF00");
         }
         return ComMethod.mustColor(this.nowNum,this.def.mustNum);
      }
   }
}

