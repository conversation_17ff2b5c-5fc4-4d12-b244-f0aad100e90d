package com.sounto.motion.ik
{
   public class IK_Bone
   {
      public var x:Number = 0;
      
      public var y:Number = 0;
      
      public var rotation:Number = 0;
      
      public var ra:Number = 0;
      
      public var name:String = "";
      
      public var headJoint:IK_Joint = null;
      
      public var tailJoint:IK_Joint = null;
      
      public var length:Number = 0;
      
      public function IK_Bone()
      {
         super();
      }
      
      public function flesh() : void
      {
         this.x = this.headJoint.x;
         this.y = this.headJoint.y;
         this.ra = Math.atan2(this.tailJoint.y - this.y,this.tailJoint.x - this.x);
         this.rotation = this.ra / Math.PI * 180;
      }
   }
}

