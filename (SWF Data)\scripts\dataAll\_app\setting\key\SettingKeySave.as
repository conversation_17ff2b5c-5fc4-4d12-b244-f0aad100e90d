package dataAll._app.setting.key
{
   import com.sounto.utils.ClassProperty;
   
   public class SettingKeySave
   {
      public static var pro_arr:Array = [];
      
      public var type:String = "";
      
      public var arrObj:Object = {};
      
      public function SettingKeySave()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.arrObj = ClassProperty.copyObj(obj0["arrObj"]);
         this.suppleObj();
      }
      
      private function suppleObj() : void
      {
         var n:* = undefined;
         var keyObj0:Object = Gaming.defineGroup.keyAction.getKeyObj(this.type);
         for(n in keyObj0)
         {
            if(!this.arrObj.hasOwnProperty(n))
            {
               this.arrObj[n] = keyObj0[n].concat();
            }
         }
      }
      
      public function toDefault() : void
      {
         this.arrObj = {};
         this.suppleObj();
      }
      
      public function inObj(obj0:Object, type0:String) : void
      {
         this.type = type0;
         this.arrObj = ClassProperty.copyObj(obj0);
      }
      
      public function getCode(name0:String) : int
      {
         if(this.arrObj.hasOwnProperty(name0))
         {
            return this.arrObj[name0][0];
         }
         return -1;
      }
      
      public function getNameByCode(code0:int) : String
      {
         var n:* = undefined;
         var arr0:Array = null;
         for(n in this.arrObj)
         {
            arr0 = this.arrObj[n];
            if(arr0.indexOf(code0) >= 0)
            {
               return n;
            }
         }
         return "";
      }
      
      public function getCodeArrByNameArr(nameArr0:Array) : Array
      {
         var name0:* = null;
         var arr0:Array = [];
         for each(name0 in nameArr0)
         {
            arr0.push(this.getCode(name0));
         }
         return arr0;
      }
      
      public function getCn(name0:String) : String
      {
         var c0:int = this.getCode(name0);
         if(c0 != -1)
         {
            return Gaming.defineGroup.keyAction.getCnByKetCode(c0);
         }
         return "";
      }
      
      public function getCodeArr(name0:String) : Array
      {
         return this.arrObj[name0];
      }
      
      public function getDataArr(pubB0:Boolean) : Array
      {
         var d0:KeyActionDefine = null;
         var bb0:Boolean = false;
         var da0:KeyActionData = null;
         var arr0:Array = [];
         var defineArr0:Array = Gaming.defineGroup.keyAction.arr;
         for each(d0 in defineArr0)
         {
            if(this.arrObj.hasOwnProperty(d0.name))
            {
               bb0 = true;
               if(pubB0)
               {
                  bb0 = d0.pubB;
               }
               else
               {
                  bb0 = !d0.pubB;
               }
               if(bb0)
               {
                  da0 = new KeyActionData();
                  da0.name = d0.name;
                  da0.def = d0;
                  da0.valueArr = this.arrObj[d0.name];
                  arr0.push(da0);
               }
            }
         }
         return arr0;
      }
      
      public function setOneByName(name0:String, index0:int, keyCode0:int) : void
      {
         var arr0:Array = this.arrObj[name0];
         if(index0 == 1 && keyCode0 == 0)
         {
            arr0.splice(index0,1);
         }
         else
         {
            arr0[index0] = keyCode0;
         }
      }
      
      public function setNewKeyCodeEvent(type0:String, name0:String, keyCode0:int, beforeCode0:int) : void
      {
         var n:* = undefined;
         var arr0:Array = null;
         var f0:int = 0;
         for(n in this.arrObj)
         {
            if(type0 != this.type || name0 != n)
            {
               arr0 = this.arrObj[n];
               f0 = int(arr0.indexOf(keyCode0));
               if(f0 >= 0)
               {
                  arr0[f0] = beforeCode0;
               }
            }
         }
      }
   }
}

