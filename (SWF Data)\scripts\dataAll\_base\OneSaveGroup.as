package dataAll._base
{
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   
   public class OneSaveGroup
   {
      public static var pro_arr:Array = null;
      
      protected var saveClass:Class = OneSave;
      
      public var arr:Array = [];
      
      public var lastId:Number = 0;
      
      public var hideHB:Boolean = true;
      
      public function OneSaveGroup()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.arr = ClassProperty.copySaveArray(obj0["arr"],this.saveClass);
      }
      
      public function addSave(s0:OneSave) : void
      {
         this.lastId = this.getNewLastId();
         this.arr.push(s0);
      }
      
      public function getNewLastId() : Number
      {
         return Math.round(this.lastId + 1);
      }
      
      public function removeSave(s0:OneSave) : void
      {
         ArrayMethod.remove(this.arr,s0);
      }
      
      public function getNewSave() : OneSave
      {
         return new this.saveClass();
      }
      
      public function setNewArr(arr0:Array) : void
      {
         this.arr = arr0;
      }
      
      public function removeAll() : void
      {
         this.arr.length = 0;
      }
   }
}

import dataAll._app.edit.card.BossCardSaveGroup;

BossCardSaveGroup;

