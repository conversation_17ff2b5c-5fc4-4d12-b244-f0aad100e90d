package dataAll._app.union.info
{
   import com.adobe.serialization.json.JSON2;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.top.IO_TopDataGroup;
   
   public class UnionListInfo implements IO_TopDataGroup
   {
      public static var BAR_NUM:int = 10;
      
      public static var pro_arr:Array = [];
      
      public var unionList:Array = [];
      
      public var rowCount:int = 0;
      
      public function UnionListInfo()
      {
         super();
      }
      
      public static function getByJson(json0:String) : UnionListInfo
      {
         var obj0:Object = JSON2.decode(json0);
         var u0:UnionListInfo = new UnionListInfo();
         u0.inData_byObj(obj0);
         return u0;
      }
      
      public static function getSimulated(num0:int, rowCount0:int = -1) : UnionListInfo
      {
         var i0:UnionInfo = null;
         var u0:UnionListInfo = new UnionListInfo();
         for(var i:int = 0; i < num0; i++)
         {
            i0 = UnionInfo.getSimulated(i);
            u0.unionList.push(i0);
         }
         if(rowCount0 == -1)
         {
            rowCount0 = num0;
         }
         u0.rowCount = rowCount0;
         return u0;
      }
      
      public function getTopArr(sortName0:String = "") : Array
      {
         return this.unionList;
      }
      
      public function getTopFunName() : String
      {
         return "inData_byUnionInfo";
      }
      
      public function getTopTitleCnArr() : Array
      {
         return UnionInfo.LIST_CN;
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData(this,obj0,pro_arr);
         this.unionList = ClassProperty.copySaveArray(obj0["unionList"],UnionInfo);
      }
      
      public function getPageNum() : int
      {
         var num0:int = int((this.rowCount - 1) / BAR_NUM) + 1;
         if(num0 < 1)
         {
            num0 = 1;
         }
         return num0;
      }
      
      public function inPageNum(page0:int) : void
      {
         var i0:UnionInfo = null;
         ArrayMethod.sortArrByProName(this.unionList,"experience",false);
         var index0:int = 0;
         for each(i0 in this.unionList)
         {
            index0++;
            i0.rank = page0 * 10 + index0;
         }
      }
      
      public function getSimulatedJson(pageNum:int, pageSize:int) : String
      {
         var first0:int = (pageNum - 1) * pageSize;
         var last0:int = (pageNum + 0) * pageSize;
         var u0:UnionListInfo = new UnionListInfo();
         u0.inData_byObj(this);
         u0.unionList = [];
         for(var i:int = first0; i < last0; i++)
         {
            if(i >= 0 && i <= this.unionList.length - 1)
            {
               u0.unionList.push(this.unionList[i]);
            }
         }
         return JSON2.encode(u0);
      }
   }
}

