package dataAll._app.ask
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import dataAll._app.ask.define.AskPropsDefine;
   import dataAll._app.vip.define.VipLevelDefine;
   
   public class AskPropsData
   {
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var def:AskPropsDefine;
      
      public function AskPropsData()
      {
         super();
         this.useNum = 0;
      }
      
      public function get useNum() : Number
      {
         return this.CF.getAttribute("useNum");
      }
      
      public function set useNum(v0:Number) : void
      {
         this.CF.setAttribute("useNum",v0);
      }
      
      public function getCanUseNum() : int
      {
         return this.getAllNum() - this.useNum;
      }
      
      public function getAllNum() : int
      {
         var vipD0:VipLevelDefine = Gaming.PG.da.vip.def;
         return this.def.getPropsNum(vipD0);
      }
   }
}

