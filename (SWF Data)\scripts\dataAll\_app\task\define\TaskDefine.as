package dataAll._app.task.define
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.Sounto64;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.task.TaskState;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.level.define.event.LevelEventConditionDefine;
   import dataAll.things.define.ThingsDefine;
   
   public class TaskDefine
   {
      public static var pro_arr:Array = [];
      
      public static var stateColor:Object = {
         "ing":"#00FFFF",
         "complete":"#00FF00",
         "fail":"#FF4343"
      };
      
      public var father:String = "";
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var role:String = "";
      
      public var uiShowTime:Number = 8;
      
      public var shortText:String = "";
      
      public var conditionText:String = "";
      
      public var uiConditionText:String = "";
      
      public var description:String = "";
      
      public var remarks:String = "";
      
      public var dropStr:String = "";
      
      public var maxLvLimitB:Boolean = true;
      
      private var _lv:String = "";
      
      private var _unlockLv:String = "";
      
      public var unlockText:String = "";
      
      public var tempLvB:Boolean = false;
      
      public var levelIsTaskLvB:Boolean = false;
      
      public var moreKillEnemyNumIsMe:Boolean = false;
      
      public var superAddNumB:Boolean = false;
      
      public var diffMul:Number = 0;
      
      public var diff:Number = 0;
      
      public var enemyDpsMul:Number = 1;
      
      public var enemyLifeMul:Number = 1;
      
      private var _completeLimitNum:String = "";
      
      public var uiFleshConditionArr:Array = [];
      
      public var condition:TaskConditionDefine = new TaskConditionDefine();
      
      public var limit:TaskLimitDefine = new TaskLimitDefine();
      
      public var levelId:String = "";
      
      public var worldMapId:String = "";
      
      public var drop:TaskDropDefine = new TaskDropDefine();
      
      public var gift:GiftAddDefineGroup = new GiftAddDefineGroup();
      
      public var openD:TaskOpenDefine = new TaskOpenDefine();
      
      public var growthArr:Array = [];
      
      public var haveGrowthB:Boolean = false;
      
      public var worldMapType:String = "";
      
      public var fixedLevelUrl:String = "";
      
      public var noEnemyWhenCompleteB:Boolean = false;
      
      public function TaskDefine()
      {
         super();
         this.completeLimitNum = 1;
      }
      
      public function inData_byXML(xml0:XML, father0:String, inDataGrowthB:Boolean = true) : void
      {
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         ClassProperty.inData_byXML(this,xml0,pro_arr);
         if(this.uiConditionText == "")
         {
            this.uiConditionText = this.conditionText;
         }
         if(int(xml0.@uiShowTime) > 0)
         {
            this.uiShowTime = int(xml0.@uiShowTime);
         }
         this.father = father0;
         this.uiFleshConditionArr = this.getConditionDefineByXml(xml0.uiFleshCondition[0]);
         this.gift.inAllData_byXML(xml0.gift,false);
         this.condition.inData_byXML(xml0.condition[0]);
         this.limit.inData_byXML(xml0.limit[0]);
         this.drop.inData_byXML(xml0.drop[0]);
         this.openD.inData_byXML(xml0.openD[0]);
         if(inDataGrowthB)
         {
            this.inGrowthXml(xml0.growth[0]);
            this.haveGrowthB = this.growthArr.length > 0;
         }
         var giftMul0:Number = TaskType.getGiftMul(this.father);
         if(giftMul0 > 1)
         {
            this.gift.addNumMul(giftMul0);
         }
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function clone() : TaskDefine
      {
         var d0:TaskDefine = new TaskDefine();
         d0.inData_byObj(this);
         return d0;
      }
      
      private function inGrowthXml(xml0:XML) : void
      {
         var n:* = undefined;
         var d0:TaskDefine = null;
         if(!xml0)
         {
            return;
         }
         var xmlList0:XMLList = xml0.task;
         for(n in xmlList0)
         {
            d0 = new TaskDefine();
            d0.inData_byXML(xmlList0[n],this.father,false);
            this.growthArr[n] = d0;
         }
      }
      
      public function getGrowthNum() : int
      {
         return this.growthArr.length;
      }
      
      private function getConditionDefineByXml(xml0:XML) : Array
      {
         var n:* = undefined;
         var n_xml0:XML = null;
         var d0:LevelEventConditionDefine = null;
         if(!xml0)
         {
            return [];
         }
         var c_xmlList0:XMLList = xml0.one;
         var arr0:Array = [];
         for(n in c_xmlList0)
         {
            n_xml0 = c_xmlList0[n];
            d0 = new LevelEventConditionDefine();
            d0.inData_byXML(n_xml0);
            arr0.push(d0);
         }
         return arr0;
      }
      
      public function getAlertTitleText() : String
      {
         var fd0:TaskFatherDefine = Gaming.defineGroup.task.getFatherDefine(this.father);
         var lvStr0:String = "";
         if(this.lv > 0)
         {
            lvStr0 = this.lv + "级";
         }
         return ComMethod.color("<b>" + this.cnName + "</b>","#00FF00") + ComMethod.color("[" + fd0.cnName + lvStr0 + "]","#00FFFF");
      }
      
      public function getFatherCn() : String
      {
         var fd0:TaskFatherDefine = Gaming.defineGroup.task.getFatherDefine(this.father);
         return fd0.cnName + "任务";
      }
      
      public function getFatherCnShort() : String
      {
         var fd0:TaskFatherDefine = Gaming.defineGroup.task.getFatherDefine(this.father);
         return fd0.cnName;
      }
      
      public function getTaskBoxText(state0:String) : String
      {
         var fd0:TaskFatherDefine = null;
         var str0:String = "";
         if(this.father != "level")
         {
            fd0 = Gaming.defineGroup.task.getFatherDefine(this.father);
            str0 += "<b>" + ComMethod.color(this.cnName,state0 != TaskState.complete ? "#FFFF00" : stateColor[state0]) + "</b>[" + fd0.cnName + "]\n";
         }
         return str0 + this.getShortText(state0);
      }
      
      public function getShortText(state0:String) : String
      {
         return ComMethod.color(this.shortText,stateColor[state0]);
      }
      
      public function getWorldMapDefine() : WorldMapDefine
      {
         return Gaming.defineGroup.worldMap.getDefine(this.worldMapId);
      }
      
      public function getUnlockText() : String
      {
         if(this.unlockLv > 0)
         {
            return "解锁等级：" + ComMethod.color(this.unlockLv + "级","#00FF00");
         }
         if(this.name == "BaiLu_ZangShi")
         {
            return "通关" + ComMethod.color("南唐城","#00FF00") + "后解锁";
         }
         return "";
      }
      
      public function isMainB() : Boolean
      {
         return this.father == TaskType.MAIN;
      }
      
      public function isMemoryB() : Boolean
      {
         return this.father == TaskType.MEMORY;
      }
      
      public function isDayClearB() : Boolean
      {
         if(TaskType.dayClearTypeArr.indexOf(this.father) >= 0)
         {
            return true;
         }
         return false;
      }
      
      public function isAutoUnlockNextB() : Boolean
      {
         return TaskType.autoUnlockArr.indexOf(this.father) >= 0;
      }
      
      public function isWeekClearB() : Boolean
      {
         if(TaskType.weekClearTypeArr.indexOf(this.father) >= 0)
         {
            return true;
         }
         return false;
      }
      
      public function mustWorldMapUnlockB() : Boolean
      {
         var d0:WorldMapDefine = null;
         if(this.father == TaskType.DAY)
         {
            if(this.worldMapType == "")
            {
               return false;
            }
         }
         else
         {
            d0 = Gaming.defineGroup.worldMap.getDefine(this.worldMapId);
            if(Boolean(d0))
            {
               if(d0.isSpaceB())
               {
                  return false;
               }
               if(d0.father == "task")
               {
                  return false;
               }
            }
         }
         return true;
      }
      
      public function set lv(v0:Number) : void
      {
         this._lv = Sounto64.encode(String(v0));
      }
      
      public function get lv() : Number
      {
         return Number(Sounto64.decode(this._lv));
      }
      
      public function set unlockLv(v0:Number) : void
      {
         this._unlockLv = Sounto64.encode(String(v0));
      }
      
      public function get unlockLv() : Number
      {
         return Number(Sounto64.decode(this._unlockLv));
      }
      
      public function set completeLimitNum(v0:Number) : void
      {
         this._completeLimitNum = Sounto64.encode(String(v0));
      }
      
      public function get completeLimitNum() : Number
      {
         return Number(Sounto64.decode(this._completeLimitNum));
      }
      
      public function getLastGiftThingsDefine() : ThingsDefine
      {
         var giftD0:GiftAddDefine = this.gift.arr[this.gift.arr.length - 1];
         if(Boolean(giftD0))
         {
            return Gaming.defineGroup.things.getDefine(giftD0.name);
         }
         return null;
      }
      
      public function rolePan(role0:String) : Boolean
      {
         var noRole0:String = null;
         if(this.role == "")
         {
            return true;
         }
         if(this.role.indexOf("no") == 0)
         {
            noRole0 = this.role.replace("no","");
            if(role0 == noRole0)
            {
               return false;
            }
            return true;
         }
         if(this.role == role0)
         {
            return true;
         }
         if(this.role.indexOf(role0) >= 0)
         {
            return true;
         }
         return false;
      }
      
      public function isUnlockMapB() : Boolean
      {
         var d0:WorldMapDefine = null;
         if(this.worldMapId != "")
         {
            if(TaskType.autoUnlockMapArr.indexOf(this.father) >= 0)
            {
               return true;
            }
            d0 = Gaming.defineGroup.worldMap.getDefine(this.worldMapId);
            if(Boolean(d0))
            {
               if(d0.isShowMapB())
               {
                  return false;
               }
            }
            return true;
         }
         return false;
      }
   }
}

