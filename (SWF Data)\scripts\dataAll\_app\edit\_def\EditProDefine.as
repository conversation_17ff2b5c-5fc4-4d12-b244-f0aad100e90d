package dataAll._app.edit._def
{
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.NumberMethod;
   import dataAll._app.edit.tor.IO_TorEditDefine;
   import dataAll._base.NormalDefine;
   import dataAll.pro.ProType;
   
   public class EditProDefine extends NormalDefine implements IO_TorEditDefine
   {
      public static var pro_arr:Array = null;
      
      public static var fatherPro_arr:Array = ["proPos","changeB","gra","ca"];
      
      public var gather:String = "";
      
      public var defPro:String = "";
      
      public var defValueFun:String = "";
      
      public var type:String = "string";
      
      public var init:String = "";
      
      private var trueInit:* = null;
      
      public var proPos:String = "";
      
      public var method:String = "";
      
      public var min:Number = 0;
      
      public var max:Number = 99999999;
      
      public var fixed:int = 0;
      
      public var scoreMul:Number = 0;
      
      public var color:String = "";
      
      public var unit:String = "";
      
      public var tip:String = "";
      
      public var changeB:Boolean = true;
      
      public var gra:String = "";
      
      public var ca:String = "";
      
      public function EditProDefine()
      {
         super();
      }
      
      public function inFatherXML(xml0:XML) : void
      {
         ClassProperty.inData_byXMLAt(this,xml0,fatherPro_arr);
      }
      
      override public function inData_byXML(xml0:XML, father0:String = "") : void
      {
         super.inData_byXML(xml0,father0);
         if(this.gather != EditProGather.boss)
         {
            if(this.proPos == "no")
            {
               this.proPos = "";
            }
            this.defPro = name;
            if(this.proPos != "")
            {
               name = EditProPos.getUrlName(name,this.proPos);
            }
         }
         if(this.init != "")
         {
            this.trueInit = ProType.stringToType(this.init,this.type);
         }
         else
         {
            this.trueInit = ProType.getInit(this.type);
         }
      }
      
      override protected function getBaseClassProArr() : Array
      {
         return pro_arr;
      }
      
      public function isArray() : Boolean
      {
         return this.type == ProType.ARRAY;
      }
      
      public function isEditArray() : Boolean
      {
         return this.type == ProType.ARRAY;
      }
      
      public function getLinkLabel() : String
      {
         if(this.changeB)
         {
            return name;
         }
         return "";
      }
      
      public function getChildType() : String
      {
         return this.method;
      }
      
      public function isNormalGra() : Boolean
      {
         return this.gra == EditProGra.n;
      }
      
      public function getTorShow(hideHighB0:Boolean, showCa0:String = "", pn0:String = "") : Boolean
      {
         var showB0:Boolean = true;
         if(hideHighB0 && this.isNormalGra() == false)
         {
            showB0 = false;
         }
         if(this.ca != "" && this.ca != showCa0)
         {
            showB0 = false;
         }
         if(pn0 == "" && name == "pn")
         {
            showB0 = false;
         }
         return showB0;
      }
      
      public function getObjValue(obj0:Object) : *
      {
         var m0:Object = EditProPos.getPosObj(this.proPos,obj0);
         if(Boolean(m0))
         {
            if(m0.hasOwnProperty(this.defPro))
            {
               return m0[this.defPro];
            }
         }
         return null;
      }
      
      public function setObjValue(obj0:Object, v0:*) : Boolean
      {
         var m0:Object = EditProPos.getPosObj(this.proPos,obj0);
         if(Boolean(m0))
         {
            m0[this.defPro] = v0;
            return true;
         }
         return false;
      }
      
      public function setObjValueAndAdd(obj0:Object, v0:*) : Boolean
      {
         var m0:Object = EditProPos.getPosObj(this.proPos,obj0,true);
         if(Boolean(m0))
         {
            m0[this.defPro] = v0;
            return true;
         }
         return false;
      }
      
      public function getNumGap() : Number
      {
         if(this.fixed == 0)
         {
            return 1;
         }
         return 0.1;
      }
      
      public function getInit() : *
      {
         return this.trueInit;
      }
      
      public function getBossProPos() : String
      {
         if(this.proPos == "")
         {
            return father;
         }
         return this.proPos;
      }
      
      public function getUIText(v0:*) : String
      {
         if(this.type == ProType.BOOLEAN)
         {
            v0 = ProType.booleanToUI(v0);
         }
         return cnName + " " + String(v0) + this.unit;
      }
      
      public function getTorTip() : String
      {
         return this.tip;
      }
      
      public function fixedNumber(v0:Number) : Number
      {
         v0 = NumberMethod.toFixed(v0,this.fixed);
         if(v0 > this.max)
         {
            v0 = this.max;
         }
         if(v0 < this.min)
         {
            v0 = this.min;
         }
         return v0;
      }
      
      public function getParent() : IO_TorEditDefine
      {
         return null;
      }
   }
}

