package dataAll.items
{
   import dataAll._player.more.NormalPlayerData;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.items.save.ItemsSave;
   
   public interface IO_ItemsData
   {
      function setFatherData(param1:ItemsDataGroup) : void;
      
      function getSave() : ItemsSave;
      
      function getColor() : String;
      
      function getIconImgUrl(param1:int = 0, param2:int = 0) : String;
      
      function getSellPrice() : Number;
      
      function getWearLevel() : int;
      
      function getTypeId() : String;
      
      function getChildTypeId() : String;
      
      function getDataType() : String;
      
      function setPlaceType(param1:String) : void;
      
      function getPlaceType() : String;
      
      function getCnType() : String;
      
      function getTempSortId() : String;
      
      function setTempSortId(param1:String) : void;
      
      function toOneSortId(param1:String) : void;
      
      function setPlayerData(param1:NormalPlayerData) : void;
      
      function getPlayerData() : NormalPlayerData;
      
      function isArenaGiftB() : Boolean;
      
      function getAllUpgradeConverStoneNum() : int;
      
      function setHidingB(param1:Boolean) : void;
      
      function getHidingB() : Boolean;
      
      function set isChosen(param1:Boolean) : void;
      
      function get isChosen() : Boolean;
      
      function set newB(param1:Boolean) : void;
      
      function get newB() : Boolean;
      
      function canStrengthenB() : Boolean;
      
      function canStrengthenMoveB() : Boolean;
      
      function isCanNumSwapB() : Boolean;
      
      function isCanOverlayB() : Boolean;
      
      function getNowNum() : int;
      
      function setNowNum(param1:int) : void;
      
      function addNowNum(param1:int, param2:IO_ItemsData = null) : void;
      
      function normalClone() : IO_ItemsData;
      
      function canRefiningB() : Boolean;
      
      function canResolveB() : Boolean;
      
      function getResolveGift() : GiftAddDefineGroup;
      
      function dealBtnListCn(param1:String) : String;
   }
}

