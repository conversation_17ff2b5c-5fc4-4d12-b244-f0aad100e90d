package dataAll._player.more
{
   import UI.bag.ItemsGripMoveCtrl;
   import dataAll._player.PlayerData;
   import dataAll.equip.EquipPropertyData;
   import dataAll.equip.add.IO_EquipAddGetter;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipPro;
   import dataAll.equip.define.EquipType;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import gameAll.body.IO_NormalBody;
   import gameAll.hero.HeroBody;
   
   public class MoreWay implements IO_EquipAddGetter
   {
      public var PD:PlayerData;
      
      public function MoreWay()
      {
         super();
      }
      
      public function isFightB(da0:MoreData) : Boolean
      {
         if(da0 == this.PD.heroData)
         {
            return true;
         }
         if(this.PD.more.dataArr.indexOf(da0) >= 0)
         {
            return true;
         }
         return false;
      }
      
      public function getAll_maxCountNum(proName0:String) : Number
      {
         var pd0:NormalPlayerData = null;
         var hero0:HeroBody = null;
         var num2:Number = NaN;
         var num0:Number = 0;
         var heroArr0:Array = this.PD.getPlayerDataArr();
         for each(pd0 in heroArr0)
         {
            hero0 = pd0.hero;
            if(hero0 is HeroBody)
            {
               num2 = Number(hero0.dat.countD[proName0]);
               if(num0 < num2)
               {
                  num0 = num2;
               }
            }
         }
         return num0;
      }
      
      public function getAll_bestArmsColor() : String
      {
         var pd0:NormalPlayerData = null;
         var hero0:HeroBody = null;
         var color2:String = null;
         var color0:String = "";
         var heroArr0:Array = this.PD.getPlayerDataArr();
         for each(pd0 in heroArr0)
         {
            hero0 = pd0.hero;
            if(hero0 is HeroBody)
            {
               color2 = hero0.dat.countD.bestArmsColor;
               if(EquipColor.firstMax(color2,color0))
               {
                  color0 = color2;
               }
            }
         }
         return color0;
      }
      
      public function getAll_maxArmsPartsNum() : int
      {
         return this.getAll_maxCountNum("maxArmsPartsNum");
      }
      
      public function getAll_maxWearEquipNum() : int
      {
         return this.getAll_maxCountNum("maxWearEquipNum");
      }
      
      public function getAll_levelKillEnemyNum() : int
      {
         var hero0:HeroBody = null;
         var num0:Number = 0;
         var heroArr0:Array = this.PD.getHeroArr();
         for each(hero0 in heroArr0)
         {
            if(hero0 is HeroBody)
            {
               num0 += hero0.dat.countD.killEnemyNum;
            }
         }
         return num0;
      }
      
      public function getAllDps() : Number
      {
         var pd0:NormalPlayerData = null;
         var dps0:Number = 0;
         var heroArr0:Array = this.PD.getPlayerDataArr();
         for each(pd0 in heroArr0)
         {
            dps0 += pd0.getDps();
         }
         return dps0;
      }
      
      public function getAll_countNum(pro0:String) : Number
      {
         var pd0:NormalPlayerData = null;
         var num0:Number = 0;
         var heroArr0:Array = this.PD.getPlayerDataArr();
         for each(pd0 in heroArr0)
         {
            num0 += pd0.getNormalSave().count[pro0];
         }
         return num0;
      }
      
      public function getMaxLife() : Number
      {
         var pd0:NormalPlayerData = null;
         var v0:Number = NaN;
         var arr0:Array = this.PD.getPlayerDataArr();
         var max0:Number = 0;
         for each(pd0 in arr0)
         {
            v0 = pd0.base.getMaxLife();
            if(v0 > max0)
            {
               max0 = v0;
            }
         }
         return max0;
      }
      
      public function getMergeProSum(name0:String, moreMul0:Number = 1, bagB0:Boolean = false) : Number
      {
         var pd0:NormalPlayerData = null;
         var me0:EquipPropertyData = null;
         var v0:Number = NaN;
         var mul0:Number = NaN;
         var arr0:Array = this.PD.getPlayerDataArr();
         var all0:Number = 0;
         for each(pd0 in arr0)
         {
            me0 = pd0.getDropMerge();
            v0 = 0;
            mul0 = 1;
            if(pd0 is PlayerData)
            {
               mul0 = 1;
            }
            else if(pd0.getRoleName() == this.PD.getCpRole())
            {
               mul0 = 1;
            }
            else
            {
               mul0 = moreMul0;
            }
            if(me0[name0] is Function)
            {
               v0 = me0[name0]() * mul0;
            }
            else
            {
               v0 = me0[name0] * mul0;
            }
            all0 += v0;
         }
         if(bagB0)
         {
            all0 += this.PD.equipBag.getEquipProSum(name0,EquipType.FASHION);
         }
         var max0:Number = EquipPro.getSumMax(name0);
         if(max0 > 0)
         {
            if(all0 > max0)
            {
               all0 = max0;
            }
         }
         return all0;
      }
      
      public function getMergeDropProSum(name0:String) : Number
      {
         return this.getMergeProSum(name0,0.5);
      }
      
      public function fleshAllEquip() : void
      {
         var pd0:NormalPlayerData = null;
         var arr0:Array = this.PD.getPlayerDataArr();
         for each(pd0 in arr0)
         {
            pd0.fleshAllByEquip();
         }
      }
      
      public function getProAddObj() : Object
      {
         var pro0:* = null;
         var v0:Number = NaN;
         var mev0:Number = NaN;
         var obj0:Object = {};
         var me0:EquipPropertyData = this.PD.getDropMerge();
         for each(pro0 in EquipPro.more50DropArr)
         {
            v0 = this.getMergeDropProSum(pro0);
            mev0 = Number(me0[pro0]);
            v0 -= mev0;
            if(v0 > 0)
            {
               obj0[pro0] = v0;
            }
         }
         for each(pro0 in EquipPro.more100DropArr)
         {
            v0 = this.getMergeProSum(pro0,1);
            mev0 = Number(me0[pro0]);
            v0 -= mev0;
            if(v0 > 0)
            {
               obj0[pro0] = v0;
            }
         }
         for each(pro0 in EquipPro.bagDropArr)
         {
            v0 = this.getMergeProSum(pro0,1,true);
            mev0 = Number(me0[pro0]);
            v0 -= mev0;
            if(v0 > 0)
            {
               obj0[pro0] = v0;
            }
         }
         return obj0;
      }
      
      public function getProAddMax(pro0:String) : Number
      {
         return 0;
      }
      
      public function getProSum(pro0:String) : Number
      {
         if(EquipPro.isMore50(pro0))
         {
            return this.getMergeDropProSum(pro0);
         }
         if(EquipPro.more100DropArr.indexOf(pro0) >= 0)
         {
            return this.getMergeProSum(pro0);
         }
         if(EquipPro.bagDropArr.indexOf(pro0) >= 0)
         {
            return this.getMergeProSum(pro0,1,true);
         }
         return -1;
      }
      
      public function get_specialPartsDropPro() : Number
      {
         return this.getProSum("specialPartsDropPro");
      }
      
      public function get_gemDropPro() : Number
      {
         return this.getProSum("gemDropPro");
      }
      
      public function get_blackEquipDropPro() : Number
      {
         return this.getProSum("blackEquipDropPro");
      }
      
      public function get_blackArmsDropPro() : Number
      {
         return this.getProSum("blackArmsDropPro");
      }
      
      public function get_rareGeneDropPro() : Number
      {
         return this.getProSum("rareGeneDropPro");
      }
      
      public function get_petBookDropPro() : Number
      {
         return this.getProSum("petBookDropPro");
      }
      
      public function get_demStroneDropNum() : Number
      {
         return this.getMergeProSum(EquipPro.demStroneDropNum,1,true);
      }
      
      public function get_demBallDropNum() : Number
      {
         return this.getMergeProSum(EquipPro.demBallDropNum,1,true);
      }
      
      public function get_madheartDropNum() : Number
      {
         return this.getMergeProSum(EquipPro.madheartDropNum,1,true);
      }
      
      public function get_sweepingNum() : Number
      {
         return this.getMergeProSum(EquipPro.sweepingNum,1,true);
      }
      
      public function get_dayLoveAdd() : Number
      {
         return this.getMergeProSum(EquipPro.dayLoveAdd,1,true);
      }
      
      public function allPartnerFight() : void
      {
         var site2:int = 0;
         var bagArr0:Array = null;
         var bagDa0:IO_ItemsData = null;
         var len0:int = this.PD.getMoreNum();
         for(var i:int = 0; i < len0; i++)
         {
            site2 = this.PD.more.spaceSiteOf();
            if(site2 == -1)
            {
               return;
            }
            bagArr0 = this.PD.moreBag.dataArr;
            if(bagArr0.length > 0)
            {
               bagDa0 = bagArr0[0];
               ItemsGripMoveCtrl.swap(this.PD.moreBag,this.PD.more,bagDa0.getSave().site,site2);
            }
         }
      }
      
      public function initPartnerNameArr(nameArr0:Array) : void
      {
         var name0:* = null;
         var daArr0:Array = null;
         var da0:MoreData = null;
         if(Boolean(nameArr0))
         {
            for each(name0 in nameArr0)
            {
               da0 = this.getDataByHeroName(name0);
               if(da0 == null)
               {
                  this.PD.addMoreByUnitName(name0);
               }
            }
            daArr0 = this.PD.more.dataArr.concat(this.PD.moreBag.dataArr);
            for each(da0 in daArr0)
            {
               if(nameArr0.indexOf(da0.name) == -1)
               {
                  da0.getFatherData().removeData(da0);
               }
            }
            Gaming.PG.setNowMoreData(this.PD.heroData);
         }
      }
      
      public function initPartnerBattleNum(v0:int) : void
      {
         this.PD.more.saveGroup.unlockTo(v0 - 1);
      }
      
      public function getDataByHeroName(name0:String) : MoreData
      {
         var da0:MoreData = null;
         var arr0:Array = Gaming.PG.da.getMoreDataArr();
         for each(da0 in arr0)
         {
            if(da0.def.name == name0)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function getDataByHeroCn(cn0:String) : MoreData
      {
         var da0:MoreData = null;
         var arr0:Array = Gaming.PG.da.getMoreDataArr();
         for each(da0 in arr0)
         {
            if(da0.def.cnName == cn0)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function getCanSummonArr() : Array
      {
         var da0:MoreData = null;
         var arr0:Array = this.PD.moreBag.dataArr;
         var arr2:Array = [];
         for each(da0 in arr0)
         {
            if(!(da0.DATA.hero is HeroBody))
            {
               arr2.push(da0);
            }
         }
         return arr2;
      }
      
      public function getMoreLiveHeroArr(haveMainB0:Boolean = true, noInVehicleB0:Boolean = false) : Array
      {
         var da0:MoreData = null;
         var hero0:HeroBody = null;
         var bb1:Boolean = false;
         var bb2:Boolean = false;
         var arr0:Array = Gaming.PG.da.getMoreDataArr();
         var arr2:Array = [];
         for each(da0 in arr0)
         {
            hero0 = da0.DATA.hero;
            if(hero0 is HeroBody)
            {
               bb1 = haveMainB0 || da0.DATA != this.PD;
               bb2 = !noInVehicleB0 || !hero0.vehicleCtrl.getBodyHaveCd();
               if(bb1 && bb2 && hero0.getDie() == 0)
               {
                  arr2.push(da0.DATA.hero);
               }
            }
         }
         return arr2;
      }
      
      public function getMoreShowNum() : int
      {
         var da0:MoreData = null;
         var hero0:HeroBody = null;
         var num0:int = 0;
         var arr0:Array = Gaming.PG.da.getMoreDataArr();
         for each(da0 in arr0)
         {
            hero0 = da0.DATA.hero;
            if(hero0 is HeroBody)
            {
               num0++;
            }
         }
         num0--;
         return num0;
      }
      
      public function addLifeLiveMore(per0:Number) : void
      {
         var b0:IO_NormalBody = null;
         var arr0:Array = this.getMoreLiveHeroArr();
         for each(b0 in arr0)
         {
            b0.getData().addLife(per0,true);
         }
      }
      
      public function getLoveGiftNumDay() : int
      {
         var da0:MoreData = null;
         var pd0:MorePlayerData = null;
         var num0:int = 0;
         var arr0:Array = Gaming.PG.da.getMoreDataArr();
         for each(da0 in arr0)
         {
            pd0 = da0.DATA as MorePlayerData;
            if(Boolean(pd0))
            {
               num0 += pd0.love.save.todayGivingNum;
            }
         }
         return num0;
      }
      
      public function getLoveGiftAll() : Number
      {
         var da0:MoreData = null;
         var pd0:MorePlayerData = null;
         var num0:Number = 0;
         var arr0:Array = Gaming.PG.da.getMoreDataArr();
         for each(da0 in arr0)
         {
            pd0 = da0.DATA as MorePlayerData;
            if(Boolean(pd0))
            {
               num0 += pd0.love.save.allGivingNum;
            }
         }
         return num0;
      }
      
      public function getMaxLoveValue() : Number
      {
         var da0:MoreData = null;
         var pd0:MorePlayerData = null;
         var v0:Number = NaN;
         var max0:Number = 0;
         var arr0:Array = Gaming.PG.da.getMoreDataArr();
         for each(da0 in arr0)
         {
            pd0 = da0.DATA as MorePlayerData;
            if(Boolean(pd0))
            {
               v0 = pd0.love.getValue();
               if(v0 > max0)
               {
                  max0 = v0;
               }
            }
         }
         return max0;
      }
      
      public function switchDoubleModel() : Boolean
      {
         var doubleB0:Boolean = this.PD.isDoubleB();
         if(doubleB0)
         {
            this.gotoSingle();
         }
         else
         {
            this.gotoDouble();
         }
         return !doubleB0;
      }
      
      public function gotoDouble() : void
      {
         var secRole0:String = null;
         var moreNum0:int = 0;
         var da0:MoreData = null;
         var doubleB0:Boolean = this.PD.isDoubleB();
         if(!doubleB0)
         {
            secRole0 = this.PD.getDoubleInitSecRole();
            moreNum0 = int(this.PD.more.dataArr.length);
            if(moreNum0 <= 1)
            {
               this.fightOneByName(secRole0);
            }
            da0 = this.PD.more.getDataBySaveName(secRole0) as MoreData;
            if(!(da0 is MoreData))
            {
               da0 = this.PD.more.dataArr[1];
            }
            this.setMoreDataPlayerCtrl(da0,true);
         }
      }
      
      public function gotoSingle() : void
      {
         var doubleB0:Boolean = this.PD.isDoubleB();
         if(doubleB0)
         {
            this.PD.more.clearAllPlayerCtrl();
         }
      }
      
      public function setMoreDataPlayerCtrl(da0:MoreData, bb0:Boolean) : void
      {
         if(bb0)
         {
            this.PD.more.clearAllPlayerCtrl();
            this.PD.moreBag.clearAllPlayerCtrl();
         }
         da0.DATA.setPlayerCtrlB(bb0);
      }
      
      private function fightOneByName(name0:String) : void
      {
         var da0:MoreData = null;
         var f0:int = this.PD.more.spaceSiteOf();
         if(f0 >= 0)
         {
            da0 = this.PD.moreBag.getDataBySaveName(name0) as MoreData;
            if(da0 is MoreData)
            {
               ItemsDataGroup.swapTo(this.PD.more,this.PD.moreBag,f0,da0.save.site);
            }
         }
      }
   }
}

