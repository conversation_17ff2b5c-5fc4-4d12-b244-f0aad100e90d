package dataAll._app.worldMap
{
   import com.sounto.utils.ArrayMethod;
   import dataAll._app.head.HeadConditionCheckEquip;
   import dataAll._app.task.TaskData;
   import dataAll._app.task.define.TaskType;
   import dataAll._app.union.battle.UnionBattleMapDefine;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._app.worldMap.save.WorldMapSave;
   import dataAll._app.worldMap.save.WorldMapSaveGroup;
   import dataAll._player.PlayerData;
   import dataAll._player.time.TimeData;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.level.DemonDataCtrl;
   import dataAll.level.LevelDiffGetting;
   import dataAll.level.define.LevelDefine;
   import dataAll.level.define.unit.OneUnitOrderDefine;
   import dataAll.level.modeDiy.ModeDiyDefine;
   import dataAll.test.ZuoBiMax;
   import gameAll.level.endless.EndlessModelData;
   
   public class WorldMapDataGroup
   {
      public var saveGroup:WorldMapSaveGroup = null;
      
      public var playerData:PlayerData;
      
      private var noSayArr:Array = [];
      
      public function WorldMapDataGroup()
      {
         super();
      }
      
      public static function getEndlessDirectGiftLv() : Number
      {
         return 60;
      }
      
      public function overLevel(name0:String, diff0:int) : void
      {
         if(diff0 == 3 && HeadConditionCheckEquip.zongziArr.indexOf(name0) >= 0)
         {
            this.saveGroup.winMaxDiffMapArr.push(name0);
         }
      }
      
      public function uiFlesh() : void
      {
         this.winOneByTaskOver("XingGu","beatMadboss");
      }
      
      private function winOneByTaskOver(mapName0:String, taskName0:String) : WorldMapSave
      {
         var s0:WorldMapSave = null;
         var bb0:Boolean = this.playerData.task.getTaskIsGetGiftB(taskName0);
         if(bb0)
         {
            s0 = this.saveGroup.unlockOne(mapName0);
            if(Boolean(s0))
            {
               if(!s0.winB)
               {
                  s0.win(6,MapMode.REGULAR);
               }
            }
         }
         return s0;
      }
      
      public function inData_bySaveGroup(sg0:WorldMapSaveGroup) : void
      {
         this.saveGroup = sg0;
      }
      
      public function setReadSaveTime(readTime0:TimeData) : void
      {
         DemonDataCtrl.setReadStringDate(readTime0.getReadTimeDate());
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.randomChallengeLevel();
         this.saveGroup.newDayCtrl(timeStr0);
      }
      
      public function newWeek(timeStr0:String) : void
      {
         this.saveGroup.newWeek(timeStr0);
      }
      
      public function tryRandomChallengeLevel() : void
      {
         var heroLv0:int = this.playerData.level;
         if(heroLv0 >= 35)
         {
            if(!this.saveGroup.haveChallengeLevelB())
            {
               this.saveGroup.randomChallengeLevel(this.playerData.level);
            }
         }
      }
      
      private function randomChallengeLevel() : void
      {
         var heroLv0:int = this.playerData.level;
         if(heroLv0 >= 35)
         {
            this.saveGroup.randomChallengeLevel(this.playerData.level);
         }
      }
      
      public function unlockAll() : void
      {
         if(Boolean(this.saveGroup))
         {
            this.saveGroup.unlockAll();
         }
      }
      
      private function taskPanLevel(levelName0:String) : Boolean
      {
         var levelD0:LevelDefine = Gaming.defineGroup.level.getDefine(levelName0);
         if(levelD0 is LevelDefine)
         {
            if(levelD0.fixed.target == "")
            {
               return true;
            }
         }
         return false;
      }
      
      public function getWinArrExcludeTaskByType(taskType0:String, ingB0:Boolean = false, excludeMapArr0:Array = null) : Array
      {
         var s0:WorldMapSave = null;
         var d0:WorldMapDefine = null;
         var levelName0:String = null;
         var s2:WorldMapSave = null;
         var da0:TaskData = null;
         var n_arr0:Array = [];
         var arr0:Array = this.saveGroup.getWinArr();
         var arr_len0:int = int(arr0.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            s0 = arr0[i];
            d0 = s0.getDefine();
            if(d0 != null)
            {
               if(!d0.noTaskB)
               {
                  if(Boolean(excludeMapArr0))
                  {
                     if(excludeMapArr0.indexOf(d0.name) >= 0)
                     {
                        continue;
                     }
                  }
                  if(d0.lv <= 200)
                  {
                     levelName0 = s0.getLevelName();
                     if(this.taskPanLevel(levelName0))
                     {
                        n_arr0.push(s0);
                     }
                  }
               }
            }
         }
         var n_arr2:Array = [];
         var arr_len2:int = int(n_arr0.length);
         for(var j:int = 0; j < arr_len2; j++)
         {
            s2 = n_arr0[j];
            da0 = this.playerData.task.getTaskDataByWorldMap(s2.name,taskType0,ingB0);
            if(!(da0 is TaskData))
            {
               n_arr2.push(s2);
            }
         }
         if(n_arr2.length == 0)
         {
            n_arr2 = n_arr0;
         }
         return n_arr2;
      }
      
      public function test33() : void
      {
         var arr0:Array = null;
         var s0:WorldMapSave = null;
         for(var i:int = 0; i < 1; i++)
         {
            arr0 = this.getWinArrExcludeTaskByType(TaskType.DAY);
            for each(s0 in arr0)
            {
               if(s0.name == "MainUpland")
               {
                  trace("找到MainUpland===================");
               }
            }
         }
      }
      
      public function getRandomMapExcludeTaskByType(mapNameArr0:Array, taskType0:String) : WorldMapSave
      {
         var map0:* = null;
         var s0:WorldMapSave = null;
         var levelName0:String = null;
         var da0:TaskData = null;
         var newArr0:Array = [];
         for each(map0 in mapNameArr0)
         {
            s0 = this.saveGroup.getSave(map0);
            if(Boolean(s0))
            {
               levelName0 = s0.getLevelName();
               if(this.taskPanLevel(levelName0))
               {
                  da0 = this.playerData.task.getTaskDataByWorldMap(map0,taskType0);
                  if(!(da0 is TaskData))
                  {
                     newArr0.push(s0);
                  }
               }
            }
         }
         if(newArr0.length == 0)
         {
            return null;
         }
         return ArrayMethod.getRandomOne(newArr0);
      }
      
      public function getLevelDefine(worldMapId:String) : LevelDefine
      {
         var s0:WorldMapSave = this.saveGroup.getSave(worldMapId);
         if(!(s0 is WorldMapSave))
         {
            return null;
         }
         var task_da0:TaskData = this.playerData.task.getIngTaskDataByWorldMap(worldMapId);
         if(task_da0 is TaskData)
         {
            return task_da0.getLevelDefine();
         }
         return Gaming.defineGroup.level.getDefine(s0.getLevelName());
      }
      
      public function getDataByName(worldMapId:String) : WorldMapData
      {
         var mapDefine0:WorldMapDefine = null;
         var battleMapDefine0:UnionBattleMapDefine = null;
         var xx0:int = 0;
         var da0:WorldMapData = new WorldMapData();
         da0.id = worldMapId;
         da0.worldMapDataGroup = this;
         var unionAttackMapName0:String = this.playerData.union.getAttackOrTrainMapName();
         var task_da0:TaskData = this.playerData.task.getIngTaskDataByWorldMap(worldMapId);
         var s0:WorldMapSave = this.saveGroup.getSave(worldMapId);
         if(s0 is WorldMapSave)
         {
            mapDefine0 = s0.getDefine();
            if(unionAttackMapName0 == worldMapId)
            {
               battleMapDefine0 = Gaming.defineGroup.union.battle.getDefine(unionAttackMapName0);
               da0.levelDef = Gaming.defineGroup.level.getDefine(battleMapDefine0.levelName);
               da0.battleDef = Gaming.defineGroup.union.battle.getDefine(unionAttackMapName0);
            }
            else if(task_da0 is TaskData)
            {
               da0.levelDef = task_da0.getLevelDefine();
               da0.taskData = task_da0;
            }
            else
            {
               da0.levelDef = Gaming.defineGroup.level.getDefine(s0.getLevelName());
            }
            da0.def = mapDefine0;
         }
         else
         {
            if(task_da0 is TaskData)
            {
               da0.levelDef = task_da0.getLevelDefine();
               da0.taskData = task_da0;
            }
            da0.def = Gaming.defineGroup.worldMap.getDefine(worldMapId);
         }
         if(da0.levelDef == null)
         {
            xx0 = 0;
         }
         da0.setSave(s0);
         return da0;
      }
      
      public function getArenaRandomWorldMap() : WorldMapData
      {
         var index0:int = 0;
         var name0:String = null;
         var da0:WorldMapData = null;
         var arr0:Array = Gaming.defineGroup.worldMap.nameArr.concat([]);
         var len0:int = int(arr0.length);
         for(var i:int = 0; i < len0; i++)
         {
            index0 = Math.random() * arr0.length;
            name0 = arr0[index0];
            da0 = this.getDataByName(name0);
            if(!(da0.taskData || !da0.getSaveNull() || !da0.def.isShowMapB()))
            {
               return da0;
            }
            arr0.splice(index0,1);
         }
         return null;
      }
      
      private function getDataArrByEnemyName(name0:String, minLv0:int) : Array
      {
         var s0:WorldMapSave = null;
         var da0:WorldMapData = null;
         var level_d0:LevelDefine = null;
         var unit_d0:OneUnitOrderDefine = null;
         var enemy_lv0:int = 0;
         var dataArr0:Array = [];
         var arr0:Array = this.saveGroup.getWinArr();
         for each(s0 in arr0)
         {
            da0 = this.getDataByName(s0.name);
            level_d0 = da0.levelDef;
            if(level_d0.name.indexOf("PrisonDeep") == -1)
            {
               unit_d0 = level_d0.unitG.getOneByName(name0);
               if(unit_d0 is OneUnitOrderDefine)
               {
                  enemy_lv0 = da0.getEnemyLv();
                  if(unit_d0.camp == "enemy" && enemy_lv0 >= minLv0)
                  {
                     dataArr0.push(da0);
                  }
               }
            }
         }
         return dataArr0;
      }
      
      public function getSortDataArrByEnemyName(name0:String, minLv0:int) : Array
      {
         var dataArr0:Array = this.getDataArrByEnemyName(name0,minLv0);
         dataArr0.sort(this.getSortDataArrByEnemyName_sortFun);
         return dataArr0;
      }
      
      private function getSortDataArrByEnemyName_sortFun(da1:WorldMapData, da2:WorldMapData) : int
      {
         if(da1.tempLevel < da2.tempLevel)
         {
            return 1;
         }
         if(da1.tempLevel > da2.tempLevel)
         {
            return -1;
         }
         return 0;
      }
      
      public function canEndlessGiftB() : Boolean
      {
         return this.getEndlessCanNum() >= 0;
      }
      
      public function canEndlessDropB() : Boolean
      {
         return this.canEndlessGiftB();
      }
      
      public function getEndlessMax() : Number
      {
         if(this.playerData.time.weekend)
         {
            return 2;
         }
         return 1;
      }
      
      public function getEndlessCanNum() : Number
      {
         return this.getEndlessMax() - this.saveGroup.todayEndlessNum;
      }
      
      public function getEndlessCanNumUI() : Number
      {
         var num0:Number = this.getEndlessCanNum();
         if(num0 < 0)
         {
            num0 = 0;
         }
         return num0;
      }
      
      public function getEndlessDirectTip(giftB0:Boolean) : String
      {
         var g0:GiftAddDefineGroup = null;
         var str0:String = "<purple 地图/><blue 实验室顶层/><purple 突破/><b><blue " + getEndlessDirectGiftLv() + "/></b><purple 层后(当前" + this.getEndlessDirectGiftNow() + "层)，每天都可以直接领取无尽奖励。/>";
         var num0:int = this.getEndlessCanNum();
         if(giftB0)
         {
            if(num0 > 0)
            {
               g0 = EndlessModelData.getAllGift();
               g0.addNumMul(num0);
               str0 += "\n当前奖励如下：\n";
               str0 += g0.getDescription(1);
            }
            else
            {
               str0 += "<b><green 当前奖励已领完。/></b>";
            }
         }
         return str0;
      }
      
      public function endlessDirectGiftB() : Boolean
      {
         return this.getEndlessDirectGiftNow() >= getEndlessDirectGiftLv();
      }
      
      public function getEndlessDirectGiftNow() : Number
      {
         var lv0:int = 0;
         var s0:WorldMapSave = this.saveGroup.getSave("Hospital5");
         if(Boolean(s0))
         {
            lv0 = s0.maxEndlessGrade;
         }
         return lv0;
      }
      
      public function useEndlessNum(num0:int = 1) : void
      {
         this.saveGroup.todayEndlessNum += num0;
      }
      
      public function getMaxEndlessScore() : int
      {
         var s0:WorldMapSave = null;
         var now0:int = 0;
         var max0:int = 0;
         for each(s0 in this.saveGroup.obj)
         {
            now0 = s0.maxEndlessScore;
            if(now0 > max0 && now0 < ZuoBiMax.endlessScore)
            {
               max0 = now0;
            }
         }
         return max0;
      }
      
      public function inEndlessScore(worldMapId0:String, score0:Number, grade0:int) : void
      {
         var s0:WorldMapSave = this.saveGroup.getSave(worldMapId0);
         if(Boolean(s0))
         {
            s0.inEndlessScore(score0,grade0);
         }
      }
      
      public function getDemonSaveArr() : Array
      {
         var d0:WorldMapDefine = null;
         var s0:WorldMapSave = null;
         var defArr0:Array = Gaming.defineGroup.worldMap.getShowMapArr();
         var sarr0:Array = [];
         for each(d0 in defArr0)
         {
            if(d0.getDemonDiy() != ModeDiyDefine.CLOSE)
            {
               s0 = this.saveGroup.getSave(d0.name);
               if(Boolean(s0))
               {
                  if(s0.winB)
                  {
                     sarr0.push(s0);
                  }
               }
            }
         }
         return sarr0;
      }
      
      public function getDemonWinNum() : int
      {
         var s0:WorldMapSave = null;
         var num0:int = 0;
         var sarr0:Array = this.getDemonSaveArr();
         for each(s0 in sarr0)
         {
            if(s0.demWin > 0)
            {
               num0++;
            }
         }
         return num0;
      }
      
      public function getDemonWinNum9() : int
      {
         var s0:WorldMapSave = null;
         var num0:int = 0;
         var sarr0:Array = this.getDemonSaveArr();
         for each(s0 in sarr0)
         {
            if(s0.getBestDiffWin() >= LevelDiffGetting.getDemonDiff9())
            {
               num0++;
            }
         }
         return num0;
      }
      
      public function getDemChestMax() : int
      {
         var win0:int = this.getDemonWinNum();
         var num0:int = win0 / 5;
         if(num0 > 4)
         {
            num0 = 4;
         }
         var w9:int = this.getDemonWinNum9();
         if(w9 > 4)
         {
            w9 = 4;
         }
         return num0 + w9;
      }
      
      public function getDemChestGift() : GiftAddDefineGroup
      {
         var g0:GiftAddDefineGroup = null;
         var can0:int = this.getDemChestMax() - this.saveGroup.ch;
         if(can0 > 0)
         {
            g0 = new GiftAddDefineGroup();
            g0.addThings("demonChest",can0);
            return g0;
         }
         return null;
      }
      
      public function demChestEvent() : void
      {
         var max0:int = this.getDemChestMax();
         var now0:int = this.saveGroup.ch;
         if(now0 < max0)
         {
            this.saveGroup.ch = max0;
            this.saveGroup.chA += max0 - now0;
         }
      }
      
      public function isNoSayB(id0:String) : Boolean
      {
         return this.noSayArr.indexOf(id0) >= 0;
      }
      
      public function addNoSay(id0:String) : Boolean
      {
         return ArrayMethod.addNoRepeatInArr(this.noSayArr,id0);
      }
      
      public function countNum() : String
      {
         var s0:WorldMapSave = null;
         var d0:WorldMapDefine = null;
         var str0:String = "";
         var arr0:Array = this.saveGroup.getSaveArr();
         arr0.sort(this.sortSaveByNumFun);
         for each(s0 in arr0)
         {
            d0 = s0.getDefine();
            str0 += d0.lv + "：" + d0.cnName + "    " + (s0.winNum + s0.failNum) + "\n";
         }
         return str0;
      }
      
      public function countAllLevelNum() : int
      {
         var s0:WorldMapSave = null;
         var d0:WorldMapDefine = null;
         var num0:int = 0;
         var arr0:Array = this.saveGroup.getSaveArr();
         for each(s0 in arr0)
         {
            d0 = s0.getDefine();
            num0 += s0.winNum + s0.failNum;
         }
         return num0;
      }
      
      private function sortSaveByNumFun(s1:WorldMapSave, s2:WorldMapSave) : int
      {
         var n1:int = s1.winNum + s1.failNum;
         var n2:int = s2.winNum + s2.failNum;
         if(n1 < n2)
         {
            return 1;
         }
         if(n1 > n2)
         {
            return -1;
         }
         return 0;
      }
      
      public function getBattleTime() : Number
      {
         var s0:WorldMapSave = null;
         var num0:Number = 0;
         for each(s0 in this.saveGroup.obj)
         {
            num0 += s0.countSave.time;
         }
         return num0;
      }
      
      public function zuobiPan(dps0:Number) : String
      {
         var num0:int = 0;
         if(dps0 > 5000000 && Boolean(this.saveGroup))
         {
            num0 = this.countAllLevelNum();
            if(num0 < 500)
            {
               return "战斗力高于500w，但是刷关次数低于500次";
            }
         }
         return "";
      }
      
      public function testLastLevelName() : void
      {
         var d0:WorldMapDefine = null;
         var s0:WorldMapSave = null;
         var darr0:Array = Gaming.defineGroup.worldMap.getShowMapArr();
         for each(d0 in darr0)
         {
            if(d0.lv <= 50)
            {
               s0 = this.saveGroup.getSave(d0.name);
               INIT.tempTrace(d0.name + ":" + s0.levelName);
            }
         }
      }
   }
}

