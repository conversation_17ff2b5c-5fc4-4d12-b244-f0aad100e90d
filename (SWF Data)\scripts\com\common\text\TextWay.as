package com.common.text
{
   public class TextWay
   {
      private static var han_arr1:Array = [" "];
      
      private static var han_arr2:Array = ["\f","\n","\r","\t"];
      
      private static var htmlNoArr:Array = ["/","<",">","\"","\'"];
      
      private static var cnNumArr:Array = ["零","一","二","三","四","五","六","七","八","九","十"];
      
      public function TextWay()
      {
         super();
      }
      
      public static function toHanSpace(in_name:String) : String
      {
         return toHan_byArr(in_name,han_arr1);
      }
      
      public static function toHan2(in_name:String) : String
      {
         return toHan_byArr(in_name,han_arr2);
      }
      
      public static function toHan_byArr(in_name:String, clearArr:Array) : String
      {
         var n:* = undefined;
         var cc:String = null;
         var num0:int = 0;
         if(!in_name)
         {
            return "";
         }
         var n0:String = in_name;
         for(n in clearArr)
         {
            cc = clearArr[n];
            num0 = 0;
            do
            {
               num0++;
               n0 = n0.replace(cc,"");
            }
            while(n0.indexOf(cc) >= 0 && num0 < 100);
            
         }
         return n0;
      }
      
      public static function replaceStr(str0:String, s0:String, s1:String) : String
      {
         if(s1.indexOf(s0) >= 0)
         {
            return str0;
         }
         while(str0.indexOf(s0) >= 0)
         {
            str0 = str0.replace(s0,s1);
         }
         return str0;
      }
      
      public static function fleshDescription(str0:String) : String
      {
         if(str0 != null && str0 != "")
         {
            if(str0.indexOf("{") >= 0 || str0.indexOf("[") >= 0)
            {
               str0 = toHan2(str0);
               str0 = replaceStr(str0,"[n]","\n");
               str0 = replaceStr(str0,"{","<");
               str0 = replaceStr(str0,"}",">");
            }
         }
         return str0;
      }
      
      public static function getText(str:String) : String
      {
         var ss:String = null;
         var str2:String = "";
         var arr:Array = new Array();
         var len:int = int(str.length / 5);
         for(var n:int = 0; n <= len - 1; n++)
         {
            ss = str.substr(n * 5,5);
            arr[n] = String.fromCharCode(int(ss));
            str2 += arr[n];
         }
         return str2;
      }
      
      public static function toCode(str:String) : String
      {
         var ss:String = null;
         var str2:String = "";
         var arr:Array = new Array();
         var len:int = str.length;
         for(var n:int = 0; n <= len - 1; n++)
         {
            ss = str.substr(n,1);
            arr[n] = String(ss.charCodeAt());
            arr[n] = to5(arr[n]);
            str2 += arr[n];
         }
         return str2;
      }
      
      public static function toNumCode(str:String) : Number
      {
         var ss:String = null;
         var str2:Number = 0;
         var arr:Array = new Array();
         var len:int = str.length;
         for(var n:int = 0; n <= len - 1; n++)
         {
            ss = str.substr(n,1);
            arr[n] = ss.charCodeAt();
            str2 += arr[n];
         }
         return str2;
      }
      
      public static function to5(str:String) : String
      {
         var ss:String = null;
         if(str.length == 0)
         {
            ss = "00000" + str;
         }
         else if(str.length == 4)
         {
            ss = "0" + str;
         }
         else if(str.length == 3)
         {
            ss = "00" + str;
         }
         else if(str.length == 2)
         {
            ss = "000" + str;
         }
         else if(str.length == 1)
         {
            ss = "0000" + str;
         }
         else
         {
            ss = str;
         }
         return ss;
      }
      
      public static function toNum(str:String, num:int) : String
      {
         var len:int = str.length;
         var add:String = "";
         for(var n:int = 0; n <= num - len - 1; n++)
         {
            add += "0";
         }
         return add + str;
      }
      
      public static function systemChange(txt:String, radix:uint, target:uint) : String
      {
         var num:Number = parseInt(txt,radix);
         return num.toString(target);
      }
      
      public static function toCode32(str:String) : String
      {
         var ss:String = null;
         var str2:String = "";
         var arr:Array = new Array();
         var len:int = str.length;
         for(var n:int = 0; n <= len - 1; n++)
         {
            ss = str.substr(n,1);
            arr[n] = String(ss.charCodeAt());
            arr[n] = systemChange(arr[n],10,32);
            arr[n] = toNum(arr[n],4);
            str2 += arr[n];
         }
         return str2;
      }
      
      public static function getText32(str:String) : String
      {
         var ss:String = null;
         var str2:String = "";
         var arr:Array = new Array();
         var len:int = int(str.length / 4);
         for(var n:int = 0; n <= len - 1; n++)
         {
            ss = str.substr(n * 4,4);
            ss = systemChange(ss,32,10);
            ss = to5(ss);
            arr[n] = String.fromCharCode(int(ss));
            str2 += arr[n];
         }
         return str2;
      }
      
      public static function cutTextToOne(str0:String) : Array
      {
         var arr0:Array = [];
         var len0:int = str0.length;
         for(var i:int = 0; i < len0; i++)
         {
            arr0[i] = str0.charAt(i);
         }
         return arr0;
      }
      
      public static function splitOne(str0:String, str1:String) : Array
      {
         var s0:String = null;
         var s1:String = null;
         var index0:int = int(str0.indexOf(str1));
         if(index0 == -1)
         {
            return [str0];
         }
         s0 = str0.substring(0,index0);
         s1 = str0.substring(index0 + str1.length);
         return [s0,s1];
      }
      
      public static function mixedStringArr(arr0:Array, num0:int, separator0:String = "、") : String
      {
         var n:* = undefined;
         var tip0:String = null;
         var str0:String = "";
         for(n in arr0)
         {
            tip0 = arr0[n];
            str0 += tip0;
            if(n < arr0.length - 1)
            {
               if((n + 1) % num0 == 0)
               {
                  str0 += "\n";
               }
               else
               {
                  str0 += separator0;
               }
            }
         }
         return str0;
      }
      
      public static function numToCn(num0:int) : String
      {
         return cnNumArr[num0];
      }
      
      public static function numberToPer(v0:Number, fixedNum0:int = 1) : String
      {
         return Number(Number(v0 * 100).toFixed(fixedNum0)) + "%";
      }
      
      public static function numberRoundToPer(v0:Number) : String
      {
         var fixedV0:Number = Math.round(v0 * 100);
         return fixedV0 + "%";
      }
      
      public static function numberToFixed(v0:Number, num0:int) : String
      {
         return String(Number(v0.toFixed(num0)));
      }
      
      public static function haveHtmlNoB(str0:String) : String
      {
         var s0:* = null;
         for each(s0 in htmlNoArr)
         {
            if(str0.indexOf(s0) >= 0)
            {
               return s0;
            }
         }
         return "";
      }
      
      public static function clearHtmlNo(str0:String) : String
      {
         var s0:* = null;
         for each(s0 in htmlNoArr)
         {
            str0 = replaceStr(str0,s0,"*");
         }
         return str0;
      }
      
      public static function getRepeartStr(num0:int, s0:String = " ") : String
      {
         var s2:String = "";
         for(var i:int = 0; i < num0; i++)
         {
            s2 += s0;
         }
         return s2;
      }
   }
}

