package com.sounto.utils
{
   import com.sounto.math.Lines;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class StringMethod
   {
      public static const FALSE:String = "false";
      
      public static const TRUE:String = "true";
      
      private static var han_arr1:Array = [" "];
      
      private static var han_arr2:Array = [" ","\f","\n","\r","\t"];
      
      private static var htmlNoArr:Array = ["/","<",">","\"","\'","#","@","*"];
      
      private static var cnNumArr:Array = ["零","一","二","三","四","五","六","七","八","九","十"];
      
      private static var allNumStr:String = "0123456789零一二三四五六七八九十壹贰叁肆伍陆柒捌玖拾①②③④⑤⑥⑦⑧⑨⑩⑴⑵⑶⑷⑸⑹⑺⑻⑼⑽⑾⑿⒀⒁⒂⒃⒄⒅⒆⒇⒈⒉⒊⒋⒌⒍⒎⒏⒐⒑⒒⒓⒔⒕⒖⒗⒘⒙⒚⒛ⅠⅡⅢⅣⅤⅥⅦⅧⅨⅩⅪⅫⅰⅱⅲⅳⅴⅵⅶⅷⅸⅹ㈠㈡㈢㈣㈤㈥㈦㈧㈨㈩０１２３４５６７８９";
      
      private static var allNumObj:Object = {};
      
      public function StringMethod()
      {
         super();
      }
      
      public static function staticInit() : void
      {
         var s0:String = null;
         var len0:int = allNumStr.length;
         for(var i:int = 0; i < len0; i++)
         {
            s0 = allNumStr.charAt(i);
            allNumObj[s0] = "*";
         }
      }
      
      public static function converNumerStr(str0:String) : String
      {
         var s0:String = null;
         var len0:int = str0.length;
         var new0:String = "";
         for(var i:int = 0; i < len0; i++)
         {
            s0 = str0.charAt(i);
            if(allNumObj[s0] != null)
            {
               new0 += "*";
            }
            else
            {
               new0 += s0;
            }
         }
         return new0;
      }
      
      public static function isNumberB(singleStr0:String) : Boolean
      {
         var code0:int = int(singleStr0.charCodeAt());
         return code0 >= 48 && code0 <= 57;
      }
      
      public static function toHanSpace(in_name:String) : String
      {
         return toHan_byArr(in_name,han_arr1);
      }
      
      public static function toHan2(in_name:String) : String
      {
         return toHan_byArr(in_name,han_arr2);
      }
      
      public static function toHan_byArr(in_name:String, clearArr:Array) : String
      {
         var n:* = undefined;
         var cc:String = null;
         var num0:int = 0;
         if(in_name == null)
         {
            return null;
         }
         var n0:String = in_name;
         for(n in clearArr)
         {
            cc = clearArr[n];
            num0 = 0;
            do
            {
               num0++;
               n0 = n0.replace(cc,"");
            }
            while(n0.indexOf(cc) >= 0 && num0 < 100);
            
         }
         return n0;
      }
      
      public static function getRepeartStr(num0:int, s0:String = " ") : String
      {
         var s2:String = "";
         for(var i:int = 0; i < num0; i++)
         {
            s2 += s0;
         }
         return s2;
      }
      
      public static function findStrIndex(s0:String, find0:String, index0:int) : int
      {
         var f0:int = 0;
         var start0:int = 0;
         var site0:int = -1;
         for(var i:int = 0; i <= index0; i++)
         {
            f0 = int(s0.indexOf(find0,start0));
            if(f0 < 0)
            {
               return -1;
            }
            start0 = f0 + find0.length;
            site0 = f0;
         }
         return site0;
      }
      
      public static function dealCharLen(str0:String, len0:Number, rightB0:Boolean = true) : String
      {
         var num0:Number = getCharLen(str0);
         if(len0 > num0)
         {
            if(rightB0)
            {
               str0 += getRepeartStr(len0 - num0);
            }
            else
            {
               str0 = getRepeartStr(len0 - num0) + str0;
            }
         }
         return str0;
      }
      
      public static function dealCharArr(strArr0:Array, len0:Number, rightB0:Boolean = true) : String
      {
         var s0:* = undefined;
         var str0:String = "";
         for each(s0 in strArr0)
         {
            str0 += dealCharLen(String(s0),len0,rightB0);
         }
         return str0;
      }
      
      public static function getCharLen(str0:String) : Number
      {
         var char0:int = 0;
         var num0:Number = 0;
         var len0:Number = str0.length;
         for(var i:int = 0; i < len0; i++)
         {
            char0 = int(str0.charCodeAt(i));
            num0 += char0 < 256 ? 1 : 2;
         }
         return num0;
      }
      
      public static function keepCharNumLast(s0:String, num0:int, delStr0:String) : String
      {
         var c0:String = null;
         var char0:int = 0;
         var charNum0:Number = NaN;
         var len0:int = s0.length;
         var new0:String = "";
         var all0:Number = 0;
         for(var i:int = len0 - 1; i >= 0; i--)
         {
            c0 = s0.charAt(i);
            char0 = int(s0.charCodeAt(i));
            charNum0 = char0 < 256 ? 1 : 2;
            if(delStr0 != "")
            {
               if(delStr0.indexOf(c0) >= 0)
               {
                  all0 += charNum0;
                  continue;
               }
            }
            if(all0 + charNum0 > num0)
            {
               break;
            }
            new0 = c0 + new0;
            all0 += charNum0;
         }
         return new0;
      }
      
      public static function dealCharList(list0:Array, len0:Number, first0:String) : String
      {
         var arr0:Array = null;
         var n0:String = null;
         var s0:String = "";
         for each(arr0 in list0)
         {
            n0 = dealCharArr(arr0,len0);
            s0 = addNewLine(s0,n0,first0);
         }
         return s0;
      }
      
      public static function replaceStr(str0:String, s0:String, s1:String) : String
      {
         if(s1.indexOf(s0) >= 0)
         {
            return str0;
         }
         while(str0.indexOf(s0) >= 0)
         {
            str0 = str0.replace(s0,s1);
         }
         return str0;
      }
      
      public static function firstUpperCase(str0:String) : String
      {
         var s0:String = str0.charAt();
         s0 = s0.toUpperCase();
         return s0 + str0.substr(1);
      }
      
      public static function firstLowerCase(str0:String) : String
      {
         var s0:String = str0.charAt();
         s0 = s0.toLowerCase();
         return s0 + str0.substr(1);
      }
      
      public static function numToCn(num0:int) : String
      {
         return cnNumArr[num0];
      }
      
      public static function addNewLine(s0:String, new0:String, firstStr0:String = "") : String
      {
         if(new0 != "")
         {
            if(firstStr0 == "\n" && s0 == "")
            {
               new0 = new0;
            }
            else
            {
               new0 = firstStr0 + new0;
            }
            if(s0 != "")
            {
               s0 += "\n";
            }
            s0 += new0;
         }
         return s0;
      }
      
      public static function inArrNoZero(arr0:Array, s0:String) : void
      {
         if(s0 != "")
         {
            arr0.push(s0);
         }
      }
      
      public static function concatStringArr(arr0:Array, num0:int, separator0:String = "、") : String
      {
         var n:* = undefined;
         var tip0:String = null;
         var str0:String = "";
         for(n in arr0)
         {
            tip0 = arr0[n];
            str0 += tip0;
            if(n < arr0.length - 1)
            {
               if((n + 1) % num0 == 0)
               {
                  str0 += "\n";
               }
               else
               {
                  str0 += separator0;
               }
            }
         }
         return str0;
      }
      
      public static function cutTextToOne(str0:String) : Array
      {
         var arr0:Array = [];
         var len0:int = str0.length;
         for(var i:int = 0; i < len0; i++)
         {
            arr0[i] = str0.charAt(i);
         }
         return arr0;
      }
      
      public static function splitOne(str0:String, str1:String) : Array
      {
         var s0:String = null;
         var s1:String = null;
         var index0:int = int(str0.indexOf(str1));
         if(index0 == -1)
         {
            return [str0];
         }
         s0 = str0.substring(0,index0);
         s1 = str0.substring(index0 + str1.length);
         return [s0,s1];
      }
      
      public static function toPoint(str:String) : Point
      {
         var shootp:Array = str.split(",");
         var shootPoint:Point = new Point();
         if(shootp.length >= 2)
         {
            shootPoint.x = shootp[0];
            shootPoint.y = shootp[1];
         }
         return shootPoint;
      }
      
      public static function toLines(str:String) : Lines
      {
         var shootp:Array = str.split(",");
         var shootPoint:Lines = new Lines();
         if(shootp.length >= 2)
         {
            shootPoint.x = shootp[0];
            shootPoint.y = shootp[1];
         }
         if(Boolean(shootp[2]))
         {
            shootPoint.z = shootp[2];
         }
         return shootPoint;
      }
      
      public static function toRect(str:String) : Rectangle
      {
         var shootp:Array = str.split(",");
         var shootPoint:Rectangle = new Rectangle();
         if(shootp.length >= 4)
         {
            shootPoint.x = shootp[0];
            shootPoint.y = shootp[1];
            shootPoint.width = shootp[2];
            shootPoint.height = shootp[3];
         }
         return shootPoint;
      }
      
      public static function toNumberArr(str0:String, splitString0:String = ",") : Array
      {
         var n:* = undefined;
         if(str0 == "" || !str0)
         {
            return [];
         }
         var arr0:Array = str0.split(splitString0);
         for(n in arr0)
         {
            arr0[n] = Number(arr0[n]);
         }
         return arr0;
      }
      
      public static function toBoolean(str0:String) : Boolean
      {
         if(str0 == TRUE)
         {
            return true;
         }
         return false;
      }
      
      public static function haveHtmlNoB(str0:String) : String
      {
         var s0:* = null;
         for each(s0 in htmlNoArr)
         {
            if(str0.indexOf(s0) >= 0)
            {
               return s0;
            }
         }
         return "";
      }
      
      public static function clearHtmlNo(str0:String) : String
      {
         var s0:* = null;
         for each(s0 in htmlNoArr)
         {
            str0 = replaceStr(str0,s0,"*");
         }
         return str0;
      }
      
      public static function addIconPath(url0:String, path0:String, baseName0:String, last0:String) : String
      {
         if(url0 == "")
         {
            url0 = baseName0;
         }
         var s0:String = url0;
         if(last0 != "" && url0.indexOf(last0) == -1)
         {
            s0 += last0;
         }
         if(url0.indexOf("/") == -1)
         {
            s0 = path0 + s0;
         }
         return s0;
      }
   }
}

