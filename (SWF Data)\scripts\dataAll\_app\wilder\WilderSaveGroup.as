package dataAll._app.wilder
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   
   public class WilderSaveGroup
   {
      public static var pro_arr:Array = [];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var obj:Object = {};
      
      public var todayClickB:Boolean = false;
      
      public var lootB:Boolean = false;
      
      public function WilderSaveGroup()
      {
         super();
         this.keyNum = 0;
      }
      
      public function get keyNum() : Number
      {
         return this.CF.getAttribute("keyNum");
      }
      
      public function set keyNum(v0:Number) : void
      {
         this.CF.setAttribute("keyNum",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         var d0:WilderSave = null;
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.obj = ClassProperty.copySaveObj(obj0["obj"],WilderSave);
         obj0 = {};
         for each(d0 in this.obj)
         {
            if(Boolean(d0.getDefine()))
            {
               obj0[d0.name] = d0;
            }
         }
         this.obj = obj0;
      }
   }
}

