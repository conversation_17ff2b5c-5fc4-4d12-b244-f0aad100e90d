package dataAll.arms.skin
{
   public interface IO_HaveSkinData
   {
      function getCnName() : String;
      
      function getBaseSkin() : String;
      
      function getNowSkin() : String;
      
      function getSkinDefArr() : Array;
      
      function getEvoLv() : int;
      
      function getColor() : String;
      
      function getStrengthenLv() : int;
      
      function setSkin(param1:String) : void;
      
      function haveMoreSkinB() : Boolean;
      
      function getIconImgUrl(param1:int = 0, param2:int = 0) : String;
   }
}

