package dataAll._player.define
{
   import com.common.text.TextWay;
   
   public class KeywordShield
   {
      private static var arr:Array = ["充值"];
      
      public function KeywordShield()
      {
         super();
      }
      
      public static function haveKey(str0:String) : Boolean
      {
         var n0:* = null;
         for each(n0 in arr)
         {
            if(str0.indexOf(n0) >= 0)
            {
               return true;
            }
         }
         return false;
      }
      
      public static function clearKey(str0:String) : String
      {
         var n0:* = null;
         for each(n0 in arr)
         {
            str0 = TextWay.replaceStr(str0,n0,"*");
         }
         return str0;
      }
   }
}

