package com.sounto.cf
{
   import com.adobe.crypto.MD5;
   import com.sounto.utils.NumberMethod;
   
   public class CodeCF
   {
      public function CodeCF()
      {
         super();
      }
      
      public static function getText(str:String) : String
      {
         var ss:String = null;
         var str2:String = "";
         var arr:Array = new Array();
         var len:int = int(str.length / 5);
         for(var n:int = 0; n <= len - 1; n++)
         {
            ss = str.substr(n * 5,5);
            arr[n] = String.fromCharCode(int(ss));
            str2 += arr[n];
         }
         return str2;
      }
      
      public static function toCode(str:String) : String
      {
         var ss:String = null;
         var str2:String = "";
         var arr:Array = new Array();
         var len:int = str.length;
         for(var n:int = 0; n <= len - 1; n++)
         {
            ss = str.substr(n,1);
            arr[n] = String(ss.charCodeAt());
            arr[n] = to5(arr[n]);
            str2 += arr[n];
         }
         return str2;
      }
      
      public static function toNumCode(str:String) : Number
      {
         var ss:String = null;
         var str2:Number = 0;
         var arr:Array = new Array();
         var len:int = str.length;
         for(var n:int = 0; n <= len - 1; n++)
         {
            ss = str.substr(n,1);
            arr[n] = ss.charCodeAt();
            str2 += arr[n];
         }
         return str2;
      }
      
      public static function to5(str:String) : String
      {
         var ss:String = null;
         if(str.length == 0)
         {
            ss = "00000" + str;
         }
         else if(str.length == 4)
         {
            ss = "0" + str;
         }
         else if(str.length == 3)
         {
            ss = "00" + str;
         }
         else if(str.length == 2)
         {
            ss = "000" + str;
         }
         else if(str.length == 1)
         {
            ss = "0000" + str;
         }
         else
         {
            ss = str;
         }
         return ss;
      }
      
      public static function toNum(str:String, num:int) : String
      {
         var len:int = str.length;
         var add:String = "";
         for(var n:int = 0; n <= num - len - 1; n++)
         {
            add += "0";
         }
         return add + str;
      }
      
      public static function toCode32(str:String) : String
      {
         var en0:String = null;
         var str2:String = "";
         var len:int = str.length;
         for(var n:int = 0; n <= len - 1; n++)
         {
            en0 = String(str.charCodeAt(n));
            en0 = NumberMethod.systemChange(en0,10,32);
            en0 = toNum(en0,4);
            str2 += en0;
         }
         return str2;
      }
      
      public static function getText32(str:String) : String
      {
         var ss:String = null;
         var en0:String = null;
         var str2:String = "";
         var len:int = int(str.length / 4);
         for(var n:int = 0; n <= len - 1; n++)
         {
            ss = str.substr(n * 4,4);
            ss = NumberMethod.systemChange(ss,32,10);
            ss = to5(ss);
            en0 = String.fromCharCode(int(ss));
            str2 += en0;
         }
         return str2;
      }
      
      public static function uidToCode(uid0:String) : String
      {
         return MD5.hash(CodeCF.toCode32(uid0) + "2640");
      }
   }
}

