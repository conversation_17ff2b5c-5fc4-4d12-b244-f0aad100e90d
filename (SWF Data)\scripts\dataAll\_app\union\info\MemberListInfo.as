package dataAll._app.union.info
{
   import com.adobe.serialization.json.JSON2;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.top.IO_TopDataGroup;
   import dataAll._app.union.extra.MemberExtra;
   import dataAll._app.union.extra.UnionExtra;
   
   public class MemberListInfo implements IO_TopDataGroup
   {
      public var arr:Array = [];
      
      private var tempSortName:String = "";
      
      public function MemberListInfo()
      {
         super();
      }
      
      public static function getSimulatedJson(num0:int) : String
      {
         var i0:MemberInfo = null;
         var arr0:Array = [];
         for(var i:int = 0; i < num0; i++)
         {
            i0 = MemberInfo.getSimulated(i);
            arr0.push(i0);
         }
         return JSON2.encode(arr0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         this.arr = ClassProperty.copySaveArray(obj0["arr"],MemberInfo);
      }
      
      public function inData_byArr(arr0:Array) : void
      {
         if(!arr0)
         {
            return;
         }
         this.arr = ClassProperty.copySaveArray(arr0,MemberInfo);
      }
      
      public function inData_byJson(jsonStr0:String, kingUid0:Number) : void
      {
         var arr0:Array = JSON2.decode(jsonStr0);
         this.inData_byArr(arr0);
         this.inKingUid(kingUid0);
      }
      
      private function inKingUid(kingUid0:Number) : void
      {
         var m0:MemberInfo = null;
         for each(m0 in this.arr)
         {
            if(m0.uId == kingUid0)
            {
               m0.setRoleId(-1);
            }
         }
      }
      
      public function sort() : void
      {
         var n:* = undefined;
         var i0:MemberInfo = null;
         this.arr.sort(this.sortFun);
         for(n in this.arr)
         {
            i0 = this.arr[n];
            i0.rank = n + 1;
         }
      }
      
      private function sortFun(a:MemberInfo, b:MemberInfo) : int
      {
         if(a.contribution > b.contribution)
         {
            return -1;
         }
         if(a.contribution < b.contribution)
         {
            return 1;
         }
         return 0;
      }
      
      public function getTopArr(sortName0:String = "") : Array
      {
         var f0:int = 0;
         var name0:String = null;
         if(sortName0 != "")
         {
            f0 = int(this.getTopTitleCnArr().indexOf(sortName0));
            if(f0 >= 0)
            {
               name0 = MemberInfo.LIST_ARR[f0];
               return this.getSortArrByProName(name0);
            }
         }
         return this.arr;
      }
      
      public function getTopFunName() : String
      {
         return "inData_byMemberInfo";
      }
      
      public function getTopTitleCnArr() : Array
      {
         return MemberInfo.LIST_CN;
      }
      
      private function getSortArrByProName(name0:String) : Array
      {
         this.tempSortName = name0;
         var arr0:Array = this.arr.concat();
         arr0.sort(this.sortByProNameFun);
         return arr0;
      }
      
      private function sortByProNameFun(a0:MemberInfo, b0:MemberInfo) : int
      {
         var av0:* = a0.getProByUrl(this.tempSortName,true);
         var bv0:* = b0.getProByUrl(this.tempSortName,true);
         var mul0:int = 1;
         if(this.tempSortName != "rank")
         {
            mul0 = -1;
         }
         return mul0 * ArrayMethod.sortObjectFun(av0,bv0);
      }
      
      public function countInUnionInfo(u0:UnionInfo, battleScore0:int = -1) : void
      {
         var name0:* = null;
         var m0:MemberInfo = null;
         var name2:* = null;
         var nameArr0:Array = ["dps","life","money"];
         var e0:UnionExtra = u0.extraObj;
         for each(name0 in nameArr0)
         {
            e0[name0] = 0;
         }
         for each(m0 in this.arr)
         {
            for each(name2 in nameArr0)
            {
               e0[name2] += m0.extraObj[name2];
            }
         }
         if(battleScore0 >= 0)
         {
            e0.bs = battleScore0;
         }
         u0.refreshToExtra();
      }
      
      public function getRoleNum(roleId0:int) : int
      {
         var m0:MemberInfo = null;
         var num0:int = 0;
         for each(m0 in this.arr)
         {
            if(m0.roleId == roleId0)
            {
               num0++;
            }
         }
         return num0;
      }
      
      public function getAllCopyStr() : String
      {
         var i0:MemberInfo = null;
         var s0:String = "";
         s0 = "" + MemberInfo.ALL_CN;
         for each(i0 in this.arr)
         {
            s0 += "\n" + i0.getStringArrBy(MemberInfo.ALL_ARR);
         }
         return s0;
      }
      
      public function getBattleCopyStr() : String
      {
         var i0:MemberInfo = null;
         var e0:MemberExtra = null;
         var t0:String = null;
         var s0:String = "";
         for each(i0 in this.arr)
         {
            e0 = i0.extraObj;
            t0 = e0.playerName;
            if(i0.isAttackB())
            {
               t0 += "  " + i0.getBattleMapCn() + "  " + i0.getBattleScore();
            }
            s0 += t0 + "\n";
         }
         return s0;
      }
   }
}

