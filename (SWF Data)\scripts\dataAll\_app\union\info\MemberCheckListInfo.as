package dataAll._app.union.info
{
   import com.adobe.serialization.json.JSON2;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.top.IO_TopDataGroup;
   
   public class MemberCheckListInfo implements IO_TopDataGroup
   {
      public static var BAR_NUM:int = 10;
      
      public static var pro_arr:Array = [];
      
      public var applyList:Array = [];
      
      public var rowCount:int = 0;
      
      public function MemberCheckListInfo()
      {
         super();
      }
      
      public static function getByJson(json0:String) : MemberCheckListInfo
      {
         var obj0:Object = JSON2.decode(json0);
         var u0:MemberCheckListInfo = new MemberCheckListInfo();
         u0.inData_byObj(obj0);
         return u0;
      }
      
      public static function getSimulated(num0:int) : MemberCheckListInfo
      {
         var i0:MemberInfo = null;
         var u0:MemberCheckListInfo = new MemberCheckListInfo();
         for(var i:int = 0; i < num0; i++)
         {
            i0 = MemberInfo.getSimulated(i);
            u0.applyList.push(i0);
         }
         u0.rowCount = num0;
         return u0;
      }
      
      public static function getSimulatedJson(num0:int) : String
      {
         var u0:MemberCheckListInfo = getSimulated(num0);
         return JSON2.encode(u0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData(this,obj0,pro_arr);
         this.applyList = ClassProperty.copySaveArray(obj0["applyList"],MemberInfo);
      }
      
      public function getPageNum() : int
      {
         var num0:int = int((this.rowCount - 1) / BAR_NUM) + 1;
         if(num0 < 1)
         {
            num0 = 1;
         }
         return num0;
      }
      
      public function getTopArr(sortName0:String = "") : Array
      {
         return this.applyList;
      }
      
      public function getTopFunName() : String
      {
         return "inData_byCheckInfo";
      }
      
      public function getTopTitleCnArr() : Array
      {
         return MemberInfo.CHECK_CN;
      }
   }
}

