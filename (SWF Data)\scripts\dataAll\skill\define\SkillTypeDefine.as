package dataAll.skill.define
{
   import com.sounto.utils.ClassProperty;
   
   public class SkillTypeDefine
   {
      public static var pro_arr:Array = [];
      
      public var type:String = "";
      
      public var child:String = "";
      
      public var pro:String = "";
      
      public var c:String = "";
      
      public function SkillTypeDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function clone() : SkillTypeDefine
      {
         var d0:SkillTypeDefine = new SkillTypeDefine();
         d0.inData_byObj(this);
         return d0;
      }
   }
}

