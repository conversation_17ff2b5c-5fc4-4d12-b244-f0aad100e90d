package dataAll._app.edit.card
{
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.StringMethod;
   import dataAll.equip.creator.EquipPropertyDataCreator;
   
   public class BcardEvoAgent
   {
      public static const noChildArr:Array = ["RifleHornetShooter","TriceratopsEgg"];
      
      public var lifeMin:Number = 0;
      
      public var lifeMax:Number = 1;
      
      public var dpsMin:Number = 0;
      
      public var dpsMax:Number = 1;
      
      public var skill:int = 0;
      
      public var rareSkill:int = 0;
      
      public var magicSkill:int = 0;
      
      public var addProArr:Array = [];
      
      public var ranProNum:int = 0;
      
      public function BcardEvoAgent()
      {
         super();
      }
      
      public function getLife() : Number
      {
         if(this.lifeMin >= this.lifeMax)
         {
            return this.lifeMin;
         }
         return Math.ceil(NumberMethod.randomInRange(this.lifeMin,this.lifeMax));
      }
      
      public function getDps() : Number
      {
         if(this.dpsMin >= this.dpsMax)
         {
            return this.dpsMin;
         }
         return Math.ceil(NumberMethod.randomInRange(this.dpsMin,this.dpsMax));
      }
      
      public function getTip() : String
      {
         var s0:String = "";
         s0 += "生命系数：";
         if(this.lifeMin >= this.lifeMax)
         {
            s0 += this.lifeMin + "";
         }
         else
         {
            s0 += this.lifeMin + "~" + this.lifeMax;
         }
         s0 += "\n";
         s0 += "伤害系数：";
         if(this.dpsMin >= this.dpsMax)
         {
            s0 += this.dpsMin + "";
         }
         else
         {
            s0 += this.dpsMin + "~" + this.dpsMax;
         }
         s0 += "\n";
         if(this.skill > 0)
         {
            s0 += "技能 +" + this.skill + "个\n";
         }
         if(this.rareSkill > 0)
         {
            s0 += "稀有技能 +" + this.rareSkill + "个\n";
         }
         if(this.magicSkill > 0)
         {
            s0 += "特别技能 +" + this.magicSkill + "个\n";
         }
         if(this.addProArr.length > 0)
         {
            s0 += "添加属性：" + StringMethod.concatStringArr(EquipPropertyDataCreator.getProCnArr(this.addProArr),99);
            s0 += "\n";
         }
         if(this.ranProNum > 0)
         {
            s0 += "获得随机属性：" + this.ranProNum + "条";
            s0 += "\n";
         }
         return s0;
      }
   }
}

