package dataAll._app.union.building.geology
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.union.building.define.UnionGeologyThingsDefine;
   
   public class UnionGeologyThingsSave
   {
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var name:String = "";
      
      public var hangingB:Boolean = true;
      
      public function UnionGeologyThingsSave()
      {
         super();
      }
      
      public function get now() : Number
      {
         return this.CF.getAttribute("now");
      }
      
      public function set now(v0:Number) : void
      {
         this.CF.setAttribute("now",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function inDataByDefine(d0:UnionGeologyThingsDefine) : void
      {
         this.name = d0.name;
         this.now = 0;
      }
   }
}

