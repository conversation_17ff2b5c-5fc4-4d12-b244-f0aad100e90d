package dataAll._app.union.extra
{
   import dataAll._app.union.UnionData;
   import dataAll._app.union.info.MemberInfo;
   import dataAll._player.PlayerData;
   
   public class UnionExtraGetting
   {
      public var PD:PlayerData;
      
      public var U:UnionData;
      
      public function UnionExtraGetting()
      {
         super();
      }
      
      public function getMemberExtraString() : String
      {
         var e0:MemberExtra = this.getMemberExtra();
         return MemberExtra.toExtra(e0);
      }
      
      private function getMemberExtra() : MemberExtra
      {
         var unionData0:UnionData = this.PD.union;
         var meInfo0:MemberInfo = unionData0.nowMember;
         var e0:MemberExtra = new MemberExtra();
         e0.playerName = this.PD.base.save.playerName;
         e0.lv = this.PD.level;
         e0.life = this.PD.base.getMaxLife();
         e0.dps = this.PD.getDps();
         e0.money = this.PD.main.money;
         e0.vip = this.PD.vip.def.getName();
         e0.loginTime = this.PD.time.getReadTime();
         e0.conObj = unionData0.save.conObj;
         e0.conDay = unionData0.save.conDay;
         if(Boolean(meInfo0))
         {
            e0.inBattleExtra(meInfo0.extraObj);
         }
         return e0;
      }
      
      public function getUnionExtraString() : String
      {
         return this.U.nowUnion.extra;
      }
   }
}

