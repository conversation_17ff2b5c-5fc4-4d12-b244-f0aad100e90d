package dataAll._base
{
   import com.sounto.utils.TextMethod;
   import dataAll._data.ConstantDefine;
   import flash.display.DisplayObjectContainer;
   import flash.display.Loader;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.events.ProgressEvent;
   import flash.events.TextEvent;
   import flash.net.URLRequest;
   import flash.text.TextField;
   import flash.text.TextFieldAutoSize;
   
   public class LocalXmlLoader extends Sprite
   {
      private var con:DisplayObjectContainer;
      
      private var yesFun:Function;
      
      private var loaderTxt:TextField = new TextField();
      
      private var loader:Loader = new Loader();
      
      private var url:URLRequest = new URLRequest(ConstantDefine.xmlSwfUrl);
      
      public function LocalXmlLoader()
      {
         super();
         TextMethod.setNormalFormat(this.loaderTxt,14,20,false,TextFieldAutoSize.CENTER,10066329);
         addChild(this.loaderTxt);
         this.loaderTxt.styleSheet = TextMethod.getLinkCss();
         this.loaderTxt.width = ConstantDefine.WIDTH;
         this.loaderTxt.x = ConstantDefine.WIDTH / 2;
         this.loaderTxt.y = 300;
      }
      
      public function init(yesFun0:Function, con0:DisplayObjectContainer) : void
      {
         this.yesFun = yesFun0;
         this.con = con0;
         con0.addChild(this);
         this.localLoad();
      }
      
      private function linkClick(e:TextEvent) : void
      {
         this.loaderTxt.removeEventListener(TextEvent.LINK,this.linkClick);
         if(e.text == "web")
         {
            this.over(null);
         }
         else
         {
            this.localLoad();
         }
      }
      
      private function over(out0:Object) : void
      {
         this.con.removeChild(this);
         this.yesFun(out0);
      }
      
      private function localLoad() : void
      {
         this.loader.contentLoaderInfo.addEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this.loader.contentLoaderInfo.addEventListener(Event.COMPLETE,this.loaderCompleteHandler);
         this.loader.contentLoaderInfo.addEventListener(ProgressEvent.PROGRESS,this.loaderIng);
         this.loader.load(this.url);
      }
      
      private function loaderIng(event:ProgressEvent) : void
      {
         var s0:String = "加载中……";
         var per0:Number = event.bytesLoaded / event.bytesTotal;
         s0 += "\n" + event.bytesLoaded + "/" + event.bytesTotal + "  （" + per0.toFixed(3) + "）";
         this.loaderTxt.htmlText = s0;
      }
      
      private function loaderCompleteHandler(event:Event) : void
      {
         this.loader.contentLoaderInfo.removeEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this.loader.contentLoaderInfo.removeEventListener(Event.COMPLETE,this.loaderCompleteHandler);
         this.loader.contentLoaderInfo.removeEventListener(ProgressEvent.PROGRESS,this.loaderIng);
         this.over(this.loader.content);
      }
      
      private function errorHandler(e:IOErrorEvent) : void
      {
         this.loaderTxt.htmlText = String(e);
      }
   }
}

