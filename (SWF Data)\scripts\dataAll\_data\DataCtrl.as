package dataAll._data
{
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.active.AccActiveSave;
   import dataAll._app.city.CitySave;
   import dataAll._app.city.define.CityBodyDefine;
   import dataAll._app.city.define.CityHouseDefine;
   import dataAll._app.city.dress.CityDressSave;
   import dataAll._app.city.dress.CityDressSaveGroup;
   import dataAll._app.edit.EditSave;
   import dataAll._app.edit.EditSaveGroup;
   import dataAll._app.edit._def.EditProDefine;
   import dataAll._app.edit.arms.ArmsTorSaveGroup;
   import dataAll._app.edit.boss.BossEditSaveGroup;
   import dataAll._app.edit.card.BossCardSave;
   import dataAll._app.edit.card.BossCardSaveGroup;
   import dataAll._app.food.FoodBookDefine;
   import dataAll._app.food.FoodRawDefine;
   import dataAll._app.food.FoodSave;
   import dataAll._app.partner.PartnerSave;
   import dataAll._app.partner.ability.PartnerAbilityDefine;
   import dataAll._app.peak.PeakProDefine;
   import dataAll._app.peak.PeakSave;
   import dataAll._app.space.SpaceSave;
   import dataAll._app.space.craft.CraftDefine;
   import dataAll._app.space.craft.CraftSave;
   import dataAll._app.space.craft.CraftSaveGroup;
   import dataAll._app.tower.TowerDefine;
   import dataAll._app.tower.TowerSave;
   import dataAll._base.OneSave;
   import dataAll._base.OneSaveGroup;
   import dataAll._player.realName.RealNameAgent;
   import dataAll.arms.save.ArmsFilterSave;
   import dataAll.arms.skin.ArmsSkinDefine;
   import dataAll.gift.anniver.AnniverRedSave;
   import dataAll.gift.school.National2017Save;
   import dataAll.gift.yuanXiao.YuanXiaoSave;
   import dataAll.image.EffectLineDefine;
   import dataAll.pet.dispatch.PetDispatchSave;
   import dataAll.pro.dataList.DataListDefine;
   import dataAll.skill.define.SkillTypeDefine;
   import dataAll.things.define.ThingsSmeltDefine;
   import flash.utils.describeType;
   import flash.utils.getDefinitionByName;
   import gameAll.body.ai.one.HeroAiData;
   import gameAll.body.ai.one.OneAiData;
   import gameAll.body.ai.one.PlayerAiData;
   import gameAll.body.ai.one.ShootAiData;
   
   public class DataCtrl
   {
      private var proClassArr:Array = [];
      
      private var meClassArr:Array = [];
      
      public function DataCtrl()
      {
         super();
      }
      
      public function propertyAdd() : void
      {
         this.proClassArr = this.proClassArr.concat([ArmsFilterSave]);
         this.proClassArr = this.proClassArr.concat([SkillTypeDefine]);
         this.proClassArr = this.proClassArr.concat([OneAiData,ShootAiData,HeroAiData,PlayerAiData]);
         this.proClassArr = this.proClassArr.concat([TowerDefine,TowerSave]);
         this.proClassArr = this.proClassArr.concat([AccActiveSave]);
         this.proClassArr = this.proClassArr.concat([ArmsSkinDefine]);
         this.proClassArr = this.proClassArr.concat([SpaceSave,CraftSave,CraftDefine]);
         this.proClassArr = this.proClassArr.concat([RealNameAgent]);
         this.proClassArr = this.proClassArr.concat([EffectLineDefine]);
         this.proClassArr = this.proClassArr.concat([PartnerSave,PartnerAbilityDefine]);
         this.proClassArr = this.proClassArr.concat([PetDispatchSave]);
         this.proClassArr = this.proClassArr.concat([DataListDefine]);
         this.proClassArr = this.proClassArr.concat([EditSaveGroup,EditSave,EditProDefine]);
         this.proClassArr = this.proClassArr.concat([PeakProDefine,PeakSave]);
         this.proClassArr = this.proClassArr.concat([National2017Save]);
         this.proClassArr = this.proClassArr.concat([CityBodyDefine]);
         this.proClassArr = this.proClassArr.concat([ThingsSmeltDefine]);
         this.proClassArr = this.proClassArr.concat([CitySave]);
         this.proClassArr = this.proClassArr.concat([CityDressSave,CityDressSaveGroup]);
         this.proClassArr = this.proClassArr.concat([AnniverRedSave,YuanXiaoSave]);
         this.proClassArr = this.proClassArr.concat([FoodSave,FoodBookDefine,FoodRawDefine]);
         this.proClassArr = this.proClassArr.concat([OneSave,OneSaveGroup]);
         this.proClassArr = this.proClassArr.concat([BossCardSave]);
         this.meClassArr = this.meClassArr.concat([BossEditSaveGroup]);
         this.meClassArr = this.meClassArr.concat([ArmsTorSaveGroup]);
         this.meClassArr = this.meClassArr.concat([BossCardSaveGroup]);
         this.meClassArr = this.meClassArr.concat([CityHouseDefine]);
         this.meClassArr = this.meClassArr.concat([CraftSaveGroup]);
         this.proDeal();
         this.proAndMeDeal();
      }
      
      private function proDeal() : void
      {
         var class0:Class = null;
         var classArr0:Array = this.proClassArr;
         for each(class0 in classArr0)
         {
            class0["pro_arr"] = ClassProperty.getProArr(new class0());
         }
      }
      
      private function proAndMeDeal() : void
      {
         var class0:Class = null;
         var classArr0:Array = this.meClassArr;
         for each(class0 in classArr0)
         {
            this.dealClassMe(class0);
         }
      }
      
      private function dealClassMe(class0:Class) : void
      {
         var n:* = undefined;
         var extendsClass0:Class = null;
         var obj:Object = new class0();
         var arr0:Array = [];
         var xml0:XML = describeType(obj);
         var list0:XMLList = xml0.variable;
         var list2:XMLList = xml0.accessor;
         for(n in list0)
         {
            arr0.push(String(list0[n].@name));
         }
         for(n in list2)
         {
            arr0.push(String(list2[n].@name));
         }
         class0["pro_arr"] = arr0;
         var extends0:XML = xml0.extendsClass[0];
         if(Boolean(extends0))
         {
            extendsClass0 = getDefinitionByName(extends0.@type) as Class;
            class0["mePro_arr"] = ArrayMethod.deductArr(arr0,extendsClass0["pro_arr"]);
         }
      }
   }
}

