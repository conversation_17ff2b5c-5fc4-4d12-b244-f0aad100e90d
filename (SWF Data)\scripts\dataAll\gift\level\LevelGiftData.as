package dataAll.gift.level
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._app.task.define.TaskDefine;
   import dataAll._player.PlayerData;
   import dataAll.arms.ArmsData;
   import dataAll.equip.define.EquipColor;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class LevelGiftData
   {
      public var name:String = "";
      
      public var define:GiftAddDefineGroup;
      
      public var openB:Boolean = false;
      
      public var getGiftB:Boolean = false;
      
      public var taskStr:String = "";
      
      public function LevelGiftData()
      {
         super();
      }
      
      public static function getMainMustStr(cn0:String, yesB0:Boolean) : String
      {
         if(yesB0)
         {
            return TextMethod.color(cn0 + "√","#00FF00");
         }
         return TextMethod.color(cn0,"#FFFF00");
      }
      
      public static function getEvoArmsNum(pd0:PlayerData, moreColor0:String) : int
      {
         var da0:ArmsData = null;
         var num0:int = 0;
         var arr0:Array = pd0.getArmsDataArr(true,true,true);
         for each(da0 in arr0)
         {
            if(da0.def.isCanEvoB())
            {
               if(EquipColor.moreColorPan(da0.getSaveColor(),moreColor0))
               {
                  num0++;
               }
            }
         }
         return num0;
      }
      
      public function haveInfoB() : Boolean
      {
         return this.define.info != "";
      }
      
      public function getShowB() : Boolean
      {
         return this.getGiftB == false;
      }
      
      public function fleshMust(pd0:PlayerData) : void
      {
         var nowNum0:int = 0;
         var mustNum0:int = 0;
         var taskD0:TaskDefine = null;
         var mustB0:Boolean = false;
         var mustStr0:String = this.define.cnName;
         if(mustStr0 != "")
         {
            nowNum0 = 0;
            mustNum0 = this.define.mustLevel;
            if(mustStr0 == "darkgoldArms")
            {
               nowNum0 = getEvoArmsNum(pd0,EquipColor.DARKGOLD);
               mustB0 = nowNum0 >= mustNum0;
               this.taskStr = "进阶出" + ComMethod.colorMustNum(nowNum0,mustNum0) + "把\n" + getMainMustStr("暗金武器",mustB0);
            }
            else if(mustStr0 == "purgoldArms")
            {
               nowNum0 = getEvoArmsNum(pd0,EquipColor.PURGOLD);
               mustB0 = nowNum0 >= mustNum0;
               this.taskStr = "进阶出" + ComMethod.colorMustNum(nowNum0,mustNum0) + "把\n" + getMainMustStr("无双武器",mustB0);
            }
            else
            {
               taskD0 = Gaming.defineGroup.task.getOneDefine(mustStr0);
               if(Boolean(taskD0))
               {
                  this.taskStr = "需完成" + TextMethod.color(taskD0.getFatherCnShort(),"#00FFFF") + "任务\n";
                  if(pd0.task.isCompleteB(mustStr0))
                  {
                     mustB0 = true;
                  }
                  this.taskStr += getMainMustStr(taskD0.cnName,mustB0);
               }
               else
               {
                  this.taskStr = "找不到任务：" + mustStr0;
               }
            }
         }
         else
         {
            mustB0 = true;
            this.taskStr = "";
         }
         this.openB = mustB0 && pd0.level >= this.define.mustLevel;
      }
   }
}

