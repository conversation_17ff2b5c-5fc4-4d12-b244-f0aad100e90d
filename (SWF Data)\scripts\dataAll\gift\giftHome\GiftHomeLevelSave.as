package dataAll.gift.giftHome
{
   import com.sounto.utils.ClassProperty;
   
   public class GiftHomeLevelSave
   {
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var openB:Boolean = false;
      
      public var specialB:Boolean = false;
      
      public var giftB:Boolean = false;
      
      public var specialGiftB:Boolean = false;
      
      public function GiftHomeLevelSave()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function inDataByDefine(d0:GiftHomeLevelDefine) : void
      {
         this.name = d0.name;
      }
      
      public function getDefine() : GiftHomeLevelDefine
      {
         return Gaming.defineGroup.giftHome.getLevelDefine(this.name);
      }
      
      public function inAllNum(num0:Number) : void
      {
         var d0:GiftHomeLevelDefine = this.getDefine();
         this.openB = num0 >= d0.allMustNum;
      }
      
      public function inOccupyNum(num0:Number) : void
      {
         var d0:GiftHomeLevelDefine = this.getDefine();
         if(!this.specialB)
         {
            this.specialB = d0.panEndString(num0);
         }
      }
   }
}

