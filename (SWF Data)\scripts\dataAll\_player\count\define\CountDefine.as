package dataAll._player.count.define
{
   public class CountDefine
   {
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var pro:String = "";
      
      public var valueIsIndexB:Boolean = false;
      
      public var mustLv:int = 0;
      
      public var range:Array = [];
      
      public function CountDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var x0:XML = null;
         this.name = xml0.@name;
         this.cnName = xml0.@cnName;
         this.pro = xml0.@pro;
         if(String(xml0.@mustLv) != "")
         {
            this.mustLv = int(xml0.@mustLv);
         }
         if(String(xml0.@valueIsIndexB) != "")
         {
            this.valueIsIndexB = Boolean(int(xml0.@valueIsIndexB));
         }
         var xl0:XMLList = xml0.range;
         for each(x0 in xl0)
         {
            this.range.push(Number(x0));
         }
      }
      
      public function getStringBy(v0:Number) : String
      {
         var num0:int = this.getNum(v0);
         return this.pro + "=" + num0;
      }
      
      private function getNum(v0:Number) : int
      {
         var n:* = undefined;
         var range0:Number = NaN;
         if(this.valueIsIndexB)
         {
            return int(v0);
         }
         for(n in this.range)
         {
            range0 = Number(this.range[n]);
            if(v0 <= range0)
            {
               return n + 1;
            }
         }
         return this.range.length;
      }
      
      public function getText() : String
      {
         var v0:Number = NaN;
         var str0:String = "";
         var before0:Number = 0;
         for each(v0 in this.range)
         {
            if(before0 == v0)
            {
               str0 += "\n" + v0;
            }
            else
            {
               str0 += "\n" + before0 + " ~ " + v0;
            }
            before0 = v0 + 1;
         }
         return str0;
      }
   }
}

