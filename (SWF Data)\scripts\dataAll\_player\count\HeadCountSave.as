package dataAll._player.count
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll.level.LevelCountSave;
   
   public class HeadCountSave
   {
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var continuousLoginDay:HeadCountProSave = new HeadCountProSave();
      
      public var askAllRightNum:HeadCountProSave = new HeadCountProSave();
      
      public var topOneNameArr:Array = [];
      
      public function HeadCountSave()
      {
         super();
         this.petEvoNum = 0;
         this.vehicleEvoNum = 0;
         this.armsRemakeNum = 0;
         this.equipRemakeNum = 0;
         this.headShotLevelNum = 0;
      }
      
      public function get petEvoNum() : Number
      {
         return this.CF.getAttribute("petEvoNum");
      }
      
      public function set petEvoNum(v0:Number) : void
      {
         this.CF.setAttribute("petEvoNum",v0);
      }
      
      public function get vehicleEvoNum() : Number
      {
         return this.CF.getAttribute("vehicleEvoNum");
      }
      
      public function set vehicleEvoNum(v0:Number) : void
      {
         this.CF.setAttribute("vehicleEvoNum",v0);
      }
      
      public function get armsRemakeNum() : Number
      {
         return this.CF.getAttribute("armsRemakeNum");
      }
      
      public function set armsRemakeNum(v0:Number) : void
      {
         this.CF.setAttribute("armsRemakeNum",v0);
      }
      
      public function get equipRemakeNum() : Number
      {
         return this.CF.getAttribute("equipRemakeNum");
      }
      
      public function set equipRemakeNum(v0:Number) : void
      {
         this.CF.setAttribute("equipRemakeNum",v0);
      }
      
      public function get headShotLevelNum() : Number
      {
         return this.CF.getAttribute("headShotLevelNum");
      }
      
      public function set headShotLevelNum(v0:Number) : void
      {
         this.CF.setAttribute("headShotLevelNum",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function newDayCtrl(nowTimeStr0:String) : void
      {
         var name0:* = null;
         var a0:* = undefined;
         for each(name0 in pro_arr)
         {
            a0 = this[name0];
            if(a0 is HeadCountProSave)
            {
               (a0 as HeadCountProSave).newDayCtrl(nowTimeStr0);
            }
         }
      }
      
      public function getValue(name0:String) : *
      {
         var a0:* = undefined;
         if(this.hasOwnProperty(name0))
         {
            a0 = this[name0];
            if(a0 is HeadCountProSave)
            {
               return (a0 as HeadCountProSave).value;
            }
            if(a0 is Function)
            {
               return a0();
            }
            return a0;
         }
         return null;
      }
      
      public function getTopOneNum() : int
      {
         return this.topOneNameArr.length;
      }
      
      public function inTop(name0:String, rank0:int, compareValue0:int = 1) : void
      {
         var proName0:String = null;
         var a0:HeadCountProSave = null;
         var nowTimeStr0:String = null;
         if(rank0 <= compareValue0)
         {
            proName0 = name0 + "_" + compareValue0;
            if(this.hasOwnProperty(proName0))
            {
               a0 = this[proName0] as HeadCountProSave;
               if(a0 is HeadCountProSave)
               {
                  nowTimeStr0 = Gaming.api.save.getNowServerDate().getStr();
                  a0.add(nowTimeStr0);
               }
            }
         }
         if(rank0 == 1)
         {
            if(this.topOneNameArr.indexOf(name0) == -1)
            {
               this.topOneNameArr.push(name0);
            }
         }
      }
      
      public function inCountSaveWhenWin(levelCount0:LevelCountSave, winB0:Boolean) : void
      {
         if(levelCount0.getHeadshotRate() >= 0.8 && winB0)
         {
            ++this.headShotLevelNum;
         }
         else
         {
            this.headShotLevelNum = 0;
         }
      }
      
      public function repair6_1() : void
      {
         this.topOneNameArr.length = 0;
      }
   }
}

