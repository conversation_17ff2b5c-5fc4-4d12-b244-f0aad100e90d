package dataAll._app.worldMap
{
   import UI.base.grid.NormalGrid;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.StringMethod;
   import dataAll._app.task.TaskData;
   import dataAll._app.task.define.TaskDefine;
   import dataAll._app.union.battle.UnionBattleMapDefine;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._app.worldMap.save.WorldMapSave;
   import dataAll._player.base.PlayerBaseData;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.equip.define.EquipColor;
   import dataAll.level.LevelDiffGetting;
   import dataAll.level.define.LevelDefine;
   import dataAll.level.modeDiy.ModeDiyDefine;
   import gameAll.drop.BodyDropCtrl;
   import gameAll.drop.bodyDrop.MadBodyDrop;
   
   public class WorldMapData
   {
      public var worldMapDataGroup:WorldMapDataGroup;
      
      public var id:String = "";
      
      private var save:WorldMapSave = null;
      
      public var def:WorldMapDefine;
      
      public var levelDef:LevelDefine;
      
      public var taskData:TaskData = null;
      
      public var battleDef:UnionBattleMapDefine = null;
      
      public var tempLevel:int = 1;
      
      public function WorldMapData()
      {
         super();
      }
      
      public static function getEndlessWeekMax() : int
      {
         return 2;
      }
      
      public function setSave(s0:WorldMapSave) : void
      {
         this.save = s0;
      }
      
      public function getSaveNull() : WorldMapSave
      {
         return this.save;
      }
      
      public function canDiffB() : Boolean
      {
         if(Boolean(this.save))
         {
            if(this.save.winB && !this.taskData)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getEnemyLv(diff0:int = -1) : int
      {
         var wilderB0:Boolean = false;
         if(Boolean(this.battleDef))
         {
            this.tempLevel = this.battleDef.getEnemyLv();
         }
         else
         {
            wilderB0 = false;
            if(Boolean(this.levelDef))
            {
               wilderB0 = this.levelDef.isWilderB();
               this.tempLevel = this.levelDef.info.enemyLv;
            }
            if(!wilderB0)
            {
               if(Boolean(this.taskData))
               {
                  if(this.taskData.def.levelIsTaskLvB)
                  {
                     this.tempLevel = this.taskData.getLv();
                  }
               }
            }
         }
         if(this.tempLevel < 1)
         {
            this.tempLevel = 1;
         }
         return this.tempLevel;
      }
      
      public function getEnemyLvByModel(model0:String, diff0:int = -1) : int
      {
         if(model0 == MapMode.CHALLENGE)
         {
            if(this.getChallengeLevel() > 0)
            {
               return this.getChallengeLevel();
            }
         }
         else if(model0 == MapMode.DEMON)
         {
            return MapMode.getDemonLv();
         }
         return this.getEnemyLv(diff0);
      }
      
      private function getChallengeLevel() : int
      {
         if(Boolean(this.save))
         {
            return this.save.challengeLevel;
         }
         return 0;
      }
      
      private function getDiff(model0:String) : Number
      {
         var diff0:Number = 1;
         if(Boolean(this.levelDef) && model0 != MapMode.DEMON)
         {
            diff0 = this.levelDef.info.diff;
         }
         if(Boolean(this.battleDef))
         {
            diff0 = 1;
         }
         if(Boolean(this.taskData))
         {
            diff0 = this.taskData.dealLevelDiff(diff0);
         }
         return diff0;
      }
      
      public function getDpsFator(dpsDiffMul0:Number, model0:String, diyD0:ModeDiyDefine, camp0:String) : Number
      {
         var v0:Number = this.getDiff(model0);
         if(Boolean(this.battleDef))
         {
            v0 *= this.battleDef.getDpsFator();
         }
         if(!this.taskData)
         {
            v0 *= dpsDiffMul0;
         }
         else
         {
            v0 *= this.taskData.getDpsMul(camp0);
         }
         if(Boolean(diyD0))
         {
            v0 *= diyD0.dpsMul;
         }
         return v0;
      }
      
      public function getLifeFator(lifeDiffMul0:Number, model0:String, diyD0:ModeDiyDefine, camp0:String) : Number
      {
         var v0:Number = this.getDiff(model0);
         if(Boolean(this.battleDef))
         {
            v0 *= this.battleDef.getLifeFator();
         }
         if(!this.taskData)
         {
            v0 *= lifeDiffMul0;
         }
         else
         {
            v0 *= this.taskData.getLifeMul(camp0);
         }
         if(Boolean(diyD0))
         {
            v0 *= diyD0.lifeMul;
         }
         return v0;
      }
      
      public function getMapCnName() : String
      {
         if(Boolean(this.def))
         {
            return this.def.cnName;
         }
         if(Boolean(this.levelDef))
         {
            return this.levelDef.name;
         }
         return "";
      }
      
      public function getName() : String
      {
         if(Boolean(this.def))
         {
            return this.def.name;
         }
         return "";
      }
      
      public function getModeDiyDef(mode0:String) : ModeDiyDefine
      {
         if(Boolean(this.levelDef))
         {
            if(this.levelDef.info.modeDiy != "")
            {
               return Gaming.defineGroup.modelDiy.getDefine(this.levelDef.info.modeDiy);
            }
         }
         if(mode0 == MapMode.DEMON)
         {
            if(Boolean(this.save))
            {
               return this.save.getDemonDiyDef();
            }
         }
         if(this.def.isGreenIsB())
         {
            return Gaming.defineGroup.modelDiy.getDefine(ModeDiyDefine.GREEN_IS);
         }
         return ModeDiyDefine.ZERO;
      }
      
      public function getOneBossDefine() : NormalBodyDefine
      {
         var cnArr0:Array = null;
         var cn0:String = null;
         if(Boolean(this.save))
         {
            cnArr0 = this.save.getBossCnArr(this.levelDef);
            if(Boolean(cnArr0) && cnArr0.length > 0)
            {
               cn0 = cnArr0[0];
               return Gaming.defineGroup.body.getCnDefine(cn0);
            }
         }
         return null;
      }
      
      public function getOneBossName() : String
      {
         var d0:NormalBodyDefine = this.getOneBossDefine();
         if(Boolean(d0))
         {
            return d0.name;
         }
         return "";
      }
      
      public function isMustTaskActivedB() : Boolean
      {
         var taskB0:Boolean = false;
         if(this.def.unlockTask == "no")
         {
            taskB0 = false;
         }
         else if(this.def.unlockTask != "")
         {
            if(Boolean(this.worldMapDataGroup))
            {
               if(this.worldMapDataGroup.playerData.task.isCompleteB(this.def.unlockTask))
               {
                  taskB0 = true;
               }
            }
         }
         else
         {
            taskB0 = true;
         }
         return taskB0;
      }
      
      public function dealModeGrip(grip0:NormalGrid) : void
      {
         var diyD0:ModeDiyDefine = null;
         var lv0:int = 0;
         if(!this.save)
         {
            return;
         }
         var mode0:String = grip0.label;
         if(mode0 == MapMode.DEMON)
         {
            diyD0 = this.save.getDemonDiyDef();
            grip0.visible = true;
            grip0.actived = this.save.getDemModeActied(this.worldMapDataGroup);
            grip0.setLevelText(diyD0.getTitle(true,false));
         }
         else
         {
            lv0 = this.getEnemyLvByModel(mode0);
            grip0.setLevelText(lv0 + "级");
            grip0.visible = lv0 > 0;
            if(mode0 == MapMode.ENDLESS)
            {
               grip0.actived = this.canEndlessB();
               grip0.visible = this.haveEndlessB(lv0);
            }
            else if(mode0 == MapMode.CHALLENGE)
            {
               grip0.actived = !(this.getName() == MadBodyDrop.XingGu || this.def.isAfterXingGuB());
            }
         }
      }
      
      public function getLvText(heroLv0:int) : String
      {
         if(heroLv0 > PlayerBaseData.ENEMY_LEVEL)
         {
            heroLv0 = PlayerBaseData.ENEMY_LEVEL;
         }
         var cLv0:int = this.getChallengeLevel();
         var str0:String = this.getEnemyLv() + "";
         if(cLv0 > 0)
         {
            str0 += "·" + ComMethod.color(cLv0 + "",EquipColor.htmlColorByIndex(4 - (heroLv0 - cLv0)));
         }
         return str0;
      }
      
      public function getBtnTip(heroLv0:int) : String
      {
         var taskD0:TaskDefine = null;
         var taskB0:Boolean = false;
         var task0:String = null;
         var labelS0:String = null;
         var str0:String = "";
         if(!this.save)
         {
            return str0;
         }
         if(this.def.unlockTask != "" && this.def.unlockTask != "no")
         {
            taskD0 = Gaming.defineGroup.task.getOneDefine(this.def.unlockTask);
            taskB0 = this.worldMapDataGroup.playerData.task.isCompleteB(this.def.unlockTask);
            if(taskB0 == false)
            {
               task0 = "<orange 需要完成任务/>" + taskD0.getAlertTitleText() + "<orange 才能解锁。/>";
               str0 = StringMethod.addNewLine(str0,task0 + "\n");
            }
         }
         var drop0:String = BodyDropCtrl.getMapDropTip(this);
         if(drop0 != "")
         {
            str0 = StringMethod.addNewLine(str0,drop0 + "\n");
         }
         var demon0:String = this.save.getDemBtnTip();
         if(demon0 != "")
         {
            str0 = StringMethod.addNewLine(str0,demon0 + "\n");
         }
         var labelArr0:Array = this.getLabelArr();
         if(labelArr0.length > 0)
         {
            labelS0 = ComMethod.color(StringMethod.concatStringArr(labelArr0,999),"#00FFCC");
            str0 = StringMethod.addNewLine(str0,labelS0,"地图特点：");
         }
         if(Boolean(this.save))
         {
            if(this.save.winNum > 0)
            {
               str0 = StringMethod.addNewLine(str0,"累计挑战次数：<blove " + this.save.winNum + "/>");
            }
         }
         var bossStr0:String = this.save.getBossTip(this.levelDef);
         return StringMethod.addNewLine(str0,bossStr0);
      }
      
      public function getModeTip(mode0:String, btnActivedB0:Boolean) : String
      {
         if(!this.save)
         {
            return "";
         }
         if(mode0 == MapMode.ENDLESS)
         {
            return this.getEndlessModeTip(btnActivedB0);
         }
         if(mode0 == MapMode.DEMON)
         {
            return this.save.getDemModeTip(this.worldMapDataGroup);
         }
         if(mode0 == MapMode.REGULAR)
         {
            return BodyDropCtrl.getMapDropTip(this);
         }
         if(btnActivedB0 == false)
         {
            return "未开放";
         }
         return "";
      }
      
      public function getDiffTip(mode0:String, diff0:int, btnActived0:Boolean) : String
      {
         var lv0:int = 0;
         var openB0:Boolean = false;
         if(!this.save)
         {
            return "";
         }
         var str0:String = "";
         if(mode0 == MapMode.DEMON)
         {
            str0 = this.save.getDemDiffTip(diff0);
         }
         else
         {
            lv0 = this.getEnemyLvByModel(mode0,diff0);
            openB0 = LevelDiffGetting.getDiffOpen(diff0,mode0,lv0,this.getName());
            if(openB0)
            {
               if(mode0 == MapMode.CHALLENGE)
               {
                  str0 = this.getChallengeDiffTip(diff0,lv0,btnActived0);
               }
               else if(mode0 == MapMode.REGULAR)
               {
                  str0 = this.getRegularDiffTip(diff0,lv0,btnActived0);
               }
            }
            else
            {
               str0 = "未开放";
            }
         }
         return str0;
      }
      
      private function getRegularDiffTip(diff0:int, lv0:int, btnActived0:Boolean) : String
      {
         var str0:String = "";
         var mapName0:String = this.getName();
         if(mapName0 == MadBodyDrop.XingGu)
         {
            str0 = MadBodyDrop.getMadheartDiffTip(diff0);
         }
         else
         {
            str0 = BodyDropCtrl.getMapDropTipOne(this,diff0,MapMode.REGULAR);
         }
         if(!btnActived0)
         {
            str0 = "通关" + ComMethod.color(LevelDiffGetting.getCnName(diff0 - 1,MapMode.REGULAR) + "难度","#00FF00") + "后解锁该难度。\n\n" + str0;
         }
         return str0;
      }
      
      private function getChallengeDiffTip(diff0:int, lv0:int, btnActived0:Boolean) : String
      {
         var mapName0:String = null;
         var str0:String = "";
         var challengeNum0:int = this.save.getChallengeNum(diff0);
         var mapD0:WorldMapDefine = this.save.getChallengeMaxWorldMapDefine();
         str0 += "今日可挑战次数：" + ComMethod.colorEnoughNum(challengeNum0);
         if(Boolean(mapD0))
         {
            mapName0 = ComMethod.color(mapD0.cnName,"#00FF00") + ComMethod.color("（" + LevelDiffGetting.getCnName(diff0,MapMode.CHALLENGE) + "）","#00FFFF");
            str0 += "\n\n每次通关" + mapName0 + "都可增加1次挑战次数。";
         }
         return str0;
      }
      
      private function getEndlessModeTip(btnActivedB0:Boolean) : String
      {
         if(!this.save)
         {
            return "";
         }
         var lv0:int = this.getEnemyLvByModel(MapMode.ENDLESS);
         if(btnActivedB0 == false && this.haveEndlessB(lv0) == false)
         {
            return "未开放";
         }
         var str0:String = "";
         var allMax0:Number = this.worldMapDataGroup.getMaxEndlessScore();
         var nowMax0:Number = this.save.maxEndlessScore;
         var nowGrade0:int = this.save.maxEndlessGrade;
         var canNum0:int = this.worldMapDataGroup.getEndlessCanNumUI();
         str0 += "全图最高评分：<yellow <b>" + allMax0 + "</b>/>";
         str0 += "\n本图最高评分：<green <b>" + nowMax0 + "</b>/>";
         str0 += "\n已挑战最高层级：<blue <b>" + nowGrade0 + "</b>/>";
         str0 += "\n";
         str0 += "\n<gray 今日产生奖励剩余：/><b>" + ComMethod.colorEnoughNum(canNum0) + "<gray 次/></b>";
         if(canNum0 <= 0)
         {
            str0 += "\n<b><purple 今日挑战无尽模式将不会有奖励，也不会有掉落物。/></b>";
         }
         else
         {
            str0 += "\n" + this.worldMapDataGroup.getEndlessDirectTip(false);
         }
         return str0;
      }
      
      public function canEndlessB() : Boolean
      {
         return true;
      }
      
      public function haveEndlessB(enemyLv0:int) : Boolean
      {
         if(Boolean(this.def))
         {
            return enemyLv0 >= 35 && !this.def.noEndlessB;
         }
         return false;
      }
      
      public function getLabelArr() : Array
      {
         if(Boolean(this.def))
         {
            return this.def.labelArr;
         }
         return [];
      }
      
      public function haveLabelB(name0:String) : Boolean
      {
         return this.getLabelArr().indexOf(name0) >= 0;
      }
   }
}

