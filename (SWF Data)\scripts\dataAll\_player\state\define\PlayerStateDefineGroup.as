package dataAll._player.state.define
{
   import dataAll._base.FirstNormalDefineGroup;
   
   public class PlayerStateDefineGroup extends FirstNormalDefineGroup
   {
      public function PlayerStateDefineGroup()
      {
         super();
         defineClass = PlayerStateDefine;
      }
      
      public function outAddDefine(d0:PlayerStateDefine) : void
      {
         addDefine(d0,d0.father);
      }
      
      public function getDefine(name0:String) : PlayerStateDefine
      {
         return obj[name0];
      }
   }
}

