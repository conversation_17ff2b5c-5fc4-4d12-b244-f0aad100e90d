package dataAll._app.ask.define
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.vip.define.VipLevelDefine;
   
   public class AskPropsDefine
   {
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var info:String = "";
      
      public var mouseTip:String = "";
      
      public var vipAddB:Boolean = true;
      
      public function AskPropsDefine()
      {
         super();
         this.num = 0;
      }
      
      public function get num() : Number
      {
         return this.CF.getAttribute("num");
      }
      
      public function set num(v0:Number) : void
      {
         this.CF.setAttribute("num",v0);
      }
      
      public function fleshMouseTip() : void
      {
         this.mouseTip += ComMethod.color(this.info,"#FFFF00");
         this.mouseTip += "\n<i1>|<b>每日可使用次数</b>";
         this.mouseTip += "\n" + this.getVipTip();
      }
      
      public function getPropsNum(vipD0:VipLevelDefine) : int
      {
         if(this.vipAddB)
         {
            return this.num + vipD0.askPropsNum;
         }
         return this.num;
      }
      
      public function getVipTip() : String
      {
         var n:* = undefined;
         var d0:VipLevelDefine = null;
         var now0:int = 0;
         var arr0:Array = Gaming.defineGroup.vip.arr;
         var num0:int = 0;
         var lv0:int = 0;
         var before_lv0:int = 0;
         var beforeNow0:int = 0;
         var str0:String = "普通：<blue " + num0 + "次/>";
         if(this.vipAddB)
         {
            for(n in arr0)
            {
               d0 = arr0[n];
               now0 = this.getPropsNum(d0);
               if(now0 > num0 || n + 1 == arr0.length)
               {
                  beforeNow0 = num0;
                  before_lv0 = lv0;
                  num0 = now0;
                  lv0 = d0.index + 1;
                  str0 += "\n" + ("VIP" + (lv0 - 1)) + "：<blue " + beforeNow0 + "次/>";
               }
            }
         }
         return str0;
      }
   }
}

