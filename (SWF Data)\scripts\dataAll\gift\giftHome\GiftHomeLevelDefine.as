package dataAll.gift.giftHome
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class GiftHomeLevelDefine
   {
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var giftObj:Object = {};
      
      public var gift:GiftAddDefineGroup = new GiftAddDefineGroup();
      
      public var specialGift:GiftAddDefineGroup = new GiftAddDefineGroup();
      
      public function GiftHomeLevelDefine()
      {
         super();
         this.lv = 0;
         this.allMustNum = 0;
         this.meMustNum = 0;
      }
      
      public function get lv() : Number
      {
         return this.CF.getAttribute("lv");
      }
      
      public function set lv(v0:Number) : void
      {
         this.CF.setAttribute("lv",v0);
      }
      
      public function get allMustNum() : Number
      {
         return this.CF.getAttribute("allMustNum");
      }
      
      public function set allMustNum(v0:Number) : void
      {
         this.CF.setAttribute("allMustNum",v0);
      }
      
      public function get meMustNum() : Number
      {
         return this.CF.getAttribute("meMustNum");
      }
      
      public function set meMustNum(v0:Number) : void
      {
         this.CF.setAttribute("meMustNum",v0);
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var gift0:GiftAddDefineGroup = null;
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         this.specialGift.inAllData_byXML(xml0.specialGift,false);
         for(var i:int = 0; i < 6; i++)
         {
            gift0 = new GiftAddDefineGroup();
            gift0.inAllData_byXML(xml0["vip" + i],false);
            this.giftObj["vip" + i] = gift0;
         }
         this.name = "v" + this.lv;
      }
      
      public function getGiftByVipLevel(lv0:int) : GiftAddDefineGroup
      {
         var i0:int = (lv0 + 1) / 2;
         return this.giftObj["vip" + i0];
      }
      
      public function getEndString() : String
      {
         var lv0:int = this.lv;
         var v0:int = 9999;
         if(lv0 == 1)
         {
            v0 = Number("222") * 1;
         }
         else if(lv0 == 2)
         {
            v0 = Number("2222") * 2;
         }
         else if(lv0 == 3)
         {
            v0 = Number("2222") * 3;
         }
         else if(lv0 == 4)
         {
            v0 = Number("2222") * 4;
         }
         return String(v0);
      }
      
      public function getAllMustString() : String
      {
         return ComMethod.numberToSmall(this.allMustNum,"",10000);
      }
      
      public function panEndString(v0:Number) : Boolean
      {
         var str0:String = String(v0);
         var end0:String = this.getEndString();
         var now0:String = str0.substr(str0.length - end0.length,end0.length);
         return end0 == now0;
      }
   }
}

