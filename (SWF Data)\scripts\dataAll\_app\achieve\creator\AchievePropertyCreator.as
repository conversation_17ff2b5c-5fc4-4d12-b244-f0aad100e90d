package dataAll._app.achieve.creator
{
   import dataAll._app.achieve.AchieveData;
   import dataAll._app.achieve.define.AchieveDefine;
   import dataAll._app.achieve.define.AchieveDefineGroup;
   import dataAll._app.achieve.define.MedelProDefine;
   import dataAll.pro.PropertyArrayDefine;
   
   public class AchievePropertyCreator
   {
      public var achieveDefineGroup:AchieveDefineGroup;
      
      public function AchievePropertyCreator()
      {
         super();
      }
      
      public function getObj(da0:AchieveData) : Object
      {
         return this.getObjByDef(da0.def);
      }
      
      public function getObjByDef(d0:AchieveDefine) : Object
      {
         var pro0:* = null;
         var obj0:Object = {};
         var proArr0:Array = d0.medelProArr;
         for each(pro0 in proArr0)
         {
            obj0[pro0] = this.getValue(pro0,d0.achieveDiff);
         }
         return obj0;
      }
      
      private function getValue(pro0:String, achieveDiff0:Number) : Number
      {
         var d0:MedelProDefine = this.achieveDefineGroup.getMedelPropertyDefine(pro0);
         if(!(d0 is MedelProDefine))
         {
            INIT.showError("找不到定义：MedelProDefine" + pro0);
         }
         return d0.v * achieveDiff0 / 100;
      }
      
      public function getGatherText(obj0:Object, proSortArr0:Array = null) : String
      {
         var pro0:* = null;
         var proD0:MedelProDefine = null;
         var d0:PropertyArrayDefine = null;
         var v0:Number = NaN;
         var color0:String = null;
         var v_color0:String = null;
         var str0:String = "";
         var allArr0:Array = proSortArr0;
         if(!(proSortArr0 is Array))
         {
            allArr0 = this.achieveDefineGroup.medelProNameArr;
         }
         var num0:int = 0;
         for each(pro0 in allArr0)
         {
            if(obj0.hasOwnProperty(pro0))
            {
               proD0 = this.achieveDefineGroup.getMedelPropertyDefine(pro0);
               d0 = Gaming.defineGroup.getPropertyArrayDefine(pro0);
               v0 = Number(obj0[pro0]);
               color0 = "gray";
               v_color0 = "yellow";
               str0 += "<" + color0 + " " + d0.cnName + "/>|<" + v_color0 + " " + d0.getValueString(v0) + "/>" + "\n";
               num0++;
            }
         }
         if(num0 == 0)
         {
            str0 += "<gray 无/>";
         }
         return str0;
      }
   }
}

