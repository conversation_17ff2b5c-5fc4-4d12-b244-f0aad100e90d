package dataAll.drop.define
{
   import com.sounto.oldUtils.ComMethod;
   
   public class EquipColorDefine extends DropColorDefine
   {
      public var normalNum:Array = [];
      
      public var specialNum:Array = [];
      
      public var redNum:Array = [];
      
      public var lvRange:Array = [];
      
      public function EquipColorDefine()
      {
         super();
      }
      
      override public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var pro0:String = null;
         super.inData_byXML(xml0);
         var pro_arr0:Array = ["normalNum","specialNum","redNum"];
         for(n in pro_arr0)
         {
            pro0 = pro_arr0[n];
            this[pro0] = ComMethod.stringToNumberArr(String(xml0[pro0]));
         }
         this.lvRange = ComMethod.stringToRangeArr(String(xml0.lvRange));
      }
   }
}

