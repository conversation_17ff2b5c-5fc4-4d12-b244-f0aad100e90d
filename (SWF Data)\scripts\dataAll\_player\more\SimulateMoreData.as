package dataAll._player.more
{
   import dataAll._player.role.RoleName;
   
   public class SimulateMoreData
   {
      private static var arr:Array = [];
      
      public function SimulateMoreData()
      {
         super();
      }
      
      public static function init() : void
      {
         addData(RoleName.Striker);
         addData(RoleName.WenJie);
         addData(RoleName.ZangShi);
         addData(RoleName.Girl);
      }
      
      private static function addData(name0:String) : void
      {
         var da0:MoreData = new MoreData();
         da0.def = Gaming.defineGroup.body.getHeroDefine(name0);
         arr.push(da0);
      }
      
      public static function getNewArr(arr0:Array) : Array
      {
         var n:* = undefined;
         var s_da0:MoreData = null;
         var bb0:Boolean = false;
         var arr2:Array = [];
         for(n in arr)
         {
            s_da0 = arr[n];
            bb0 = findData(arr0,s_da0.def.name);
            if(!bb0)
            {
               arr2.push(s_da0);
            }
         }
         return arr0.concat(arr2);
      }
      
      private static function findData(arr0:Array, name0:String) : Boolean
      {
         var i:* = undefined;
         var da0:MoreData = null;
         for(i in arr0)
         {
            da0 = arr0[i];
            if(name0 == da0.def.name)
            {
               return true;
            }
         }
         return false;
      }
   }
}

