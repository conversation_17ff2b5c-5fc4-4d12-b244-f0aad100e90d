package com.sounto.math
{
   public class TwoPoint
   {
      public var pNum:int = 0;
      
      public var x1:Number = 0;
      
      public var y1:Number = 0;
      
      public var x2:Number = 0;
      
      public var y2:Number = 0;
      
      public var ra1:Number = 0;
      
      public function TwoPoint()
      {
         super();
      }
      
      public function toString() : String
      {
         var n:* = undefined;
         var name0:String = null;
         var name_arr:Array = ["pNum","x1","x2","y1","y2"];
         var str0:String = "";
         for(n in name_arr)
         {
            name0 = name_arr[n];
            if(n < name_arr.length - 1)
            {
               str0 += name0 + ":" + this[name0] + ",";
            }
            else
            {
               str0 += name0 + ":" + this[name0];
            }
         }
         return str0;
      }
   }
}

