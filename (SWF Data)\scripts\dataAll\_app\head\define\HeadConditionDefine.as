package dataAll._app.head.define
{
   import UI.base.tip.TextGatherAnalyze;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.NumberMethod;
   
   public class HeadConditionDefine
   {
      public static var pro_arr:Array = [];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var fun:String = "";
      
      public var pro:String = "";
      
      public var info:String = "";
      
      public var progressInfo:String = "";
      
      public function HeadConditionDefine()
      {
         super();
         this.must = 10000;
      }
      
      public function get must() : Number
      {
         return this.CF.getAttribute("must");
      }
      
      public function set must(v0:Number) : void
      {
         this.CF.setAttribute("must",v0);
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         this.info = this.info.replace("[must]",ComMethod.color(NumberMethod.toFixedWan(this.must),"#FF9900"));
         this.info = this.dealOneStr(this.info);
         this.progressInfo = this.progressInfo.replace("[n]","\n");
         this.progressInfo = this.dealOneStr(this.progressInfo);
      }
      
      private function dealOneStr(str0:String) : String
      {
         str0 = TextWay.replaceStr(str0,"{","<");
         str0 = TextWay.replaceStr(str0,"}","/>");
         return TextGatherAnalyze.swapText(str0);
      }
   }
}

