package dataAll._app.wilder.define
{
   import com.common.text.TextWay;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldCodeCF;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.level.define.LevelDefine;
   
   public class WilderDefine
   {
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      private var codeCF:OldCodeCF = new OldCodeCF();
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var index:int = 0;
      
      public var id:int = 0;
      
      public var scene:String = "";
      
      public var father:String = "";
      
      public var dropArr:Vector.<WilderDropDefine> = new Vector.<WilderDropDefine>();
      
      public var levelDiy:String = "";
      
      public var noMoreB:Boolean = false;
      
      public var hideB:Boolean = false;
      
      public var noExchangeB:Boolean = false;
      
      public var noBuyB:Boolean = false;
      
      public var dropTipStr:String = "";
      
      private var wilderFatherArr:Array = ["timeHole","laboratory","desert","dryTown"];
      
      public function WilderDefine()
      {
         super();
         this.dpsMul = 1;
         this.lifeMul = 1;
         this.day = 60;
         this.openTime = "";
      }
      
      public function get dpsMul() : Number
      {
         return this.CF.getAttribute("dpsMul");
      }
      
      public function set dpsMul(v0:Number) : void
      {
         this.CF.setAttribute("dpsMul",v0);
      }
      
      public function get lifeMul() : Number
      {
         return this.CF.getAttribute("lifeMul");
      }
      
      public function set lifeMul(v0:Number) : void
      {
         this.CF.setAttribute("lifeMul",v0);
      }
      
      public function get openTime() : String
      {
         return this.codeCF.getAttribute("openTime");
      }
      
      public function set openTime(str0:String) : void
      {
         this.codeCF.setAttribute("openTime",str0);
      }
      
      public function get day() : Number
      {
         return this.CF.getAttribute("day");
      }
      
      public function set day(v0:Number) : void
      {
         this.CF.setAttribute("day",v0);
      }
      
      public function get limitAll() : Number
      {
         return this.CF.getAttribute("limitAll");
      }
      
      public function set limitAll(v0:Number) : void
      {
         this.CF.setAttribute("limitAll",v0);
      }
      
      public function inData_byXML(xml0:XML, father0:String) : void
      {
         var dx0:XML = null;
         var date0:StringDate = null;
         var close0:StringDate = null;
         var dd0:WilderDropDefine = null;
         this.father = father0;
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         var dindex0:int = 0;
         for each(dx0 in xml0.drop)
         {
            dd0 = new WilderDropDefine();
            dd0.inData_byXML(dx0);
            dd0.index = dindex0;
            this.dropArr.push(dd0);
            dindex0++;
         }
         date0 = new StringDate(this.openTime);
         close0 = date0.addDay(this.day);
      }
      
      public function getBodyDefine() : NormalBodyDefine
      {
         return Gaming.defineGroup.body.getDefine(this.name);
      }
      
      public function dealLevelDefine(d0:LevelDefine) : void
      {
         if(this.name == "FuGuang")
         {
            d0.info.dumB = true;
            d0.info.mustSingleB = true;
         }
      }
      
      public function isGM3() : Boolean
      {
         return this.name == "GM3";
      }
      
      public function isSkeletonB() : Boolean
      {
         return this.name == "Skeleton";
      }
      
      public function getDropMouseTip() : String
      {
         var nameArr0:Array = null;
         var d0:WilderDropDefine = null;
         if(this.dropTipStr == "")
         {
            nameArr0 = [];
            for each(d0 in this.dropArr)
            {
               nameArr0.push(d0.cnName);
            }
            return TextWay.mixedStringArr(nameArr0,2);
         }
         return ComMethod.color(this.dropTipStr,"#FFFF00");
      }
      
      public function wantSaveB() : Boolean
      {
         var d0:WilderDropDefine = null;
         for each(d0 in this.dropArr)
         {
            if(d0.pro < 1 || d0.d0 < 1 && d0.d0 > 0)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getIconUrl() : String
      {
         return "WilderUI/" + this.name;
      }
      
      public function getBackUrl() : String
      {
         return "WilderUI/" + this.father;
      }
      
      public function getBossSkillTip() : String
      {
         var d0:NormalBodyDefine = this.getBodyDefine();
         return d0.getWilderSkillTip("#009900");
      }
      
      public function getUIDropGiftG(diff0:String) : GiftAddDefineGroup
      {
         var d0:WilderDropDefine = null;
         var num0:Number = NaN;
         var gd0:GiftAddDefine = null;
         var g0:GiftAddDefineGroup = new GiftAddDefineGroup();
         for each(d0 in this.dropArr)
         {
            num0 = d0.getNum(diff0);
            if(num0 > 0)
            {
               gd0 = d0.gift.clone();
               gd0.num = num0;
               g0.addGift(gd0);
            }
         }
         return g0;
      }
   }
}

