package dataAll._base
{
   public class NormalDefineGroup
   {
      public var obj:Object = {};
      
      protected var cnObj:Object = {};
      
      public var arr:Array = [];
      
      public var fatherArrObj:Object = {};
      
      public var fatherObjObj:Object = {};
      
      public var fatherNameArr:Array = null;
      
      public var fatherCnArr:Array = null;
      
      public var gatherArrObj:Object = {};
      
      public var gatherObjObj:Object = {};
      
      private var gatherFatherNameObj:Object = {};
      
      private var gatherFatherCnObj:Object = {};
      
      public var defineClass:Class;
      
      public var haveCnB:Boolean = false;
      
      protected var swfFather:String = "";
      
      protected var insideTypeArr:Array = [];
      
      protected var insideFatherSuffixB:Boolean = false;
      
      protected var inFatherXMLFirstB:Boolean = true;
      
      public function NormalDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML, fatherTitle0:String = "father", bodyTitle0:String = "body", insideDealB0:Boolean = true) : void
      {
         var gx0:XML = null;
         var gatherName0:String = null;
         var fatherXML0:XMLList = null;
         var i:* = undefined;
         var thingsXML0:XMLList = null;
         var fatherName0:String = null;
         var fatherCn0:String = null;
         var index0:int = 0;
         var n:* = undefined;
         var d0:IO_NormalDefine = null;
         var obj0:Object = null;
         var gxl0:* = xml0.gather;
         if(gxl0.length() == 0)
         {
            gxl0 = [xml0];
         }
         var beforeDefine0:Object = null;
         for each(gx0 in gxl0)
         {
            gatherName0 = gx0.@name;
            fatherXML0 = gx0[fatherTitle0];
            for(i in fatherXML0)
            {
               thingsXML0 = fatherXML0[i][bodyTitle0];
               fatherName0 = fatherXML0[i].@name;
               if(!insideDealB0 && this.insideFatherSuffixB)
               {
                  fatherName0 += "Inside";
               }
               fatherCn0 = fatherXML0[i].@cnName;
               this.inFatherXML(fatherXML0[i],gatherName0);
               if(Boolean(this.fatherNameArr) && Boolean(this.fatherCnArr))
               {
                  if(this.fatherNameArr.indexOf(fatherName0) == -1)
                  {
                     this.fatherNameArr.push(fatherName0);
                     this.fatherCnArr.push(fatherCn0);
                  }
               }
               index0 = 0;
               for(n in thingsXML0)
               {
                  d0 = this.getNewDefine(n,thingsXML0[n],fatherXML0[i]);
                  obj0 = d0;
                  if(!this.inFatherXMLFirstB)
                  {
                     this.addDefineByXml(d0,thingsXML0[n],fatherName0,gatherName0);
                  }
                  if(obj0.hasOwnProperty("inGatherXML"))
                  {
                     obj0["inGatherXML"](gx0);
                  }
                  if(obj0.hasOwnProperty("setFatherCn"))
                  {
                     obj0["setFatherCn"](fatherCn0);
                  }
                  if(obj0.hasOwnProperty("inFatherXML"))
                  {
                     obj0["inFatherXML"](fatherXML0[i]);
                  }
                  if(this.inFatherXMLFirstB)
                  {
                     this.addDefineByXml(d0,thingsXML0[n],fatherName0,gatherName0);
                  }
                  if(obj0.hasOwnProperty("inBeforeDefine"))
                  {
                     obj0["inBeforeDefine"](beforeDefine0);
                  }
                  if(Boolean(beforeDefine0))
                  {
                     if(beforeDefine0.hasOwnProperty("inAfterDefine"))
                     {
                        beforeDefine0["inAfterDefine"](d0);
                     }
                  }
                  if(gatherName0 != "")
                  {
                     this.addInGather(d0,gatherName0,fatherName0,fatherCn0);
                  }
                  beforeDefine0 = d0;
               }
            }
         }
      }
      
      protected function inFatherXML(x0:XML, gather0:String) : void
      {
      }
      
      protected function addDefineByXml(d0:IO_NormalDefine, xml0:XML, father0:String, gather0:String) : Object
      {
         var formName0:String = null;
         var haveFormB0:Boolean = false;
         var formD0:IO_NormalDefine = null;
         var fd0:NormalFormDefine = d0 as NormalFormDefine;
         if(Boolean(fd0))
         {
            formName0 = xml0.@formName;
            haveFormB0 = NormalFormDefine.haveFormPan(formName0);
            if(haveFormB0)
            {
               formD0 = this.getNormalDefine(formName0);
               if(Boolean(formD0))
               {
                  fd0.inFormData_byObj(formD0);
               }
            }
         }
         var obj0:Object = d0;
         if(obj0.hasOwnProperty("gather"))
         {
            obj0["gather"] = gather0;
         }
         this.defineInXml(d0,xml0,father0,gather0);
         return this.addDefine(d0,father0);
      }
      
      protected function defineInXml(d0:IO_NormalDefine, xml0:XML, father0:String, gather0:String) : void
      {
         d0.inData_byXML(xml0,father0);
      }
      
      protected function getNewDefine(n0:int, xml0:XML, fatherXml0:XML) : IO_NormalDefine
      {
         return new this.defineClass();
      }
      
      protected function addDefine(d0:IO_NormalDefine, father0:String) : Object
      {
         var name0:String = d0.getName();
         if(!this.fatherArrObj.hasOwnProperty(father0))
         {
            this.fatherArrObj[father0] = [];
            this.fatherObjObj[father0] = {};
         }
         this.fatherArrObj[father0].push(d0);
         this.fatherObjObj[father0][name0] = d0;
         this.obj[name0] = d0;
         this.arr.push(d0);
         if(this.haveCnB)
         {
            this.cnObj[d0.getCnName()] = d0;
         }
         return d0;
      }
      
      protected function addInGather(d0:IO_NormalDefine, gather0:String, father0:String, fatherCn0:String) : void
      {
         var name0:String = d0.getName();
         if(!this.gatherArrObj.hasOwnProperty(gather0))
         {
            this.gatherArrObj[gather0] = [];
            this.gatherObjObj[gather0] = {};
            this.gatherFatherNameObj[gather0] = [];
            this.gatherFatherCnObj[gather0] = {};
         }
         this.gatherArrObj[gather0].push(d0);
         this.gatherObjObj[gather0][name0] = d0;
         var arr0:Array = this.gatherFatherNameObj[gather0];
         var cnObj0:Object = this.gatherFatherCnObj[gather0];
         if(arr0.indexOf(father0) == -1)
         {
            arr0.push(father0);
            cnObj0[father0] = fatherCn0;
         }
      }
      
      public function afterInit() : void
      {
         var d0:NormalDefine = null;
         for each(d0 in this.obj)
         {
            if(d0.hasOwnProperty("afterInit"))
            {
               d0["afterInit"]();
            }
         }
      }
      
      public function getNormalDefine(name0:String) : IO_NormalDefine
      {
         return this.obj[name0];
      }
      
      public function getNormalDefineByCn(cn0:String) : IO_NormalDefine
      {
         return this.cnObj[cn0];
      }
      
      public function getNormalDefineByGather(gather0:String, name0:String) : IO_NormalDefine
      {
         if(this.gatherObjObj.hasOwnProperty(gather0))
         {
            return this.gatherObjObj[gather0][name0];
         }
         return null;
      }
      
      public function getArrByFather(f0:String) : Array
      {
         return this.fatherArrObj[f0];
      }
      
      public function getArrByFatherArr(fatherArr0:Array) : Array
      {
         var father0:* = null;
         var a0:Array = null;
         var arr0:Array = [];
         for each(father0 in fatherArr0)
         {
            a0 = this.getArrByFather(father0);
            if(Boolean(a0))
            {
               arr0 = arr0.concat(a0);
            }
         }
         return arr0;
      }
      
      public function getArrByGather(g0:String) : Array
      {
         return this.gatherArrObj[g0];
      }
      
      public function getArrByNameArr(nameArr0:Array) : Array
      {
         var name0:* = null;
         var d0:IO_NormalDefine = null;
         var arr0:Array = [];
         for each(name0 in nameArr0)
         {
            d0 = this.getNormalDefine(name0);
            if(!d0)
            {
               INIT.showError("找不到定义：" + name0);
            }
            arr0.push(d0);
         }
         return arr0;
      }
      
      public function getArrByNameArrGather(nameArr0:Array, gather0:String) : Array
      {
         var name0:* = null;
         var d0:IO_NormalDefine = null;
         var arr0:Array = [];
         for each(name0 in nameArr0)
         {
            d0 = this.getNormalDefineByGather(gather0,name0);
            if(!d0)
            {
               INIT.showError("找不到定义：" + name0);
            }
            arr0.push(d0);
         }
         return arr0;
      }
      
      public function getFatherNameArr(gather0:String) : Array
      {
         return this.gatherFatherNameObj[gather0];
      }
      
      public function getFatherCn(gather0:String, father0:String) : String
      {
         if(this.gatherFatherCnObj.hasOwnProperty(gather0))
         {
            if(Boolean(this.gatherFatherCnObj[gather0].hasOwnProperty(father0)))
            {
               return this.gatherFatherCnObj[gather0][father0];
            }
         }
         return "";
      }
      
      public function getSumCode() : Number
      {
         var d0:Object = null;
         var v0:Number = NaN;
         var sum0:Number = 0;
         for each(d0 in this.arr)
         {
            if(d0.hasOwnProperty("getSumCode"))
            {
               v0 = Number(d0["getSumCode"]());
               sum0 += v0;
            }
         }
         return sum0;
      }
   }
}

