package dataAll._app.edit.card
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll._base.OneSave;
   import dataAll._base.OneSaveGroup;
   
   public class BossCardSaveGroup extends OneSaveGroup
   {
      public static var pro_arr:Array = null;
      
      public static var mePro_arr:Array = null;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var fid:String = "";
      
      public var autoDown:Boolean = true;
      
      public var wePk:Array = [];
      
      public var emPk:Array = [];
      
      public var starF:int = 1;
      
      public var giftIdA:Array = [];
      
      public function BossCardSaveGroup()
      {
         super();
         saveClass = BossCardSave;
      }
      
      public function get on() : Number
      {
         return this.CF.getAttribute("on");
      }
      
      public function set on(v0:Number) : void
      {
         this.CF.setAttribute("on",v0);
      }
      
      public function get num() : Number
      {
         return this.CF.getAttribute("num");
      }
      
      public function set num(v0:Number) : void
      {
         this.CF.setAttribute("num",v0);
      }
      
      public function get hNum() : Number
      {
         return this.CF.getAttribute("hNum");
      }
      
      public function set hNum(v0:Number) : void
      {
         this.CF.setAttribute("hNum",v0);
      }
      
      public function get s7N() : Number
      {
         return this.CF.getAttribute("s7N");
      }
      
      public function set s7N(v0:Number) : void
      {
         this.CF.setAttribute("s7N",v0);
      }
      
      public function get t() : Number
      {
         return this.CF.getAttribute("t");
      }
      
      public function set t(v0:Number) : void
      {
         this.CF.setAttribute("t",v0);
      }
      
      public function get v() : Number
      {
         return this.CF.getAttribute("v");
      }
      
      public function set v(v0:Number) : void
      {
         this.CF.setAttribute("v",v0);
      }
      
      public function get demV() : Number
      {
         return this.CF.getAttribute("demV");
      }
      
      public function set demV(v0:Number) : void
      {
         this.CF.setAttribute("demV",v0);
      }
      
      public function get pkWin() : Number
      {
         return this.CF.getAttribute("pkWin");
      }
      
      public function set pkWin(v0:Number) : void
      {
         this.CF.setAttribute("pkWin",v0);
      }
      
      public function get pkWinStar() : Number
      {
         return this.CF.getAttribute("pkWinStar");
      }
      
      public function set pkWinStar(v0:Number) : void
      {
         this.CF.setAttribute("pkWinStar",v0);
      }
      
      public function get pkStone() : Number
      {
         return this.CF.getAttribute("pkStone");
      }
      
      public function set pkStone(v0:Number) : void
      {
         this.CF.setAttribute("pkStone",v0);
      }
      
      public function get g6() : Number
      {
         return this.CF.getAttribute("g6");
      }
      
      public function set g6(v0:Number) : void
      {
         this.CF.setAttribute("g6",v0);
      }
      
      public function get g7() : Number
      {
         return this.CF.getAttribute("g7");
      }
      
      public function set g7(v0:Number) : void
      {
         this.CF.setAttribute("g7",v0);
      }
      
      public function get bag() : Number
      {
         return this.CF.getAttribute("bag");
      }
      
      public function set bag(v0:Number) : void
      {
         this.CF.setAttribute("bag",v0);
      }
      
      public function get sumN() : Number
      {
         return this.CF.getAttribute("sumN");
      }
      
      public function set sumN(v0:Number) : void
      {
         this.CF.setAttribute("sumN",v0);
      }
      
      public function get evo6() : Number
      {
         return this.CF.getAttribute("evo6");
      }
      
      public function set evo6(v0:Number) : void
      {
         this.CF.setAttribute("evo6",v0);
      }
      
      public function get evo7() : Number
      {
         return this.CF.getAttribute("evo7");
      }
      
      public function set evo7(v0:Number) : void
      {
         this.CF.setAttribute("evo7",v0);
      }
      
      public function get evo8() : Number
      {
         return this.CF.getAttribute("evo8");
      }
      
      public function set evo8(v0:Number) : void
      {
         this.CF.setAttribute("evo8",v0);
      }
      
      public function get rn() : Number
      {
         return this.CF.getAttribute("rn");
      }
      
      public function set rn(v0:Number) : void
      {
         this.CF.setAttribute("rn",v0);
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         var cs0:BossCardSave = null;
         super.inData_byObj(obj0);
         ClassProperty.inData_bySaveObj(this,obj0,mePro_arr);
         if(obj0.hasOwnProperty("g6") == false)
         {
            for each(cs0 in arr)
            {
               this.countStar(cs0);
            }
         }
      }
      
      public function newDayCtrl() : void
      {
         this.t = 0;
         if(this.autoDown)
         {
            this.fid = "";
         }
      }
      
      public function newWeek() : void
      {
         this.v = 0;
         this.demV = 0;
         this.wePk.length = 0;
         this.emPk.length = 0;
      }
      
      override public function addSave(s0:OneSave) : void
      {
         super.addSave(s0);
         var cs0:BossCardSave = s0 as BossCardSave;
         cs0.id = String(lastId);
         this.countStar(cs0);
      }
      
      private function countStar(cs0:BossCardSave) : void
      {
         if(cs0.s == 6)
         {
            ++this.g6;
         }
         else if(cs0.s == 7)
         {
            ++this.g7;
         }
      }
   }
}

