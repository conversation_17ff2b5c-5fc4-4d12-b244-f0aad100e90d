package dataAll._app.union.building.define
{
   import com.sounto.utils.ClassProperty;
   
   public class UnionCookingDefine
   {
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var iconUrl:String = "";
      
      public var addArr:Array = [];
      
      public function UnionCookingDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         if(this.iconUrl == "")
         {
            this.iconUrl = "ThingsIcon/" + this.name;
         }
      }
   }
}

