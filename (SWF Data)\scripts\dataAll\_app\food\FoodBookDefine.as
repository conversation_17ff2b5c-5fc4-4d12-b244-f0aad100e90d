package dataAll._app.food
{
   import com.sounto.cf.NiuBiCF;
   import dataAll._base.NormalDefine;
   import dataAll.skill.define.SkillDefine;
   import dataAll.ui.text.ProTipType;
   
   public class FoodBookDefine extends NormalDefine
   {
      public static var pro_arr:Array = null;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var rawCnArr:Array = [];
      
      private var rawArr:Array = null;
      
      private var rawIconArr:Array = null;
      
      private var rawSkillArr:Array = null;
      
      private var gatherTip:String = "";
      
      public var iconUrl:String = "";
      
      public var iconUrl16:String = "";
      
      public function FoodBookDefine()
      {
         super();
         this.buffTime = 300;
      }
      
      public function get unlockMust() : Number
      {
         return this.CF.getAttribute("unlockMust");
      }
      
      public function set unlockMust(v0:Number) : void
      {
         this.CF.setAttribute("unlockMust",v0);
      }
      
      public function get profi() : Number
      {
         return this.CF.getAttribute("profi");
      }
      
      public function set profi(v0:Number) : void
      {
         this.CF.setAttribute("profi",v0);
      }
      
      public function get buffTime() : Number
      {
         return this.CF.getAttribute("buffTime");
      }
      
      public function set buffTime(v0:Number) : void
      {
         this.CF.setAttribute("buffTime",v0);
      }
      
      override public function inData_byXML(xml0:XML, father0:String = "") : void
      {
         super.inData_byXML(xml0,father0);
         if(this.rawCnArr.length >= 3)
         {
            this.iconUrl = "FoodUI/bookIcon2";
         }
         else
         {
            this.iconUrl = "FoodUI/bookIcon";
         }
         this.iconUrl16 = this.iconUrl + "_16";
      }
      
      override protected function getBaseClassProArr() : Array
      {
         return pro_arr;
      }
      
      override protected function getFinalProArr() : Array
      {
         return pro_arr;
      }
      
      private function fleshRawArr() : void
      {
         var cn0:* = null;
         var d0:FoodRawDefine = null;
         if(this.rawArr == null || this.rawIconArr == null || this.rawSkillArr == null)
         {
            this.rawArr = [];
            this.rawIconArr = [];
            this.rawSkillArr = [];
            for each(cn0 in this.rawCnArr)
            {
               d0 = Gaming.defineGroup.food.raw.getNormalDefineByCn(cn0) as FoodRawDefine;
               this.rawArr.push(d0.name);
               this.rawIconArr.push(d0.iconUrl + "24");
               this.rawSkillArr.push(d0.skill);
            }
         }
      }
      
      public function getRawArr() : Array
      {
         if(this.rawArr == null)
         {
            this.fleshRawArr();
         }
         return this.rawArr;
      }
      
      public function getRawIconArr() : Array
      {
         if(this.rawIconArr == null)
         {
            this.fleshRawArr();
         }
         return this.rawIconArr;
      }
      
      public function getRawSkillArr() : Array
      {
         if(this.rawSkillArr == null)
         {
            this.fleshRawArr();
         }
         return this.rawSkillArr;
      }
      
      public function getGatherTip() : String
      {
         var s0:String = null;
         var name0:* = null;
         var d0:FoodRawDefine = null;
         var skillD0:SkillDefine = null;
         if(this.gatherTip == "")
         {
            this.fleshRawArr();
            s0 = "";
            s0 += ProTipType.getTitleMixed("所需食材和拥有技能");
            for each(name0 in this.rawArr)
            {
               d0 = Gaming.defineGroup.food.raw.getNormalDefine(name0) as FoodRawDefine;
               skillD0 = Gaming.defineGroup.skill.getDefine(d0.skill);
               s0 += "\n<orange " + d0.cnName + "/>" + "：" + skillD0.getDescription();
            }
            this.gatherTip = s0;
         }
         return this.gatherTip;
      }
   }
}

