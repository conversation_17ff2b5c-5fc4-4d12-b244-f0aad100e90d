package dataAll.equip.suit
{
   import UI.bag.ItemsGripMoveCtrl;
   import dataAll.equip.EquipData;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.creator.EquipPropertyDataCreator;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipFatherDefine;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.save.EquipSave;
   import dataAll.items.ItemsDataGroup;
   import dataAll.must.define.MustDefine;
   import dataAll.ui.tip.CheckData;
   
   public class SuitCtreator
   {
      public function SuitCtreator()
      {
         super();
      }
      
      public static function loadSuit(da0:EquipData) : void
      {
         var n:* = undefined;
         var arr0:Array = null;
         var partDa0:EquipData = null;
         var wearSite0:int = 0;
         var check0:CheckData = null;
         Gaming.PG.da.equipBag.toSuitSortId(Gaming.PG.DATA.equip);
         var suitObj0:SuitEquipDataAll = new SuitEquipDataAll();
         suitObj0.inDataByOne(da0);
         for(n in suitObj0.obj)
         {
            arr0 = suitObj0.obj[n];
            partDa0 = findSuitableEquipDataInArr(arr0,Gaming.PG.DATA.level);
            if(partDa0 is EquipData)
            {
               if(partDa0.placeType == ItemsDataGroup.PLACE_BAG)
               {
                  wearSite0 = Gaming.PG.DATA.equip.getSiteByEquipType(partDa0.save.partType);
                  check0 = ItemsGripMoveCtrl.equipGripSwap(Gaming.PG.da.equipBag,Gaming.PG.DATA.equip,partDa0.save.site,wearSite0);
                  if(!check0.bb)
                  {
                     return;
                  }
               }
            }
            else
            {
               partDa0 = arr0[0];
               if(partDa0 is EquipData)
               {
                  if(partDa0.save.getTrueLevel() > Gaming.PG.DATA.level)
                  {
                     Gaming.uiGroup.alertBox.showError("装备等级太高，无法装备。");
                  }
               }
            }
         }
      }
      
      private static function findSuitableEquipDataInArr(arr0:Array, heroLv0:int) : EquipData
      {
         var n:* = undefined;
         var da0:EquipData = null;
         if(arr0.length == 0)
         {
            return null;
         }
         var arr2:Array = [];
         for(n in arr0)
         {
            da0 = arr0[n];
            if(da0.save.getTrueLevel() <= heroLv0)
            {
               arr2.push(da0);
            }
         }
         arr2.sort(ItemsDataGroup.sortByTempSortIdFun);
         return arr2[0];
      }
      
      public static function getGatherTip(da0:EquipData, cDa0:EquipData = null) : String
      {
         var n:* = undefined;
         var partD0:EquipDefine = null;
         var color0:String = null;
         if(!da0.haveSuitB)
         {
            return "";
         }
         var str0:String = "";
         var s0:EquipSave = da0.save;
         var d0:EquipDefine = s0.getDefine();
         var f0:EquipFatherDefine = Gaming.defineGroup.equip.getFatherDefine(d0.father);
         var suitObj0:SuitEquipDataAll = new SuitEquipDataAll();
         var wearB0:Boolean = da0.placeType == "wear";
         suitObj0.inDataByOne(da0,wearB0,!wearB0);
         str0 += "\n<i1>|<blue <b>" + f0.getTitleText() + "</b>" + "/>";
         for(n in f0.partObj)
         {
            partD0 = f0.partObj[n];
            color0 = suitObj0.havePart(n) ? "green " : "graydark ";
            str0 += "\n<" + color0 + partD0.cnName + "/>";
         }
         return str0 + getObjTip(da0);
      }
      
      public static function getObjTip(da0:EquipData) : String
      {
         var proNum0:int = 0;
         var isCollectAllB0:Boolean = false;
         var proArr0:Array = null;
         var proObj0:Object = null;
         var max0:int = int(EquipType.SUIT_TYPE_ARR.length);
         var s0:EquipSave = da0.save;
         var d0:EquipDefine = s0.getDefine();
         var f0:EquipFatherDefine = Gaming.defineGroup.equip.getFatherDefine(d0.father);
         var obj0:SuitEquipDataObj = new SuitEquipDataObj();
         var suitEquipDataObj0:SuitEquipDataObj = Gaming.PG.DATA.equip.suitObj;
         obj0.inData(f0,s0.getTrueLevel(),s0.color,max0);
         var str0:String = "";
         var baseProArr0:Array = f0.getSuitProNameArr();
         var beforeProNum0:int = 0;
         for(var i:int = 1; i <= max0; i++)
         {
            proNum0 = getProNumBy(s0.color,i);
            if(proNum0 > beforeProNum0)
            {
               isCollectAllB0 = false;
               if(suitEquipDataObj0 is SuitEquipDataObj)
               {
                  if(da0.placeType == "wear" && suitEquipDataObj0.name == d0.father && i <= suitEquipDataObj0.mustPartNum)
                  {
                     isCollectAllB0 = true;
                  }
               }
               proArr0 = baseProArr0.slice(beforeProNum0,proNum0);
               proObj0 = obj0.getObjByProArr(proArr0);
               str0 += getGatherTipByObj(proObj0,f0,s0.getTrueLevel(),s0.color,i,null,isCollectAllB0 ? "green" : "gray");
               beforeProNum0 = proNum0;
            }
         }
         return "\n\n" + str0;
      }
      
      public static function getGatherTipByObj(proObj0:Object, f0:EquipFatherDefine, lv0:int, color0:String, mustPartNum0:int, cProObj0:Object = null, proColor0:String = "gray") : String
      {
         var str0:String = "";
         var mStr0:String = EquipColor.getCn(color0) + lv0 + "级";
         if(EquipColor.moreBlackB(color0))
         {
            mStr0 = f0.cnName + "套装";
         }
         str0 += "<i1>|<" + color0 + " <b>" + mStr0 + "属性(" + mustPartNum0 + ")：</b>/>";
         return str0 + ("\n" + EquipPropertyDataCreator.getTextRange(proObj0,f0.getSuitProDefineArray(),proColor0,cProObj0));
      }
      
      public static function getSuitTip(f0:EquipFatherDefine, lv0:int, color0:String) : String
      {
         var proNum0:int = 0;
         var proArr0:Array = null;
         var proObj0:Object = null;
         var max0:int = int(EquipType.SUIT_TYPE_ARR.length);
         var obj0:SuitEquipDataObj = new SuitEquipDataObj();
         obj0.inData(f0,lv0,color0,max0);
         var str0:String = "";
         var baseProArr0:Array = f0.getSuitProNameArr();
         var beforeProNum0:int = 0;
         for(var i:int = 1; i <= max0; i++)
         {
            proNum0 = getProNumBy(color0,i);
            if(proNum0 > beforeProNum0)
            {
               proArr0 = baseProArr0.slice(beforeProNum0,proNum0);
               proObj0 = obj0.getObjByProArr(proArr0);
               str0 += getGatherTipByObj(proObj0,f0,lv0,color0,i);
               beforeProNum0 = proNum0;
            }
         }
         return str0;
      }
      
      public static function getObjByEquipDataGroup(dg0:EquipDataGroup) : SuitEquipDataObj
      {
         var n:* = undefined;
         var f0:EquipFatherDefine = null;
         var type0:String = null;
         var da0:EquipData = null;
         var s0:EquipSave = null;
         var d0:EquipDefine = null;
         var i0:int = 0;
         var i2:int = 0;
         var obj0:SuitEquipDataObj = null;
         var typeArr0:Array = EquipType.SUIT_TYPE_ARR;
         var colorArr0:Array = EquipColor.TYPE_ARR;
         var minLv0:int = 9999;
         var minColor0:String = colorArr0[colorArr0.length - 1];
         var partNum0:int = 0;
         var fatherName0:String = getSuitNameByEquipDataGroup(dg0);
         if(fatherName0 != "")
         {
            for(n in typeArr0)
            {
               type0 = typeArr0[n];
               da0 = dg0.getOneDataByType(type0);
               if(da0 is EquipData)
               {
                  if(da0.save.getDefine().father == fatherName0)
                  {
                     s0 = da0.save;
                     d0 = s0.getDefine();
                     i0 = int(colorArr0.indexOf(s0.color));
                     i2 = int(colorArr0.indexOf(minColor0));
                     if(s0.getTrueLevel() < minLv0)
                     {
                        minLv0 = s0.getTrueLevel();
                     }
                     if(i0 < i2)
                     {
                        minColor0 = s0.color;
                     }
                     partNum0++;
                  }
               }
            }
            f0 = Gaming.defineGroup.equip.getFatherDefine(fatherName0);
            obj0 = new SuitEquipDataObj();
            obj0.inData(f0,minLv0,minColor0,partNum0);
         }
         return obj0;
      }
      
      private static function getSuitNameByEquipDataGroup(dg0:EquipDataGroup) : String
      {
         var type0:* = null;
         var newObj0:Object = null;
         var newArr0:Array = null;
         var n:* = undefined;
         var da0:EquipData = null;
         var s0:EquipSave = null;
         var d0:EquipDefine = null;
         var father0:String = null;
         var partArr0:Array = null;
         var m:* = undefined;
         var typeArr0:Array = EquipType.SUIT_TYPE_ARR;
         var nameObj0:Object = {};
         for each(type0 in typeArr0)
         {
            da0 = dg0.getOneDataByType(type0);
            if(da0 is EquipData)
            {
               s0 = da0.save;
               d0 = s0.getDefine();
               father0 = d0.father;
               if(!nameObj0.hasOwnProperty(father0))
               {
                  nameObj0[father0] = [];
               }
               nameObj0[father0].push(type0);
            }
         }
         newObj0 = {};
         newArr0 = [];
         for(n in nameObj0)
         {
            partArr0 = nameObj0[n];
            if(partArr0.length >= 2)
            {
               newObj0[n] = partArr0;
               newArr0.push(n);
            }
         }
         if(newArr0.length >= 2)
         {
            for(m in newObj0)
            {
               if(newObj0[m].indexOf(EquipType.SUIT_PRIORITY_NAME) >= 0)
               {
                  return m;
               }
            }
            return newArr0[0];
         }
         if(newArr0.length == 1)
         {
            return newArr0[0];
         }
         return "";
      }
      
      public static function getProNumBy(color0:String, partNum0:int) : int
      {
         if(partNum0 < 2)
         {
            return 0;
         }
         var num0:int = 1;
         if(color0 == EquipColor.PURGOLD)
         {
            num0 = 2;
         }
         else if(color0 == EquipColor.DARKGOLD)
         {
            num0 = 1;
         }
         else if(color0 == EquipColor.BLACK)
         {
            num0 = 1;
         }
         else
         {
            if(color0 == EquipColor.RED)
            {
               num0 = 4;
            }
            else if(color0 == EquipColor.ORANGE)
            {
               num0 = 3;
            }
            else if(color0 == EquipColor.PURPLE)
            {
               num0 = 2;
            }
            if(partNum0 <= 2)
            {
               num0 -= 3;
               if(num0 < 1)
               {
                  num0 = 1;
               }
            }
         }
         return num0;
      }
      
      public static function getMustPartNumBy(partNum0:int) : int
      {
         if(partNum0 <= 2)
         {
            return 2;
         }
         return 3;
      }
      
      public static function converPan(dg0:EquipDataGroup) : String
      {
         var n:* = undefined;
         var type0:String = null;
         var da0:EquipData = null;
         var nowF0:EquipFatherDefine = null;
         var suitNum0:int = 0;
         var i:* = undefined;
         var typeArr0:Array = EquipType.SUIT_TYPE_ARR;
         var equipNum0:int = 0;
         var suitObj0:Object = {};
         for(n in typeArr0)
         {
            type0 = typeArr0[n];
            da0 = dg0.getOneDataByType(type0);
            if(da0 is EquipData)
            {
               nowF0 = da0.save.getFatherDefine();
               if(nowF0.haveSuitB)
               {
                  if(!suitObj0.hasOwnProperty(nowF0.name))
                  {
                     suitObj0[nowF0.name] = 1;
                  }
                  else
                  {
                     ++suitObj0[nowF0.name];
                  }
               }
               equipNum0++;
            }
         }
         if(equipNum0 < typeArr0.length)
         {
            return "no4";
         }
         suitNum0 = 0;
         for(i in suitObj0)
         {
            if(suitObj0[i] >= 2)
            {
               if(suitObj0[i] >= typeArr0.length)
               {
                  return "fill";
               }
               return "";
            }
         }
         return "no2";
      }
      
      public static function getConverMustDefineByEquipDataGroup(dg0:EquipDataGroup) : MustDefine
      {
         var n:* = undefined;
         var mustD0:MustDefine = null;
         var type0:String = null;
         var da0:EquipData = null;
         var typeArr0:Array = EquipType.SUIT_TYPE_ARR;
         var allNum0:int = 0;
         var stoneNum0:int = 0;
         var maxLv0:int = 0;
         for(n in typeArr0)
         {
            type0 = typeArr0[n];
            da0 = dg0.getOneDataByType(type0);
            if(da0 is EquipData)
            {
               if(da0.isConverB)
               {
                  stoneNum0 += getConverStoneNumByEquipData(da0);
                  allNum0++;
                  if(maxLv0 < da0.save.getTrueLevel())
                  {
                     maxLv0 = da0.save.getTrueLevel();
                  }
               }
            }
         }
         stoneNum0 *= allNum0;
         mustD0 = new MustDefine();
         mustD0.lv = maxLv0;
         mustD0.coin = Gaming.defineGroup.normal.getLevelCoinIncome(maxLv0) * 1;
         mustD0.inThingsDataByArr(["converStone;" + stoneNum0]);
         return mustD0;
      }
      
      public static function getConverStoneNumByEquipData(da0:EquipData) : Number
      {
         var base0:Number = Gaming.defineGroup.normal.getPropertyValue("converSuitMust",da0.save.getTrueLevel());
         var color0:String = da0.getColor();
         var mul0:Number = 3;
         if(color0 == EquipColor.BLUE)
         {
            mul0 = 5;
         }
         else if(color0 == EquipColor.PURPLE)
         {
            mul0 = 9;
         }
         else if(color0 == EquipColor.ORANGE)
         {
            mul0 = 15;
         }
         else if(color0 == EquipColor.RED)
         {
            mul0 = 20;
         }
         else
         {
            mul0 = 20;
         }
         return base0 * mul0;
      }
      
      public static function getSuitConver(dg0:EquipDataGroup, index0:int) : EquipDataGroup
      {
         var arr0:Array = groupingArrByFather(dg0);
         var g0:SuitEquipDataGrouping = arr0[index0 % arr0.length];
         var f0:EquipFatherDefine = g0.fatherDefine;
         var dg2:EquipDataGroup = new EquipDataGroup();
         dg2.inCloneData(dg0);
         converEquipDataGroup(dg2,f0,true);
         dg2.suitEquipDataGroupingArr = arr0;
         dg2.fleshMergeData();
         return dg2;
      }
      
      public static function converEquipDataGroup(dg0:EquipDataGroup, f0:EquipFatherDefine, converTagB0:Boolean = false) : void
      {
         var n:* = undefined;
         var type0:String = null;
         var da0:EquipData = null;
         var nowF0:EquipFatherDefine = null;
         var partD0:EquipDefine = null;
         var typeArr0:Array = EquipType.SUIT_TYPE_ARR;
         for(n in typeArr0)
         {
            type0 = typeArr0[n];
            da0 = dg0.getOneDataByType(type0);
            if(da0 is EquipData)
            {
               nowF0 = da0.save.getFatherDefine();
               if(nowF0.name != f0.name)
               {
                  partD0 = f0.getPart(type0);
                  da0.save.setImgName(partD0.name);
                  da0.save.cnName = partD0.cnName;
                  da0.fleshHaveSuitB();
                  if(converTagB0)
                  {
                     da0.isConverB = true;
                  }
               }
            }
         }
      }
      
      private static function groupingArrByFather(dg0:EquipDataGroup) : Array
      {
         var n:* = undefined;
         var arr0:Array = [];
         var obj0:Object = groupingObjByFather(dg0);
         for(n in obj0)
         {
            arr0.push(obj0[n]);
         }
         arr0.sort(groupingArrSortFun);
         return arr0;
      }
      
      private static function groupingArrSortFun(g0:SuitEquipDataGrouping, g2:SuitEquipDataGrouping) : int
      {
         if(g0.suitPartNum > g2.suitPartNum)
         {
            return -1;
         }
         if(g0.suitPartNum < g2.suitPartNum)
         {
            return 1;
         }
         if(g0.index < g2.index)
         {
            return -1;
         }
         return 1;
      }
      
      private static function groupingObjByFather(dg0:EquipDataGroup) : Object
      {
         var n:* = undefined;
         var type0:String = null;
         var da0:EquipData = null;
         var d0:EquipFatherDefine = null;
         var suitObj0:SuitEquipDataGrouping = null;
         var newObj0:SuitEquipDataGrouping = null;
         var typeArr0:Array = EquipType.SUIT_TYPE_ARR;
         var obj0:Object = {};
         var index0:int = 0;
         for(n in typeArr0)
         {
            type0 = typeArr0[n];
            da0 = dg0.getOneDataByType(type0);
            if(da0 is EquipData)
            {
               d0 = da0.save.getFatherDefine();
               if(d0.haveSuitB)
               {
                  if(!obj0.hasOwnProperty(d0.name))
                  {
                     newObj0 = new SuitEquipDataGrouping();
                     newObj0.inData(d0,index0);
                     index0++;
                     obj0[d0.name] = newObj0;
                  }
                  suitObj0 = obj0[d0.name];
                  suitObj0.addEquipData(da0);
               }
            }
         }
         return obj0;
      }
   }
}

