package dataAll._app.active.define
{
   public class ActiveTask
   {
      public static const dailySign:String = "dailySign";
      
      public static const vipDay:String = "vipDay";
      
      public static const loveGift:String = "loveGift";
      
      public static const ask:String = "ask";
      
      public static const arena:String = "arena";
      
      public static const treasure:String = "treasure";
      
      public static const unionCoin:String = "unionCoin";
      
      public static const unionMoney:String = "unionMoney";
      
      public static const unionFederal:String = "unionFederal";
      
      public static const endlessLevel:String = "endlessLevel";
      
      public static const normalLevel:String = "normalLevel";
      
      public static const wilder:String = "wilder";
      
      public static const gm3:String = "gm3";
      
      public static const wilderKey:String = "wilderKey";
      
      public static const petDispatch:String = "petDispatch";
      
      public static const strength:String = "strength";
      
      public static const smelt:String = "smelt";
      
      public static const useMoney:String = "useMoney";
      
      public static const useArenaStamp:String = "useArenaStamp";
      
      public static const useExploitCards:String = "useExploitCards";
      
      public static const postGift:String = "postGift";
      
      public static const Task:String = "Task";
      
      public static const dayTask:String = "dayTask";
      
      public static const deputyTask:String = "deputyTask";
      
      public static const extraTask:String = "extraTask";
      
      public static const kingTask:String = "kingTask";
      
      public static const treasureTask:String = "treasureTask";
      
      public static const unionArr:Array = [unionCoin,unionMoney,unionFederal,useExploitCards];
      
      public function ActiveTask()
      {
         super();
      }
   }
}

