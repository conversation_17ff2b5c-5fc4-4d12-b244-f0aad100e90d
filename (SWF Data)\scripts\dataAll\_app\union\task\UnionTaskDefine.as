package dataAll._app.union.task
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   
   public class UnionTaskDefine
   {
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var id:String = "";
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var iconUrl:String = "";
      
      public var info:String = "";
      
      public var taskLabel:String = "";
      
      public function UnionTaskDefine()
      {
         super();
         this.contribution = 0;
         this.mustNum = 1;
      }
      
      public function get contribution() : Number
      {
         return this.CF.getAttribute("contribution");
      }
      
      public function set contribution(v0:Number) : void
      {
         this.CF.setAttribute("contribution",v0);
      }
      
      public function get mustNum() : Number
      {
         return this.CF.getAttribute("mustNum");
      }
      
      public function set mustNum(v0:Number) : void
      {
         this.CF.setAttribute("mustNum",v0);
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         if(this.iconUrl == "")
         {
            this.iconUrl = "AchieveIcon/" + this.name;
         }
         if(this.taskLabel == "")
         {
            this.taskLabel = this.name;
         }
         this.info = xml0.info;
         var f0:int = int(this.info.indexOf("或"));
         if(f0 >= 0)
         {
            this.info = this.info.substring(0,f0) + ComMethod.color("或","#666666") + ComMethod.color(this.info.substring(f0 + 1));
         }
      }
   }
}

