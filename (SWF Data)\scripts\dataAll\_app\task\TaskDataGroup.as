package dataAll._app.task
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.task.define.TaskConditionDefine;
   import dataAll._app.task.define.TaskDefine;
   import dataAll._app.task.define.TaskFatherDefine;
   import dataAll._app.task.define.TaskType;
   import dataAll._app.task.save.TaskSave;
   import dataAll._app.task.save.TaskSaveGroup;
   import dataAll._player.PlayerData;
   import dataAll.level.define.unit.UnitType;
   
   public class TaskDataGroup
   {
      public var saveGroup:TaskSaveGroup = null;
      
      public var playerData:PlayerData;
      
      private var dataObj:Object = {};
      
      private var ingArr:Array = [];
      
      private var customTaskData:TaskData = null;
      
      public function TaskDataGroup()
      {
         super();
      }
      
      public function chooseCustom(da0:TaskData) : void
      {
         this.customTaskData = da0;
      }
      
      public function getNowCustomData() : TaskData
      {
         return this.customTaskData;
      }
      
      public function inData_bySaveGroup(sg0:TaskSaveGroup) : void
      {
         var n:* = undefined;
         var s0:TaskSave = null;
         var da0:TaskData = null;
         this.saveGroup = sg0;
         this.saveGroup.clearSaveNoDefine();
         for(n in sg0.dataArr)
         {
            s0 = sg0.dataArr[n];
            da0 = new TaskData();
            da0.inData_bySave(s0,this.playerData);
            this.dataObj[da0.def.name] = da0;
         }
         this.fixedLevelByMust();
         this.dealConditionCumulative();
      }
      
      private function fleshDataAndSaveGroup() : void
      {
         var n:* = undefined;
         var da0:TaskData = null;
         var arr0:Array = [];
         var obj0:Object = {};
         for(n in this.dataObj)
         {
            da0 = this.dataObj[n] as TaskData;
            if(da0 is TaskData)
            {
               if(da0.state != TaskState.del && da0.die == 0)
               {
                  obj0[da0.def.name] = da0;
                  arr0.push(da0.save);
               }
            }
         }
         this.dataObj = obj0;
         this.saveGroup.dataArr = arr0;
         Gaming.TG.task.dataChange();
      }
      
      public function fleshIngArr() : void
      {
         var i:* = undefined;
         var da0:TaskData = null;
         var arr0:Array = [];
         for(i in this.dataObj)
         {
            da0 = this.dataObj[i];
            if(da0.rolePan() && da0.state == TaskState.ing)
            {
               arr0.push(da0);
            }
         }
         this.ingArr = arr0;
      }
      
      public function fixedLevelByMust() : void
      {
         var da0:TaskData = null;
         for each(da0 in this.dataObj)
         {
            da0.fixedLevelByMust();
         }
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         var type0:* = null;
         for each(type0 in TaskType.dayClearTypeArr)
         {
            this.dayInitByFather(type0);
         }
         this.toOverIfCompleteFather(TaskType.DAY);
         this.fleshDataAndSaveGroup();
         this.fleshIngArr();
         this.saveGroup.newDayCtrl(timeStr0);
      }
      
      public function newWeek() : void
      {
         var type0:* = null;
         this.saveGroup.newWeek();
         for each(type0 in TaskType.weekClearTypeArr)
         {
            this.dayInitByFather(type0);
         }
      }
      
      public function checkCondtion(fun0:Function) : void
      {
         var da0:TaskData = null;
         var arr_len0:int = int(this.ingArr.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            da0 = this.ingArr[i];
            da0.checkFleshConditon(fun0);
         }
      }
      
      public function addNumCollectCondition(target0:String, targetId0:String, targetId2:String = "", targetId3:String = "", num0:int = 1) : void
      {
         var da0:TaskData = null;
         var d0:TaskDefine = null;
         var con0:TaskConditionDefine = null;
         var worldMapId0:String = null;
         var arr_len0:int = int(this.ingArr.length);
         var changeB0:Boolean = false;
         for(var i:int = 0; i < arr_len0; i++)
         {
            da0 = this.ingArr[i];
            if(!(da0 is TaskData))
            {
               INIT.showErrorMust("找不到TaskData在ingArr：" + i);
            }
            d0 = da0.def;
            con0 = da0.getCondition();
            if(!(con0 is TaskConditionDefine))
            {
               INIT.showError("getCondition()找不到TaskConditionDefine：" + d0.cnName);
            }
            if(con0.type == "collect")
            {
               if(con0.target == target0 && (con0.targetId == "" || con0.targetId == targetId0 || con0.targetId == targetId2 || con0.targetId == targetId3))
               {
                  worldMapId0 = da0.getWorldMapId();
                  if(worldMapId0 == "" || worldMapId0 == Gaming.LG.getNowWorldMapName())
                  {
                     if(d0.superAddNumB)
                     {
                        if(targetId3 == UnitType.SUPER)
                        {
                           num0 *= 5;
                        }
                        if(targetId3 == UnitType.BOSS)
                        {
                           num0 *= 15;
                        }
                     }
                     da0.save.c += num0;
                     da0.uiFleshB = false;
                     da0.checkCondition();
                     changeB0 = true;
                  }
               }
            }
         }
         if(changeB0)
         {
            this.fleshIngArr();
         }
      }
      
      public function addTask(name0:String) : TaskData
      {
         var da0:TaskData = this.createTask(name0);
         return this.addTaskData(da0);
      }
      
      public function addTaskData(da0:TaskData) : TaskData
      {
         this.dataObj[da0.def.name] = da0;
         this.fleshDataAndSaveGroup();
         Gaming.TG.task.add(da0);
         return da0;
      }
      
      public function createTask(name0:String) : TaskData
      {
         var d0:TaskDefine = Gaming.defineGroup.task.getOneDefine(name0);
         if(!(d0 is TaskDefine))
         {
            INIT.showError("找不到定义TaskDefine：" + name0);
         }
         var da0:TaskData = new TaskData();
         da0.inData_byDefine(d0,this.playerData);
         return da0;
      }
      
      public function getDefineArrByType(type0:String) : Array
      {
         var d0:TaskDefine = null;
         var arr0:Array = Gaming.defineGroup.task.getArrByType(type0);
         var role0:String = this.playerData.getRoleName();
         var newArr0:Array = [];
         for each(d0 in arr0)
         {
            if(d0.rolePan(role0))
            {
               newArr0.push(d0);
            }
         }
         return newArr0;
      }
      
      public function getDataArrByType(type0:String) : Array
      {
         var d0:TaskDefine = null;
         var da0:TaskData = null;
         var darr0:Array = this.getDefineArrByType(type0);
         var arr0:Array = [];
         for each(d0 in darr0)
         {
            da0 = this.getTaskDataByName(d0.name);
            if(Boolean(da0))
            {
               arr0.push(da0);
            }
         }
         return arr0;
      }
      
      public function getRoleDataObj() : Object
      {
         var i:* = undefined;
         var da0:TaskData = null;
         var new0:Object = {};
         for(i in this.dataObj)
         {
            da0 = this.dataObj[i];
            if(da0.rolePan())
            {
               new0[i] = da0;
            }
         }
         return new0;
      }
      
      public function doOneTaskFun(name0:String, funName0:String) : void
      {
         var da0:TaskData = this.getTaskDataByName(name0);
         if(da0 is TaskData)
         {
            da0[funName0]();
         }
      }
      
      public function unlockByUnlockLevel(type0:String) : TaskData
      {
         var d0:TaskDefine = null;
         var unlockB0:Boolean = false;
         var da0:TaskData = null;
         var fd0:TaskFatherDefine = Gaming.defineGroup.task.getFatherDefine(type0);
         if(!fd0.autoUnlockByLevelB)
         {
            return null;
         }
         if(TaskType.autoUnlockArr.indexOf(type0) >= 0)
         {
            return this.unlockOneByType(type0,type0 == TaskType.MAIN);
         }
         var heroLv0:int = this.playerData.level;
         var arr0:Array = this.getDefineArrByType(type0);
         var arr_len0:int = int(arr0.length);
         var unlockDa0:TaskData = null;
         for(var i:int = 0; i < arr_len0; i++)
         {
            d0 = arr0[i];
            unlockB0 = heroLv0 >= d0.unlockLv;
            if(unlockB0)
            {
               da0 = this.getTaskDataByName(d0.name);
               if(!da0)
               {
                  da0 = this.createTask(d0.name);
                  if(da0.dealOne())
                  {
                     unlockDa0 = this.addTaskData(da0);
                  }
               }
               else if(da0.save.haveDayInitB())
               {
                  if(da0.dealOne())
                  {
                     da0.save.noDayInit();
                     unlockDa0 = da0;
                  }
                  else
                  {
                     INIT.showError(da0.def.father + "  " + da0.def.cnName + " " + da0.save.c + "  错误 XXXXXXXXXXXXXXXXXXX");
                  }
               }
            }
         }
         this.fixedLevelByMust();
         return unlockDa0;
      }
      
      public function unlockOneByType(type0:String, firstPanB0:Boolean = false) : TaskData
      {
         var before_overB:Boolean = false;
         var n:* = undefined;
         var d0:TaskDefine = null;
         var da0:TaskData = null;
         var bb0:Boolean = false;
         var arr0:Array = this.getDefineArrByType(type0);
         if(arr0 is Array)
         {
            if(firstPanB0)
            {
               if(this.getDataArrByType(type0).length == 0)
               {
                  return null;
               }
            }
            before_overB = true;
            for(n in arr0)
            {
               d0 = arr0[n];
               da0 = this.getTaskDataByName(d0.name);
               if(!da0)
               {
                  bb0 = true;
                  if(TaskType.autoUnlockArr.indexOf(d0.father) >= 0)
                  {
                     if(this.playerData.level < d0.unlockLv)
                     {
                        bb0 = false;
                     }
                  }
                  if(before_overB && bb0)
                  {
                     if(d0.worldMapId != "")
                     {
                        if(d0.isUnlockMapB())
                        {
                           this.playerData.worldMap.saveGroup.unlockOne(d0.worldMapId);
                        }
                     }
                     return this.addTask(d0.name);
                  }
               }
               else
               {
                  before_overB = da0.state == TaskState.over;
               }
            }
         }
         return null;
      }
      
      public function unlockAllByType(type0:String) : void
      {
         var n:* = undefined;
         var d0:TaskDefine = null;
         var da0:TaskData = null;
         var arr0:Array = this.getDefineArrByType(type0);
         if(arr0 is Array)
         {
            for(n in arr0)
            {
               d0 = arr0[n];
               da0 = this.getTaskDataByName(d0.name);
               if(!da0)
               {
                  this.addTask(d0.name);
               }
            }
         }
      }
      
      public function getTaskDataByName(name0:String) : TaskData
      {
         return this.dataObj[name0];
      }
      
      public function getTaskDataByWorldMap(worldMapId0:String, type0:String = "", ingB0:Boolean = false) : TaskData
      {
         var i:* = undefined;
         var da0:TaskData = null;
         var bb0:Boolean = false;
         for(i in this.dataObj)
         {
            da0 = this.dataObj[i];
            if(da0.rolePan())
            {
               bb0 = type0 == "" ? true : da0.def.father == type0;
               if(ingB0 && da0.state != TaskState.ing && da0.state != TaskState.complete)
               {
                  bb0 = false;
               }
               if(da0.getWorldMapId() == worldMapId0 && bb0)
               {
                  return da0;
               }
            }
         }
         return null;
      }
      
      public function getIngTaskDataByWorldMap(worldMapId0:String, type0:String = "") : TaskData
      {
         var da0:TaskData = null;
         var bb0:Boolean = false;
         var arr_len0:int = int(this.ingArr.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            da0 = this.ingArr[i];
            bb0 = type0 == "" ? true : da0.def.father == type0;
            if(da0.getWorldMapId() == worldMapId0 && bb0)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function getIngTaskDataByType(type0:String) : TaskData
      {
         var da0:TaskData = null;
         var arr_len0:int = int(this.ingArr.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            da0 = this.ingArr[i];
            if(da0.def.father == type0)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function getIngOrCompleteTaskDataByType(type0:String) : TaskData
      {
         var da0:TaskData = null;
         var darr0:Array = this.getDataArrByType(type0);
         for each(da0 in darr0)
         {
            if(da0.state == TaskState.ing || da0.state == TaskState.complete)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function getUITaskArr(type0:String) : Array
      {
         var n:* = undefined;
         var d0:TaskDefine = null;
         var da0:TaskData = null;
         var arr0:Array = this.getDefineArrByType(type0);
         var arr2:Array = [];
         if(arr0 is Array)
         {
            for(n in arr0)
            {
               d0 = arr0[n];
               da0 = this.getTaskDataByName(d0.name);
               if(!da0)
               {
                  da0 = new TaskData();
                  da0.inData_byDefine(d0,this.playerData);
                  da0.save.state = TaskState.lock;
               }
               if(!this.saveGroup.onlyCompleteB || da0.state != TaskState.over)
               {
                  arr2.push(da0);
               }
            }
         }
         return arr2;
      }
      
      public function getTaskBoxTaskDataArr() : Array
      {
         var da0:TaskData = null;
         var arr0:Array = [];
         for each(da0 in this.dataObj)
         {
            if(da0.rolePan())
            {
               if(da0.state == TaskState.ing || da0.state == TaskState.complete || da0.state == TaskState.fail)
               {
                  arr0.push(da0);
               }
            }
         }
         return arr0;
      }
      
      public function getCompleteTaskNum() : int
      {
         var i:* = undefined;
         var da0:TaskData = null;
         var num0:int = 0;
         for(i in this.dataObj)
         {
            da0 = this.dataObj[i];
            if(da0.rolePan())
            {
               if(da0.def.father != TaskType.LEVEL && da0.state == TaskState.complete)
               {
                  num0++;
               }
            }
         }
         return num0;
      }
      
      public function getOverTaskNum(type0:String) : int
      {
         var da0:TaskData = null;
         var num0:int = 0;
         for each(da0 in this.dataObj)
         {
            if(da0.rolePan())
            {
               if(type0 == "" || da0.def.father == type0)
               {
                  if(da0.state == TaskState.over)
                  {
                     num0++;
                  }
               }
            }
         }
         return num0;
      }
      
      public function getNoRoleTaskIng(role0:String) : TaskData
      {
         var da0:TaskData = null;
         for each(da0 in this.dataObj)
         {
            if(da0.isIngOrComplete())
            {
               if(da0.def.rolePan(role0) == false)
               {
                  return da0;
               }
            }
         }
         return null;
      }
      
      public function getCanDayNum(type0:String) : int
      {
         var unlockNum0:int = 0;
         var fd0:TaskFatherDefine = Gaming.defineGroup.task.getFatherDefine(type0);
         if(fd0.dayNum == -1)
         {
            return -1;
         }
         unlockNum0 = this.getUnlockNum(type0);
         if(unlockNum0 < fd0.dayNum)
         {
            if(unlockNum0 == 0)
            {
               unlockNum0 = 1;
            }
            return unlockNum0;
         }
         return fd0.dayNum;
      }
      
      public function getUnlockNum(type0:String) : int
      {
         return this.getDataArrByType(type0).length;
      }
      
      public function getTodayNumText(type0:String) : String
      {
         var fd0:TaskFatherDefine = null;
         var num0:int = this.getTodayNum(type0);
         if(num0 == -1)
         {
            if(type0 == TaskType.MAIN)
            {
               return ComMethod.color("> 陆续完成主线任务才能开启新地图","#FFCC00");
            }
            return "";
         }
         fd0 = Gaming.defineGroup.task.getFatherDefine(type0);
         return (type0 == "week" ? "本周" : "今日") + "还可完成" + fd0.cnName + "任务 " + ComMethod.color(num0 + "",num0 == 0 ? "#FF0000" : "#FFFF00") + " 次";
      }
      
      public function getTodayAddBtnVisible(type0:String) : Boolean
      {
         var fd0:TaskFatherDefine = Gaming.defineGroup.task.getFatherDefine(type0);
         return fd0.buyNum > 0;
      }
      
      public function getTodayNum(type0:String) : int
      {
         var nowNum0:int = 0;
         var dayNum0:int = this.getCanDayNum(type0);
         if(dayNum0 == -1)
         {
            return -1;
         }
         nowNum0 = this.saveGroup.getTodayCompleteNum(type0);
         return dayNum0 - nowNum0;
      }
      
      public function addTodayNum(type0:String, num0:int) : void
      {
         var fd0:TaskFatherDefine = Gaming.defineGroup.task.getFatherDefine(type0);
         if(fd0.dayNum > 0)
         {
            this.saveGroup.addTodayCompleteNum(type0,num0);
         }
      }
      
      public function getBuyNum(type0:String) : int
      {
         var nowNum0:int = 0;
         var fd0:TaskFatherDefine = Gaming.defineGroup.task.getFatherDefine(type0);
         if(fd0.buyNum == -1)
         {
            return -1;
         }
         nowNum0 = this.saveGroup.getTodayBuyNum(type0);
         return fd0.buyNum - nowNum0;
      }
      
      public function addBuyNum(type0:String, num0:int) : void
      {
         var arr0:Array = null;
         var n:* = undefined;
         var da0:TaskData = null;
         var fd0:TaskFatherDefine = Gaming.defineGroup.task.getFatherDefine(type0);
         if(fd0.buyNum > 0)
         {
            this.saveGroup.addTodayBuyNum(type0,num0);
         }
         this.addTodayNum(type0,-num0);
         if(fd0.clearCompleteAfterBuyNumB)
         {
            arr0 = this.getDataArrByType(type0);
            for(n in arr0)
            {
               da0 = arr0[n];
               da0.clearOverState();
            }
         }
      }
      
      private function dealConditionCumulative() : void
      {
         var da0:TaskData = null;
         for each(da0 in this.dataObj)
         {
            if(da0.rolePan())
            {
               da0.dealConditionCumulative();
            }
         }
      }
      
      private function getArrTaskNoOverNum(typeArr0:Array) : int
      {
         var da0:TaskData = null;
         var num0:int = 0;
         for each(da0 in this.dataObj)
         {
            if(da0.rolePan())
            {
               if(typeArr0.indexOf(da0.def.father) >= 0)
               {
                  if(da0.isUiShowTipB())
                  {
                     num0++;
                  }
               }
            }
         }
         return num0;
      }
      
      public function getTaskNoOverNum(type0:String) : int
      {
         var da0:TaskData = null;
         var num0:int = 0;
         var arr0:Array = this.getDataArrByType(type0);
         for each(da0 in arr0)
         {
            if(da0.isUiShowTipB())
            {
               num0++;
            }
         }
         return num0;
      }
      
      public function isCompleteB(name0:String) : Boolean
      {
         var da0:TaskData = this.getTaskDataByName(name0);
         if(Boolean(da0))
         {
            return da0.isCompleteOrOver();
         }
         return false;
      }
      
      public function isOverB(name0:String) : Boolean
      {
         var da0:TaskData = this.getTaskDataByName(name0);
         if(Boolean(da0))
         {
            return da0.isOver();
         }
         return false;
      }
      
      public function isRoleCompleteB(name0:String) : Boolean
      {
         var da0:TaskData = this.getTaskDataByName(name0);
         if(Boolean(da0))
         {
            if(da0.rolePan())
            {
               return da0.isCompleteOrOver();
            }
         }
         return false;
      }
      
      public function upWeekMul() : Number
      {
         var index0:int = this.saveGroup.weekMulIndex;
         index0 = TaskType.upWeekExtraDropIndex(index0);
         this.saveGroup.weekMulIndex = index0;
         return TaskType.getWeekMulByIndex(index0);
      }
      
      public function getWeekMul() : Number
      {
         return TaskType.getWeekMulByIndex(this.saveGroup.weekMulIndex);
      }
      
      public function getWeekKeyMust() : Number
      {
         return TaskType.getWeekKeyMustByIndex(this.saveGroup.weekMulIndex);
      }
      
      public function startLevel() : void
      {
         this.dealConditionCumulative();
         Gaming.TG.task.dataChange();
      }
      
      public function overGamingClear() : void
      {
         this.delAllTaskByFather("level");
         this.dealConditionCumulative();
      }
      
      public function getMainTaskMaxLevel() : int
      {
         var d0:TaskDefine = null;
         var da0:TaskData = null;
         var arr0:Array = this.getDefineArrByType(TaskType.MAIN);
         for(var i:int = arr0.length - 1; i >= 0; i--)
         {
            d0 = arr0[i];
            da0 = this.getTaskDataByName(d0.name);
            if(Boolean(da0))
            {
               if(da0.isCompleteOrOver())
               {
                  return d0.lv;
               }
            }
         }
         return 0;
      }
      
      private function delAllTaskByFather(father0:String) : void
      {
         this.setDelTaskByFather(father0);
         this.fleshDataAndSaveGroup();
         this.fleshIngArr();
      }
      
      private function setDelTaskByFather(father0:String) : void
      {
         var n:* = undefined;
         var da0:TaskData = null;
         for(n in this.dataObj)
         {
            da0 = this.dataObj[n];
            if(da0.def.father == father0)
            {
               da0.die = 1;
            }
         }
      }
      
      public function dayInitByFather(father0:String) : void
      {
         var da0:TaskData = null;
         for each(da0 in this.dataObj)
         {
            if(da0.def.father == father0)
            {
               da0.dayInit();
            }
         }
      }
      
      private function toOverIfCompleteFather(father0:String) : void
      {
         var da0:TaskData = null;
         for each(da0 in this.dataObj)
         {
            if(da0.def.father == father0)
            {
               da0.save.toOverIfComplete();
            }
         }
      }
      
      public function delMainMore(taskName0:String) : int
      {
         var da0:TaskData = null;
         var num0:int = 0;
         var mainArr0:Array = Gaming.defineGroup.task.getArrByTypeCut(TaskType.MAIN,taskName0);
         for each(da0 in this.dataObj)
         {
            if(da0.def.father == TaskType.MAIN)
            {
               if(mainArr0.indexOf(da0.def) == -1)
               {
                  da0.die = 1;
                  num0++;
               }
            }
         }
         this.fleshDataAndSaveGroup();
         this.fleshIngArr();
         return num0;
      }
      
      public function getLeftSmallIcon() : String
      {
         var giftNum0:int = this.getCompleteTaskNum();
         var noNum0:int = this.getArrTaskNoOverNum(TaskType.showNewArr);
         if(giftNum0 > 0)
         {
            return "getGift";
         }
         if(noNum0 > 0)
         {
            return "new";
         }
         return "";
      }
      
      public function getTaskIsGetGiftB(name0:String) : Boolean
      {
         var da0:TaskData = this.getTaskDataByName(name0);
         if(da0 is TaskData)
         {
            if(da0.state == TaskState.over)
            {
               return true;
            }
         }
         return false;
      }
   }
}

