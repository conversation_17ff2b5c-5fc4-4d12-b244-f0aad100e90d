package dataAll._player.more.creator
{
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.gift.define.GiftAddDefine;
   
   public class MoreAddData
   {
      public var name:String = "";
      
      public var lv:int = 0;
      
      public var firstSkillArr:Array = [];
      
      public var bodyDef:NormalBodyDefine;
      
      public function MoreAddData(d0:GiftAddDefine = null)
      {
         super();
         if(d0 is GiftAddDefine)
         {
            this.inData_byGiftAddDefine(d0);
         }
      }
      
      public function inData_byGiftAddDefine(d0:GiftAddDefine) : void
      {
         this.name = d0.name;
         if(d0.lv != "")
         {
            this.lv = int(d0.lv);
         }
         this.bodyDef = Gaming.defineGroup.body.getDefine(this.name);
      }
      
      public function getCnName() : String
      {
         return this.bodyDef.cnName;
      }
      
      public function getGatherTip() : String
      {
         return this.bodyDef.description;
      }
      
      public function getLv() : int
      {
         if(this.lv == 0)
         {
            return this.bodyDef.moreD.firstLv;
         }
         return this.lv;
      }
      
      public function getFirstSkillMustLv() : int
      {
         return this.bodyDef.moreD.studySkillLvAdd;
      }
   }
}

