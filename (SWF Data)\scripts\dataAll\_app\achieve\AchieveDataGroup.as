package dataAll._app.achieve
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ObjectMethod;
   import dataAll._app.achieve.define.AchieveDefine;
   import dataAll._app.achieve.define.AchieveFatherDefine;
   import dataAll._player.PlayerData;
   import dataAll.equip.add.EquipAddChild;
   import dataAll.equip.add.IO_EquipAddGetter;
   import dataAll.equip.add.IO_EquipAddTipGetter;
   
   public class AchieveDataGroup implements IO_EquipAddTipGetter, IO_EquipAddGetter
   {
      public var playerData:PlayerData;
      
      public var saveGroup:AchieveSaveGroup;
      
      private var obj:Object = {};
      
      private var triggerObj:Object = {};
      
      private var wearArr:Array = [];
      
      private var allNum:int = 0;
      
      private var allCompleteNum:int = 0;
      
      public var mergeObj:Object = {};
      
      private var tempMaxObj:Object = null;
      
      public function AchieveDataGroup()
      {
         super();
      }
      
      public function getProAddObj() : Object
      {
         return this.mergeObj;
      }
      
      public function getProAddTipObj() : Object
      {
         var da0:AchieveData = null;
         var proObj0:Object = null;
         var obj0:Object = {};
         for each(da0 in this.wearArr)
         {
            proObj0 = da0.getProObj();
            EquipAddChild.addTipInObj(proObj0,obj0,da0.def.getEquipTipCn());
         }
         return obj0;
      }
      
      public function getProAddMaxObj(pro0:String) : Object
      {
         var da0:AchieveData = null;
         var proObj0:Object = null;
         if(this.tempMaxObj == null)
         {
            this.tempMaxObj = {};
            for each(da0 in this.obj)
            {
               proObj0 = da0.getProObj();
               EquipAddChild.addMaxInObj(proObj0,this.tempMaxObj,da0.def.getEquipTipCn(),da0.def.name);
            }
         }
         return ObjectMethod.getEleIfHave(this.tempMaxObj,pro0,0);
      }
      
      public function setPlayerData(pd0:PlayerData) : void
      {
         this.playerData = pd0;
      }
      
      public function inData_bySaveGroup(sg0:AchieveSaveGroup) : void
      {
         var d0:AchieveDefine = null;
         var da0:AchieveData = null;
         this.saveGroup = sg0;
         this.obj = {};
         var allObj0:Object = Gaming.defineGroup.achieve.obj;
         for each(d0 in allObj0)
         {
            da0 = new AchieveData();
            if(this.saveGroup.obj.hasOwnProperty(d0.name))
            {
               da0.inData_bySave(this.saveGroup.obj[d0.name],this);
            }
            else
            {
               da0.inData_byDefine(d0,this);
            }
            this.obj[da0.name] = da0;
         }
         this.fleshTriggerObj();
         this.fleshWearArr();
         this.fleshMergeData();
      }
      
      public function fleshAffterComplete() : void
      {
         this.fleshSaveGroup();
         this.fleshTriggerObj();
      }
      
      public function fleshSaveGroup() : void
      {
         var da0:AchieveData = null;
         var s0:AchieveSave = null;
         var obj2:Object = {};
         for each(da0 in this.obj)
         {
            s0 = da0.getSave();
            if(s0 is AchieveSave)
            {
               obj2[s0.name] = s0;
            }
         }
         this.saveGroup.obj = obj2;
      }
      
      private function fleshTriggerObj() : void
      {
         var da0:AchieveData = null;
         var trigger0:String = null;
         this.triggerObj = {};
         for each(da0 in this.obj)
         {
            if(!da0.isCompleteB())
            {
               trigger0 = da0.def.condition.trigger;
               if(!this.triggerObj.hasOwnProperty(trigger0))
               {
                  this.triggerObj[trigger0] = [];
               }
               this.triggerObj[trigger0].push(da0);
            }
         }
      }
      
      public function getDataByName(name0:String) : AchieveData
      {
         return this.obj[name0];
      }
      
      public function isCompleteB(name0:String) : Boolean
      {
         var da0:AchieveData = this.getDataByName(name0);
         if(da0 is AchieveData)
         {
            return da0.isCompleteB();
         }
         return false;
      }
      
      public function getArrByFather(father0:String, onlyPanB0:Boolean = false) : Array
      {
         var d0:AchieveDefine = null;
         var da0:AchieveData = null;
         var bb0:Boolean = false;
         if(!this.saveGroup.onlyNoCompleteB)
         {
            onlyPanB0 = false;
         }
         var arr0:Array = Gaming.defineGroup.achieve.getArrByFather(father0);
         var dataArr0:Array = [];
         if(arr0 is Array)
         {
            for each(d0 in arr0)
            {
               da0 = this.obj[d0.name];
               bb0 = true;
               if(onlyPanB0)
               {
                  bb0 = !da0.isCompleteB();
               }
               if(bb0)
               {
                  dataArr0.push(da0);
               }
            }
         }
         return dataArr0;
      }
      
      public function getArrByFatherNoPan(father0:String, onlyPanB0:Boolean = false) : Array
      {
         var d0:AchieveDefine = null;
         var da0:AchieveData = null;
         var arr0:Array = Gaming.defineGroup.achieve.getArrByFather(father0);
         var dataArr0:Array = [];
         if(arr0 is Array)
         {
            for each(d0 in arr0)
            {
               da0 = this.obj[d0.name];
               dataArr0.push(da0);
            }
         }
         return dataArr0;
      }
      
      public function getLabelShowData() : Object
      {
         var n:* = undefined;
         var gatherName0:String = null;
         var childArr0:Array = null;
         var gNum0:int = 0;
         var gCompleteNum0:int = 0;
         var gNewB0:Boolean = false;
         var i:* = undefined;
         var gatherShowDa0:AchieveLabelShowData = null;
         var fatherName0:String = null;
         var defineArr0:Array = null;
         var num0:int = 0;
         var completeNum0:int = 0;
         var newB0:Boolean = false;
         var d0:AchieveDefine = null;
         var showDa0:AchieveLabelShowData = null;
         var da0:AchieveData = null;
         this.allNum = 0;
         this.allCompleteNum = 0;
         var obj0:Object = {};
         var gatherArr0:Array = Gaming.defineGroup.achieve.gatherNameArr;
         var fatherArr0:Array = Gaming.defineGroup.achieve.fatherNameArr;
         for(n in gatherArr0)
         {
            gatherName0 = gatherArr0[n];
            childArr0 = fatherArr0[n];
            gNum0 = 0;
            gCompleteNum0 = 0;
            gNewB0 = false;
            for(i in childArr0)
            {
               fatherName0 = childArr0[i];
               defineArr0 = Gaming.defineGroup.achieve.getArrByFather(fatherName0);
               num0 = int(defineArr0.length);
               completeNum0 = 0;
               newB0 = false;
               for each(d0 in defineArr0)
               {
                  da0 = this.obj[d0.name];
                  if(da0.isCompleteB())
                  {
                     completeNum0++;
                     if(da0.newB)
                     {
                        newB0 = true;
                        gNewB0 = true;
                     }
                  }
               }
               showDa0 = new AchieveLabelShowData(num0,completeNum0);
               showDa0.inFatherName(fatherName0);
               obj0[fatherName0] = showDa0;
               showDa0.newB = newB0;
               gNum0 += num0;
               gCompleteNum0 += completeNum0;
            }
            this.allNum += gNum0;
            this.allCompleteNum += gCompleteNum0;
            gatherShowDa0 = new AchieveLabelShowData(gNum0,gCompleteNum0);
            obj0[gatherName0] = gatherShowDa0;
            gatherShowDa0.newB = gNewB0;
         }
         return obj0;
      }
      
      public function getMedelLabelArr() : Array
      {
         var n:* = undefined;
         var gatherName0:String = null;
         var gatherD0:AchieveFatherDefine = null;
         var childArr0:Array = null;
         var completeArr0:Array = null;
         var gNum0:int = 0;
         var gCompleteNum0:int = 0;
         var i:* = undefined;
         var gatherShowDa0:AchieveLabelShowData = null;
         var fatherName0:String = null;
         var defineArr0:Array = null;
         var num0:int = 0;
         var completeNum0:int = 0;
         var arrlen0:int = 0;
         var maxDa0:AchieveData = null;
         var j:int = 0;
         var d0:AchieveDefine = null;
         var da0:AchieveData = null;
         this.allNum = 0;
         this.allCompleteNum = 0;
         var arr0:Array = [];
         var gatherArr0:Array = Gaming.defineGroup.achieve.gatherNameArr;
         var fatherArr0:Array = Gaming.defineGroup.achieve.fatherNameArr;
         for(n in gatherArr0)
         {
            gatherName0 = gatherArr0[n];
            gatherD0 = Gaming.defineGroup.achieve.getFatherDefine(gatherName0);
            childArr0 = fatherArr0[n];
            completeArr0 = [];
            gNum0 = 0;
            gCompleteNum0 = 0;
            for(i in childArr0)
            {
               fatherName0 = childArr0[i];
               defineArr0 = Gaming.defineGroup.achieve.getArrByFather(fatherName0);
               num0 = int(defineArr0.length);
               completeNum0 = 0;
               arrlen0 = int(defineArr0.length);
               maxDa0 = null;
               for(j = arrlen0 - 1; j >= 0; j--)
               {
                  d0 = defineArr0[j];
                  da0 = this.obj[d0.name];
                  if(d0.haveMedelProB())
                  {
                     if(da0.isCompleteB())
                     {
                        if(!maxDa0)
                        {
                           maxDa0 = da0;
                           completeArr0.push(da0);
                        }
                        else if(da0.isWearB())
                        {
                           completeArr0.push(da0);
                        }
                     }
                  }
                  if(da0.isCompleteB())
                  {
                     completeNum0++;
                  }
               }
               gNum0 += num0;
               gCompleteNum0 += completeNum0;
            }
            this.allNum += gNum0;
            this.allCompleteNum += gCompleteNum0;
            gatherShowDa0 = new AchieveLabelShowData(gNum0,gCompleteNum0);
            gatherShowDa0.inMedelData(gatherD0,completeArr0);
            arr0.push(gatherShowDa0);
         }
         return arr0;
      }
      
      public function getArrByTrigger(trigger0:String) : Array
      {
         return this.triggerObj[trigger0];
      }
      
      public function getAllNumText() : String
      {
         var str0:String = this.allCompleteNum + "/" + this.allNum;
         if(this.allCompleteNum == this.allNum)
         {
            str0 = ComMethod.color(str0,"#00FF00");
         }
         return str0;
      }
      
      public function getCompleteNum() : Number
      {
         var da0:AchieveData = null;
         var num0:int = 0;
         for each(da0 in this.obj)
         {
            if(da0.isCompleteB())
            {
               num0++;
            }
         }
         return num0;
      }
      
      public function getCanWearNum() : int
      {
         var v0:int = int(this.getCompleteNum() / 10) + 1;
         if(v0 > this.getCanWearMax())
         {
            v0 = this.getCanWearMax();
         }
         return v0;
      }
      
      public function getCanWearMax() : int
      {
         return 28;
      }
      
      public function clickData(da0:AchieveData) : String
      {
         if(!da0.isCompleteB())
         {
            return "该勋章未获得，无法佩戴。";
         }
         var str0:String = "";
         if(da0.isWearB())
         {
            str0 = this.unloadData(da0);
         }
         else
         {
            str0 = this.wearData(da0);
         }
         if(str0 == "")
         {
            this.affterChangeData();
         }
         return str0;
      }
      
      private function unloadData(da0:AchieveData) : String
      {
         var index0:int = int(this.wearArr.indexOf(da0));
         if(index0 == -1)
         {
            return "该勋章未佩戴，无法脱下。";
         }
         da0.setWearB(false,0);
         this.wearArr.splice(index0,1);
         return "";
      }
      
      private function wearData(da0:AchieveData) : String
      {
         var wearIndex0:int = 0;
         var sameDa0:AchieveData = this.findSameProBy(da0);
         if(!(sameDa0 is AchieveData))
         {
            if(this.wearArr.length >= this.getCanWearNum())
            {
               sameDa0 = this.wearArr[0];
            }
         }
         if(sameDa0 is AchieveData)
         {
            wearIndex0 = sameDa0.getSave().wearIndex;
            this.wearArr[this.wearArr.indexOf(sameDa0)] = da0;
            sameDa0.setWearB(false,0);
         }
         else
         {
            if(this.wearArr.length > 0)
            {
               wearIndex0 = this.wearArr[this.wearArr.length - 1].getSave().wearIndex + 1;
            }
            this.wearArr.push(da0);
         }
         da0.setWearB(true,wearIndex0);
         return "";
      }
      
      private function findSameProBy(da0:AchieveData) : AchieveData
      {
         var da2:AchieveData = null;
         for each(da2 in this.wearArr)
         {
            if(da0.def.sameProPan(da2.def.medelProArr))
            {
               return da2;
            }
         }
         return null;
      }
      
      private function fleshWearArr() : void
      {
         var da0:AchieveData = null;
         var arr0:Array = [];
         for each(da0 in this.obj)
         {
            if(da0.isWearB())
            {
               arr0.push(da0);
            }
         }
         arr0.sort(this.fleshWearArr_sortFun);
         this.wearArr = arr0;
      }
      
      private function fleshWearArr_sortFun(da1:AchieveData, da2:AchieveData) : int
      {
         var s1:Number = da1.getSave().wearIndex;
         var s2:Number = da2.getSave().wearIndex;
         if(s1 < s2)
         {
            return -1;
         }
         if(s1 > s2)
         {
            return 1;
         }
         return 0;
      }
      
      public function getWearArr() : Array
      {
         return this.wearArr;
      }
      
      public function dealMoreWear(canNum0:int) : void
      {
         var s0:int = 0;
         var i:int = 0;
         var da0:AchieveData = null;
         var len0:int = int(this.wearArr.length);
         if(canNum0 < len0)
         {
            s0 = canNum0 - 1;
            for(i = s0; i < len0; i++)
            {
               da0 = this.wearArr[i];
               da0.setWearB(false,0);
            }
            this.fleshWearArr();
         }
      }
      
      public function getWearProNameArr() : Array
      {
         var da2:AchieveData = null;
         var obj0:Object = null;
         var n:* = undefined;
         var nameArr0:Array = [];
         for each(da2 in this.wearArr)
         {
            obj0 = da2.getProObj();
            for(n in obj0)
            {
               if(nameArr0.indexOf(n) == -1)
               {
                  nameArr0.push(n);
               }
            }
         }
         return nameArr0;
      }
      
      private function fleshMergeData() : void
      {
         var da0:AchieveData = null;
         var b0:Object = null;
         var n:* = undefined;
         var obj0:Object = {};
         for each(da0 in this.wearArr)
         {
            b0 = da0.getProObj();
            for(n in b0)
            {
               if(!obj0.hasOwnProperty(n))
               {
                  obj0[n] = 0;
               }
               obj0[n] += b0[n];
            }
         }
         this.mergeObj = obj0;
      }
      
      private function affterChangeData() : void
      {
         this.fleshMergeData();
         this.playerData.fleshAllByEquip();
      }
      
      public function completeAll() : void
      {
         var da0:AchieveData = null;
         for each(da0 in this.obj)
         {
            da0.complete();
         }
         this.fleshAffterComplete();
      }
   }
}

