package dataAll._app.vip.define
{
   import com.common.text.TextWay;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.Sounto64;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll.arms.skin.ArmsSkinDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.things.define.ThingsDefine;
   import dataAll.ui.text.ProTipType;
   
   public class VipLevelDefine
   {
      public static var pro_arr:Array = [];
      
      public static var buyNameArr:Array = [];
      
      private static var propsNameArr:Array = [];
      
      private var V:Number = Math.random() / 5 + 0.01;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var gift:GiftAddDefineGroup = new GiftAddDefineGroup();
      
      private var _index:String = "";
      
      private var _must:String = "";
      
      private var _expMul:Number = 0;
      
      private var _dpsMul:Number = 0;
      
      private var _lifeMul:Number = 0;
      
      private var _defenceMul:Number = 0;
      
      private var _bag:String = "";
      
      private var _lotteryNum:String = "";
      
      private var _askPropsNum:String = "";
      
      public var propsObj:Object = {};
      
      private var buyLimitNumObj:Object = {};
      
      public var dayGift:GiftAddDefineGroup = new GiftAddDefineGroup();
      
      private var armsSkinArr:Array = null;
      
      public function VipLevelDefine()
      {
         super();
         this.index = -1;
         this.must = 0;
         this.expMul = 0;
         this.dpsMul = 0;
         this.lifeMul = 0;
         this.defenceMul = 0;
         this.bag = 0;
         this.lotteryNum = 0;
         this.askPropsNum = 0;
         this.smeltNum = 0;
      }
      
      public function set index(v0:Number) : void
      {
         this._index = Sounto64.encode(String(v0));
      }
      
      public function get index() : Number
      {
         return Number(Sounto64.decode(this._index));
      }
      
      public function set must(v0:Number) : void
      {
         this._must = Sounto64.encode(String(v0));
      }
      
      public function get must() : Number
      {
         return Number(Sounto64.decode(this._must));
      }
      
      public function set expMul(v0:Number) : void
      {
         this._expMul = v0 / this.V;
      }
      
      public function get expMul() : Number
      {
         return this._expMul * this.V;
      }
      
      public function set dpsMul(v0:Number) : void
      {
         this._dpsMul = v0 / this.V;
      }
      
      public function get dpsMul() : Number
      {
         return this._dpsMul * this.V;
      }
      
      public function set lifeMul(v0:Number) : void
      {
         this._lifeMul = v0 / this.V;
      }
      
      public function get lifeMul() : Number
      {
         return this._lifeMul * this.V;
      }
      
      public function set defenceMul(v0:Number) : void
      {
         this._defenceMul = v0 / this.V;
      }
      
      public function get defenceMul() : Number
      {
         return this._defenceMul * this.V;
      }
      
      public function set bag(v0:Number) : void
      {
         this._bag = Sounto64.encode(String(v0));
      }
      
      public function get bag() : Number
      {
         return Number(Sounto64.decode(this._bag));
      }
      
      public function set lotteryNum(v0:Number) : void
      {
         this._lotteryNum = Sounto64.encode(String(v0));
      }
      
      public function get lotteryNum() : Number
      {
         return Number(Sounto64.decode(this._lotteryNum));
      }
      
      public function set askPropsNum(v0:Number) : void
      {
         this._askPropsNum = Sounto64.encode(String(v0));
      }
      
      public function get askPropsNum() : Number
      {
         return Number(Sounto64.decode(this._askPropsNum));
      }
      
      public function get smeltNum() : Number
      {
         return this.CF.getAttribute("smeltNum");
      }
      
      public function set smeltNum(v0:Number) : void
      {
         this.CF.setAttribute("smeltNum",v0);
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var proXml0:XML = null;
         var pro0:String = null;
         var buyLimitNumXml0:XMLList = null;
         var bx0:XML = null;
         var buyStr0:String = null;
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         ClassProperty.inData_byXMLAt(this,xml0.base[0],pro_arr);
         ClassProperty.inData_byXML(this,xml0,pro_arr);
         this.gift.inData_byXML(xml0.gift);
         if(Boolean(xml0.dayGift[0]))
         {
            this.dayGift.inData_byXML(xml0.dayGift[0].gift);
         }
         var baseXml0:XMLList = xml0.props[0].attributes();
         for(n in baseXml0)
         {
            proXml0 = baseXml0[n];
            pro0 = String(proXml0.name());
            this.propsObj[pro0] = Sounto64.encode(String(int(String(proXml0))));
            if(propsNameArr.indexOf(pro0) == -1)
            {
               propsNameArr.push(pro0);
            }
         }
         if(xml0.buyLimitNumObj.length() > 0)
         {
            buyLimitNumXml0 = xml0.buyLimitNumObj[0].attributes();
            for each(bx0 in buyLimitNumXml0)
            {
               this.buyLimitNumObj[String(bx0.name())] = TextWay.toCode32(String(int(String(bx0))));
               buyStr0 = bx0.name();
               if(buyNameArr.indexOf(buyStr0) == -1)
               {
                  buyNameArr.push(buyStr0);
               }
            }
         }
      }
      
      public function propsGatherTip() : String
      {
         var n0:* = null;
         var v0:int = 0;
         var d0:ThingsDefine = null;
         var s0:String = ProTipType.getTitleMixed("战斗中道具使用次数增加");
         for each(n0 in propsNameArr)
         {
            v0 = this.getPropsValueByName(n0);
            if(v0 > 0)
            {
               d0 = Gaming.defineGroup.things.getDefine(n0);
               s0 += "\n" + d0.cnName + "|<b><green +" + v0 + "/></b>";
            }
         }
         return s0;
      }
      
      public function buyGatherTip() : String
      {
         var n0:* = null;
         var v0:int = 0;
         var d0:GoodsDefine = null;
         var s0:String = ProTipType.getTitleMixed("特殊商品每日限购增加");
         for each(n0 in buyNameArr)
         {
            v0 = this.getBuyLimitNum(n0);
            if(v0 > 0)
            {
               d0 = Gaming.defineGroup.goods.getDefine(n0);
               if(Boolean(d0))
               {
                  s0 += "\n" + d0.cnName + "|<b><green +" + v0 + "/></b>";
               }
            }
         }
         return s0;
      }
      
      public function getArmsSkinArr() : Array
      {
         var lv0:int = 0;
         var fatherArrObj0:Object = null;
         var n:* = undefined;
         var armsName0:String = null;
         var arr0:Array = null;
         var sd0:ArmsSkinDefine = null;
         var vip0:int = 0;
         var p0:int = 0;
         if(this.armsSkinArr == null)
         {
            lv0 = this.getTrueLevel();
            this.armsSkinArr = [];
            fatherArrObj0 = Gaming.defineGroup.armsCharger.skin.fatherArrObj;
            for(n in fatherArrObj0)
            {
               armsName0 = n as String;
               arr0 = fatherArrObj0[n];
               for each(sd0 in arr0)
               {
                  vip0 = sd0.vip;
                  p0 = sd0.p * 10000;
                  if(vip0 > 0 && vip0 <= lv0)
                  {
                     ArrayMethod.addNoRepeatInArr(this.armsSkinArr,armsName0);
                     break;
                  }
                  if(p0 > 0 && p0 <= this.must)
                  {
                     ArrayMethod.addNoRepeatInArr(this.armsSkinArr,armsName0);
                     break;
                  }
               }
            }
         }
         return this.armsSkinArr;
      }
      
      public function skinGatherTip() : String
      {
         var s0:String = null;
         var name0:* = null;
         var cn0:String = null;
         var ar0:Array = this.getArmsSkinArr();
         if(ar0.length > 0)
         {
            s0 = "以下武器拥有武器皮肤：";
            for each(name0 in ar0)
            {
               cn0 = Gaming.defineGroup.bullet.getArmsRangeDefine(name0).def.cnName;
               s0 += "\n<redness " + cn0 + "/>";
            }
            return s0 + "\n\n<graydark 操作方法：背包中点击武器图标，再点击“更换皮肤”。/>";
         }
         return "该VIP等级没有武器皮肤。";
      }
      
      public function getPropsValueByName(name0:String) : Number
      {
         if(this.propsObj.hasOwnProperty(name0))
         {
            return int(Sounto64.decode(this.propsObj[name0]));
         }
         if(this.buyLimitNumObj.hasOwnProperty(name0))
         {
            return this.getBuyLimitNum(name0);
         }
         return 0;
      }
      
      public function getValueByName(name0:String) : Number
      {
         if(this.hasOwnProperty(name0))
         {
            return this[name0];
         }
         return this.getPropsValueByName(name0);
      }
      
      public function getBuyLimitNum(name0:String) : int
      {
         if(this.buyLimitNumObj.hasOwnProperty(name0))
         {
            return int(TextWay.getText32(this.buyLimitNumObj[name0]));
         }
         return 0;
      }
      
      public function getLabelString() : String
      {
         if(this.index == -1)
         {
            return "无VIP";
         }
         return "VIP." + (this.index + 1);
      }
      
      public function getName2() : String
      {
         if(this.index == -1)
         {
            return "无VIP";
         }
         return "VIP- " + (this.index + 1);
      }
      
      public function getName() : String
      {
         return "VIP" + (this.index + 1);
      }
      
      public function getTrueLevel() : int
      {
         if(this.must == 0)
         {
            return 0;
         }
         return this.index + 1;
      }
      
      public function getValueStringByPro(name0:String) : String
      {
         if(name0 == "dayGift")
         {
            return "<b>√</b>";
         }
         var v0:Number = this.getValueByName(name0);
         if(name0.indexOf("Mul") > 0)
         {
            return "<b>" + TextWay.numberToPer(v0) + "</b>";
         }
         return "+<b>" + v0 + "</b>";
      }
   }
}

