package dataAll._app.edit._def
{
   import dataAll._base.NormalDefineGroup;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.pro.ProType;
   import gameAll.body.ai.one.PlayerAiData;
   
   public class EditProDefineGroup extends NormalDefineGroup
   {
      public function EditProDefineGroup()
      {
         super();
         defineClass = EditProDefine;
      }
      
      public function getBossDefine(name0:String) : EditProDefine
      {
         return getNormalDefineByGather(EditProGather.boss,name0) as EditProDefine;
      }
      
      public function getArmsDefine(name0:String) : EditProDefine
      {
         return getNormalDefineByGather(EditProGather.arms,name0) as EditProDefine;
      }
      
      public function afterDeal() : void
      {
         this.initProType(EditProGather.arms,new ArmsDefine());
         this.initProType(EditProGather.heroAI,new PlayerAiData());
      }
      
      private function initProType(gather0:String, obj0:Object) : void
      {
         var d0:EditProDefine = null;
         var v0:* = undefined;
         var type0:String = null;
         var darr0:Array = getArrByGather(gather0);
         for each(d0 in darr0)
         {
            v0 = d0.getObjValue(obj0);
            type0 = ProType.getType(v0);
            d0.type = type0;
         }
      }
      
      public function isHideFather(father0:String) : Boolean
      {
         if(father0.indexOf("hide") >= 0)
         {
            return true;
         }
         return false;
      }
   }
}

