package dataAll.pet.gene.creator
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.NumberMethod;
   import dataAll.equip.define.EquipColor;
   import dataAll.must.define.MustDefine;
   import dataAll.pet.PetData;
   import dataAll.pet.gene.GeneData;
   import dataAll.pet.gene.define.GeneDefine;
   import dataAll.pet.gene.define.GenePropertyArrayDefine;
   import dataAll.pet.gene.define.GenePropertyArrayDefineGroup;
   import dataAll.pet.gene.save.GeneSave;
   
   public class GeneDataGrowthCtrl
   {
      public function GeneDataGrowthCtrl()
      {
         super();
      }
      
      public static function getAllLimit(geneD0:GeneDefine, color0:String) : Number
      {
         var v0:Number = 0;
         if(geneD0.isLastB())
         {
            v0 = NumberMethod.toFixed(2.8,2);
            if(geneD0.superB && EquipColor.moreColorPan(color0,EquipColor.RED))
            {
               v0 += 0.3;
            }
         }
         else
         {
            v0 = NumberMethod.toFixed(2.4,2);
         }
         return v0;
      }
      
      public static function getAllProValue(obj0:Object) : Number
      {
         var v0:Number = NaN;
         var all0:Number = 0;
         for each(v0 in obj0)
         {
            all0 += v0;
         }
         return NumberMethod.toFixed(all0,2);
      }
      
      public static function growSave(s0:GeneSave) : Object
      {
         var one0:Object = null;
         var name0:String = null;
         var successRate0:Number = NaN;
         var value0:Number = NaN;
         var obj0:Object = s0.growObj;
         one0 = getGrowProOne(s0);
         var successB0:Boolean = false;
         if(Boolean(one0))
         {
            name0 = one0["name"];
            successRate0 = Number(one0["successRate"]);
            value0 = Number(one0["value"]);
            if(Math.random() <= successRate0)
            {
               if(!obj0.hasOwnProperty(name0))
               {
                  obj0[name0] = 0;
               }
               obj0[name0] += value0;
               obj0[name0] = Number(Number(obj0[name0]).toFixed(2));
               successB0 = true;
            }
         }
         s0.growObj = obj0;
         one0.successB = successB0;
         return one0;
      }
      
      public static function getGrowProOne(s0:GeneSave) : Object
      {
         var growObj0:Object = s0.growObj;
         var valueObj0:Object = s0.obj;
         var d0:GeneDefine = s0.getDefine();
         return getGrowPro(growObj0,valueObj0,d0);
      }
      
      private static function getGrowPro(growObj0:Object, valueObj0:Object, geneD0:GeneDefine) : Object
      {
         var d0:GenePropertyArrayDefine = null;
         var name0:String = null;
         var canB0:Boolean = false;
         var v0:Number = NaN;
         var limit0:Number = NaN;
         var nowD0:GenePropertyArrayDefine = null;
         var successRate0:Number = NaN;
         var value0:Number = NaN;
         var nowValue0:Number = NaN;
         var nowGrowLimit0:Number = NaN;
         var one0:Object = {};
         var canDefineArr0:Array = [];
         var proG0:GenePropertyArrayDefineGroup = Gaming.defineGroup.gene.pro;
         var dArr0:Array = proG0.propertyArr;
         var haveProNum0:int = 0;
         for each(d0 in dArr0)
         {
            name0 = d0.name;
            canB0 = true;
            if(valueObj0 is Object)
            {
               if(valueObj0[name0] == 0 || !valueObj0.hasOwnProperty(name0))
               {
                  canB0 = false;
               }
               else
               {
                  haveProNum0++;
               }
            }
            else
            {
               haveProNum0 = int(dArr0.length);
            }
            if(canB0)
            {
               if(growObj0.hasOwnProperty(name0))
               {
                  v0 = Number(growObj0[name0]);
                  limit0 = d0.getGrowLimit(geneD0);
                  if(v0 < limit0)
                  {
                     canDefineArr0.push(d0);
                  }
               }
               else
               {
                  canDefineArr0.push(d0);
               }
            }
         }
         if(canDefineArr0.length == 0)
         {
            return null;
         }
         nowD0 = canDefineArr0[int(Math.random() * canDefineArr0.length)];
         successRate0 = getSuccessRate(haveProNum0,canDefineArr0,geneD0);
         value0 = Math.random() * (nowD0.growMax - nowD0.growMin) + nowD0.growMin;
         nowValue0 = Number(growObj0[nowD0.name]);
         if(isNaN(nowValue0))
         {
            nowValue0 = 0;
         }
         nowGrowLimit0 = nowD0.getGrowLimit(geneD0);
         if(value0 + nowValue0 >= nowGrowLimit0)
         {
            value0 = nowGrowLimit0 - nowValue0;
         }
         value0 = Number(value0.toFixed(2));
         one0["name"] = nowD0.name;
         one0["successRate"] = successRate0;
         one0["value"] = value0;
         one0["tip"] = "属性：" + nowD0.cnName + "+" + TextWay.numberToPer(value0,0);
         return one0;
      }
      
      private static function getSuccessRate(haveProNum0:Number, canDefineArr0:Array, geneD0:GeneDefine) : Number
      {
         var v0:Number = 0.8 - (haveProNum0 - canDefineArr0.length) * 0.07;
         if(geneD0.isLastB())
         {
            v0 = 0.38;
         }
         return v0;
      }
      
      public static function getMust(da0:PetData) : MustDefine
      {
         var d0:MustDefine = new MustDefine();
         var thingsArr0:Array = [];
         var nowGrowth0:Number = da0.gene.getGrowAllPro();
         d0.lv = 50;
         d0.coin = Gaming.defineGroup.normal.getLevelCoinIncome(d0.lv);
         thingsArr0.push("bloodStone;" + int(8));
         var geneD0:GeneDefine = da0.gene.save.getDefine();
         if(geneD0.isLastB())
         {
            if(nowGrowth0 >= 2.4)
            {
               thingsArr0.push(geneD0.getBookThingsName() + ";8");
            }
         }
         d0.inThingsDataByArr(thingsArr0);
         return d0;
      }
      
      public static function getProStrArray(da0:GeneData) : Array
      {
         var name0:* = null;
         var d0:GenePropertyArrayDefine = null;
         var baseV0:Number = NaN;
         var nowV0:Number = NaN;
         var limitV0:Number = NaN;
         var color0:String = null;
         var geneD0:GeneDefine = da0.save.getDefine();
         var arr0:Array = ["","","",""];
         var obj0:Object = da0.save.obj;
         var growObj0:Object = da0.save.growObj;
         var proG0:GenePropertyArrayDefineGroup = Gaming.defineGroup.gene.pro;
         var nameArr0:Array = proG0.getNameArr();
         for each(name0 in nameArr0)
         {
            d0 = proG0.getDefine(name0) as GenePropertyArrayDefine;
            baseV0 = Number(obj0[name0]);
            if(isNaN(baseV0))
            {
               baseV0 = 0;
            }
            nowV0 = Number(growObj0[name0]);
            if(isNaN(nowV0))
            {
               nowV0 = 0;
            }
            limitV0 = d0.getGrowLimit(geneD0);
            arr0[0] += d0.cnName + "\n";
            arr0[1] += d0.getValueString(baseV0) + "\n";
            color0 = nowV0 > 0 ? (nowV0 >= limitV0 ? "#00FF00" : "#FFFF00") : "#999999";
            arr0[2] += ComMethod.color((nowV0 > 0 ? "+" : "") + TextWay.numberToPer(nowV0,0),color0) + "\n";
            arr0[3] += TextWay.numberToPer(limitV0,0) + "\n";
         }
         return arr0;
      }
      
      public static function getPetHead(da0:GeneData) : String
      {
         var allPro0:Number = getAllProValue(da0.save.growObj);
         if(allPro0 >= 3.2)
         {
            return ComMethod.color("·超凡尸宠·","#FF0000");
         }
         if(allPro0 >= 2.8)
         {
            return ComMethod.color("·绝世尸宠·","#FF0000");
         }
         if(allPro0 >= 2.4)
         {
            return ComMethod.color("·神化尸宠·","#FF0000");
         }
         if(allPro0 >= 2)
         {
            return ComMethod.color("·钻石尸宠·","#33CCFF");
         }
         if(allPro0 >= 1.5)
         {
            return ComMethod.color("·水晶尸宠·","#46FFFF");
         }
         if(allPro0 >= 0.9)
         {
            return ComMethod.color("·黄金尸宠·","#FFFF00");
         }
         if(allPro0 >= 0.5)
         {
            return ComMethod.color("·白银尸宠·","#CCCCCC");
         }
         if(allPro0 >= 0.1)
         {
            return ComMethod.color("·青铜尸宠·","#FF9900");
         }
         return "";
      }
      
      public static function test() : void
      {
         var v0:Number = NaN;
         var a0:Array = null;
         var n:* = undefined;
         var arr0:Array = [];
         for(var i:int = 0; i < 10; i++)
         {
            a0 = testOne();
            for(n in a0)
            {
               if(!arr0[n])
               {
                  arr0[n] = 0;
               }
               arr0[n] += a0[n];
            }
         }
         for each(v0 in arr0)
         {
            trace(int(v0 / 10));
         }
      }
      
      private static function testOne() : Array
      {
         var one0:Object = null;
         var name0:String = null;
         var successRate0:Number = NaN;
         var value0:Number = NaN;
         var allPro0:Number = NaN;
         var nowAllProValue0:Number = 0;
         var obj0:Object = {};
         var arr0:Array = [];
         var geneD0:GeneDefine = Gaming.defineGroup.gene.getDefine("PetBoomSkullS");
         for(var i:int = 0; i < 10000; i++)
         {
            one0 = getGrowPro(obj0,null,geneD0);
            if(!Boolean(one0))
            {
               break;
            }
            name0 = one0["name"];
            successRate0 = Number(one0["successRate"]);
            value0 = Number(one0["value"]);
            if(Math.random() <= successRate0)
            {
               if(!obj0.hasOwnProperty(name0))
               {
                  obj0[name0] = 0;
               }
               obj0[name0] += value0;
               allPro0 = getAllProValue(obj0);
               if(allPro0 >= nowAllProValue0)
               {
                  nowAllProValue0 += 0.1;
                  arr0.push(i);
               }
            }
         }
         return arr0;
      }
   }
}

