package dataAll.gift
{
   import dataAll._player.PlayerData;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.gift.level.GiftLevelGiftCreator;
   import dataAll.gift.level.LevelGiftData;
   import dataAll.gift.save.GiftSave;
   import dataAll.things.define.ThingsComposeDefine;
   
   public class GiftData
   {
      public var playerData:PlayerData;
      
      public var save:GiftSave = null;
      
      private var tempLevelArr:Array = null;
      
      public function GiftData()
      {
         super();
      }
      
      public function inData_bySave(s0:GiftSave) : void
      {
         this.save = s0;
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.save.newDayCtrl(timeStr0);
      }
      
      public function newWeek(timeStr0:String) : void
      {
         this.save.newWeek(timeStr0);
      }
      
      public function setNowReadTime(str0:String) : void
      {
         this.save.setNowReadTime(str0);
      }
      
      public function setLevelGiftGetB(name0:String, bb0:Boolean) : void
      {
         this.save.levelGiftObj[name0] = bb0;
      }
      
      public function getLevelGiftGetB(name0:String) : Boolean
      {
         return this.save.levelGiftObj[name0];
      }
      
      public function getLevelGiftArr() : Array
      {
         var gift0:GiftAddDefineGroup = null;
         var da0:LevelGiftData = null;
         var arr0:Array = this.tempLevelArr;
         var giftArr0:Array = Gaming.defineGroup.gift.getArrByFather("levelGift");
         if(arr0 == null)
         {
            arr0 = [];
            for each(gift0 in giftArr0)
            {
               da0 = new LevelGiftData();
               gift0 = GiftLevelGiftCreator.conver(gift0);
               da0.name = gift0.name;
               da0.define = gift0;
               arr0.push(da0);
            }
            this.tempLevelArr = arr0;
         }
         if(Boolean(arr0))
         {
            for each(da0 in arr0)
            {
               gift0 = da0.define;
               da0.getGiftB = this.getLevelGiftGetB(da0.name);
               da0.fleshMust(this.playerData);
            }
         }
         return arr0;
      }
      
      public function haveNewGiftGetB() : Boolean
      {
         var da0:LevelGiftData = null;
         var giftArr0:Array = this.getLevelGiftArr();
         for each(da0 in giftArr0)
         {
            if(da0.getGiftB == false && da0.openB)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getComposeWeekNum(name0:String) : Number
      {
         return this.save.cwObj.getAttribute(name0);
      }
      
      public function getComposeAllNum(name0:String) : Number
      {
         return this.save.caO.getAttribute(name0);
      }
      
      public function composeEvent(d0:ThingsComposeDefine, num0:int) : void
      {
         if(d0.week > 0)
         {
            this.save.cwObj.addAttribute(d0.name,num0);
         }
         if(d0.all > 0)
         {
            this.save.caO.addAttribute(d0.name,num0);
         }
      }
   }
}

