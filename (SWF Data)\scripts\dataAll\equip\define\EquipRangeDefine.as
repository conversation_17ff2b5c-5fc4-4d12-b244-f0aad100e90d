package dataAll.equip.define
{
   public class EquipRangeDefine
   {
      public var startLevel:int = 0;
      
      public var minLevel:int = 0;
      
      public var maxLevel:int = 0;
      
      public var endLevel:int = 0;
      
      public var per:Number = 1;
      
      public var head:Array = [];
      
      public var coat:Array = [];
      
      public var pants:Array = [];
      
      public var belt:Array = [];
      
      public function EquipRangeDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var arr0:Array = String(xml0.@range).split(",");
         this.startLevel = int(arr0[0]);
         this.minLevel = int(arr0[1]);
         this.maxLevel = int(arr0[2]);
         this.endLevel = int(arr0[3]);
      }
   }
}

