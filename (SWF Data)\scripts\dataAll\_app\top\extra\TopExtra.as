package dataAll._app.top.extra
{
   import com.adobe.crypto.MD5;
   import com.adobe.serialization.json.JSON2;
   
   public class TopExtra
   {
      public var m:String = "";
      
      public function TopExtra()
      {
         super();
      }
      
      public static function getMByUidScore(uid0:String, score0:Number) : String
      {
         var md50:String = uid0 + score0;
         return MD5.hash(md50).substr(0,5);
      }
      
      public function inExtraStr(str0:String) : void
      {
         var obj0:Object = null;
         if(str0 != null && str0 != "")
         {
            try
            {
               obj0 = JSON2.decode(str0);
               this.inData_byObj(obj0);
            }
            catch(e:Error)
            {
            }
         }
      }
      
      public function inData_byObj(obj0:Object) : void
      {
      }
      
      public function setToSimulated() : void
      {
      }
   }
}

