package dataAll._app.wilder
{
   import com.common.text.TextWay;
   
   public class WilderDiff
   {
      public static const nameArr:Array = ["7","6","5","4","3","2","1","0"];
      
      public static const cnArr:Array = ["修罗","地狱","炼狱","神难","超难","困难","普通","简单"];
      
      public static const dropArr:Array = ["有很高几率","有很高几率","有很高几率","有很高几率","有较高几率","有一定几率","有较低几率","有很低几率"];
      
      public static const demonSkillArr:Array = ["weaponSensitive","otherSensitive","flamerSensitive","rocketSensitive","pistolSensitive","sniperSensitive","shotgunSensitive","rifleSensitive","demCloned","weaponNo","offAllSkill","meteoriteRain","lightningFloor","invincibleEmp","moreBullet","deadlyArrow","deadlyGhost","fightBackBullet","screwBall","enemyToSpider","verShield","midLightning","lockLife","cantMove","noPurgoldArms","everSilenceEnemy","blackHoleDemon"];
      
      public function WilderDiff()
      {
         super();
      }
      
      public static function getCnByName(name0:String) : String
      {
         var f0:int = int(nameArr.indexOf(name0));
         if(f0 >= 0)
         {
            return cnArr[f0];
         }
         return "";
      }
      
      public static function getDropByName(name0:String) : String
      {
         var f0:int = int(nameArr.indexOf(name0));
         if(f0 >= 0)
         {
            return dropArr[f0];
         }
         return "";
      }
      
      public static function getProNameByValue(v0:Number) : String
      {
         if(v0 >= 1)
         {
            return "必然";
         }
         return TextWay.numberToPer(v0 + 0.02,0) + "的几率";
      }
      
      public static function getEnemyLv(diff0:String) : int
      {
         var lv0:int = 99;
         if(diff0 >= "6")
         {
            lv0 = 99;
         }
         else if(diff0 == "5")
         {
            lv0 = 95;
         }
         else if(diff0 == "3" || diff0 == "4")
         {
            lv0 = 90;
         }
         else if(diff0 == "2")
         {
            lv0 = 82;
         }
         else if(diff0 == "1")
         {
            lv0 = 70;
         }
         else
         {
            lv0 = 50;
         }
         return lv0;
      }
      
      public static function getExtraLifeMul(diff0:String) : Number
      {
         if(diff0 == "7")
         {
            return 40;
         }
         if(diff0 == "6")
         {
            return 5;
         }
         if(diff0 == "5")
         {
            return 3;
         }
         if(diff0 == "4")
         {
            return 3;
         }
         return 1;
      }
      
      public static function getExtraDpsMul(diff0:String) : Number
      {
         if(diff0 == "7")
         {
            return 8;
         }
         if(diff0 == "6")
         {
            return 5;
         }
         if(diff0 == "5")
         {
            return 3;
         }
         if(diff0 == "4")
         {
            return 3;
         }
         return 1;
      }
   }
}

