package dataAll._app.top.define
{
   public class TopBarDefineGroup
   {
      public var gather:String = "";
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var id:Number = 0;
      
      public var objType:String = "";
      
      public var uploadType:String = "";
      
      public var scoreMul:Number = 1;
      
      public var replaceLabel:String = "";
      
      public var maxLevel:int = 999;
      
      public var hideB:Boolean = false;
      
      public var info:String = "";
      
      public var arr:Array = [];
      
      public function TopBarDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML, gather0:String) : void
      {
         var i:* = undefined;
         var barXML0:XML = null;
         var d0:TopBarDefine = null;
         this.gather = gather0;
         var xml_list0:XMLList = xml0.bar;
         for(i in xml_list0)
         {
            barXML0 = xml_list0[i];
            d0 = new TopBarDefine();
            d0.inData_byXML(barXML0);
            this.arr.push(d0);
         }
         this.name = xml0.@name;
         this.cnName = xml0.@cnName;
         this.id = xml0.@id;
         this.objType = xml0.@objType;
         this.replaceLabel = xml0.@replaceLabel;
         this.uploadType = xml0.@uploadType;
         this.hideB = Boolean(int(String(xml0.@hideB)));
         this.info = xml0.@info;
         if(this.uploadType == "")
         {
            this.uploadType = this.name;
         }
         if(String(xml0.@maxLevel) != "")
         {
            this.maxLevel = int(xml0.@maxLevel);
         }
         if(String(xml0.@scoreMul) != "")
         {
            this.scoreMul = Number(xml0.@scoreMul);
         }
      }
   }
}

